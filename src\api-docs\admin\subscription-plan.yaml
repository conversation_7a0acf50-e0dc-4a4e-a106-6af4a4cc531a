openapi: 3.0.0
info:
  title: WTD Platform Subscription Plans API
  version: 1.0.0
  description: API endpoints for managing subscription plans
  
paths:
  /admin/subscription-plans/seed:
    post:
      tags:
        - Admin Subscription Plans
      summary: Seed subscription plans
      description: Creates default subscription plans if they don't exist
      security:
        - BearerAuth: []
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPlansSeeded'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/subscription-plans:
    get:
      tags:
        - Admin Subscription Plans
      summary: Get all subscription plans
      description: Retrieve all subscription plans with pagination and filtering
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SubscriptionPlanSearchParam'
        - $ref: '#/components/parameters/SubscriptionPlanTargetUserTypeParam'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPlansList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      tags:
        - Admin Subscription Plans
      summary: Create a new subscription plan
      description: Create a new subscription plan with the provided details
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionPlanInput'
      responses:
        '201':
          $ref: '#/components/responses/SubscriptionPlanCreated'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '409':
          $ref: '#/components/responses/SubscriptionPlanConflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/subscription-plans/{id}:
    get:
      tags:
        - Admin Subscription Plans
      summary: Get subscription plan by ID
      description: Retrieve a specific subscription plan by its ID
      security:
        - BearerAuth: []
      parameters:
      - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPlanDetail'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      tags:
        - Admin Subscription Plans
      summary: Update subscription plan
      description: Update an existing subscription plan
      security:
        - BearerAuth: []
      parameters:
      - $ref: '#/components/parameters/IdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionPlanUpdateInput'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPlanUpdated'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '409':
          $ref: '#/components/responses/SubscriptionPlanConflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - Admin Subscription Plans
      summary: Delete subscription plan
      description: Delete a subscription plan and archive its Stripe product
      security:
        - BearerAuth: []
      parameters:
      - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPlanDeleted'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  parameters:
    PageParam:
      in: query
      name: page
      schema:
        type: integer
        minimum: 1
      description: Page number for pagination

    LimitParam:
      in: query
      name: limit
      schema:
        type: integer
        minimum: 1
        maximum: 100
      description: Number of items per page

    SubscriptionPlanSearchParam:
      in: query
      name: search
      schema:
        type: string
      description: Search term for plan name or description

    SubscriptionPlanTargetUserTypeParam:
      in: query
      name: targetUserType
      schema:
        type: string
        enum: [EDUCATOR, PROVIDER]
      description: Filter by target user type

    IdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Subscription plan ID

  responses:
    SubscriptionPlansSeeded:
      description: Subscription plans seeded successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SubscriptionPlan'

    SubscriptionPlansList:
      description: List of subscription plans retrieved successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/PaginatedResponse'
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/SubscriptionPlan'

    SubscriptionPlanCreated:
      description: Subscription plan created successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubscriptionPlan'

    SubscriptionPlanDetail:
      description: Subscription plan retrieved successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubscriptionPlan'

    SubscriptionPlanUpdated:
      description: Subscription plan updated successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubscriptionPlan'
                  diff:
                    $ref: '#/components/schemas/DiffResponse'

    SubscriptionPlanDeleted:
      description: Subscription plan deleted successfully
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SuccessResponse'

    SubscriptionPlanConflict:
      description: Subscription plan with this slug already exists
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    UnauthorizedError:
      description: Unauthorized access
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    BadRequestError:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  schemas:
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: 'Operation completed successfully'

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: 'An error occurred'
        code:
          type: string
          example: 'ERROR_CODE'

    Uuid:
      type: string
      format: uuid
      example: '123e4567-e89b-12d3-a456-************'

    DateTime:
      type: string
      format: date-time
      example: '2024-03-20T10:30:00Z'

    PaginatedResponse:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
        total:
          type: integer
          example: 100
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 10
        totalPages:
          type: integer
          example: 10

    DiffResponse:
      type: object
      properties:
        before:
          type: object
        after:
          type: object

    SubscriptionPlan:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Uuid'
        name:
          type: string
          minLength: 3
          example: 'Educator Free'
        description:
          type: string
          minLength: 10
          example: 'Free forever. View 12 insights per month, no experience registration.'
        stripeProductId:
          type: string
          nullable: true
          example: 'prod_1234567890'
        stripePriceId:
          type: string
          nullable: true
          example: 'price_1234567890'
        price:
          type: number
          example: 0
        priceCents:
          type: integer
          minimum: 0
          example: 0
        billingInterval:
          type: string
          enum: [ONE_TIME, MONTHLY, YEARLY]
          example: ONE_TIME
        targetUserType:
          type: string
          enum: [EDUCATOR, PROVIDER]
          example: EDUCATOR
        trialDays:
          type: integer
          minimum: 0
          nullable: true
          example: null
        canRegisterExperiences:
          type: boolean
          example: false
        insightsLimit:
          type: integer
          minimum: 0
          nullable: true
          example: 12
        insightsLimitPeriod:
          type: string
          enum: [MONTHLY, YEARLY]
          nullable: true
          example: MONTHLY
        isActive:
          type: boolean
          example: true
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    SubscriptionPlanInput:
      type: object
      required:
        - name
        - description
        - priceCents
        - billingInterval
        - targetUserType
        - canRegisterExperiences
        - isActive
      properties:
        name:
          type: string
          minLength: 3
          example: 'Educator Free'
        description:
          type: string
          minLength: 10
          example: 'Free forever. View 12 insights per month, no experience registration.'
        priceCents:
          type: integer
          minimum: 0
          example: 0
        billingInterval:
          type: string
          enum: [ONE_TIME, MONTHLY, YEARLY]
          example: ONE_TIME
        targetUserType:
          type: string
          enum: [EDUCATOR, PROVIDER]
          example: EDUCATOR
        trialDays:
          type: integer
          minimum: 0
          nullable: true
          example: null
        canRegisterExperiences:
          type: boolean
          example: false
        insightsLimit:
          type: integer
          minimum: 0
          nullable: true
          example: 12
        insightsLimitPeriod:
          type: string
          enum: [MONTHLY, YEARLY]
          nullable: true
          example: MONTHLY
        isActive:
          type: boolean
          example: true

    SubscriptionPlanUpdateInput:
      type: object
      properties:
        name:
          type: string
          minLength: 3
          example: 'Educator Free'
        description:
          type: string
          minLength: 10
          example: 'Free forever. View 12 insights per month, no experience registration.'
        priceCents:
          type: integer
          minimum: 0
          example: 0
        billingInterval:
          type: string
          enum: [ONE_TIME, MONTHLY, YEARLY]
          example: ONE_TIME
        targetUserType:
          type: string
          enum: [EDUCATOR, PROVIDER]
          example: EDUCATOR
        trialDays:
          type: integer
          minimum: 0
          nullable: true
          example: null
        canRegisterExperiences:
          type: boolean
          example: false
        insightsLimit:
          type: integer
          minimum: 0
          nullable: true
          example: 12
        insightsLimitPeriod:
          type: string
          enum: [MONTHLY, YEARLY]
          nullable: true
          example: MONTHLY
        isActive:
          type: boolean
          example: true 