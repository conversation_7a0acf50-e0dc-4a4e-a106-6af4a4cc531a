/**
 * InsightView Model
 * Represents when a user views an insight
 */
const { Model, DataTypes } = require('sequelize');

class InsightView extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        viewedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
        },
      },
      {
        sequelize,
        modelName: 'InsightView',
        tableName: 'InsightView',
        timestamps: true,
        indexes: [
          {
            fields: ['userId', 'viewedAt'],
          },
          {
            fields: ['insightId'],
          },
        ],
      }
    );
  }

  static associate(models) {
    // Define associations here
    InsightView.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    InsightView.belongsTo(models.Insight, {
      foreignKey: 'insightId',
      as: 'insight',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = InsightView;
