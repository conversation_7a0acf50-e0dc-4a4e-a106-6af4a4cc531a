/* eslint-disable camelcase */
/**
 * Admin Subscription Plan Service
 *
 * Handles subscription plan business logic
 */
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const {
  SUBSCRIPTION_PLAN: SUBSCRIPTION_PLAN_MESSAGES,
} = require('@utils/messages.utils');
const subscriptionPlanRepository = require('@models/repositories/subscription-plan.repository');

/**
 * Subscription Plan service
 */
const subscriptionPlanService = {
  /**
   * Get all subscription plans
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search term
   * @param {string} options.targetUserType - Filter by target user type (EDUCATOR or PROVIDER)
   * @returns {Promise<Object>} Paginated plans
   */
  getAllPlans: async (options) => {
    const plans = await subscriptionPlanRepository.findAll(options);

    if (!plans) {
      throw new ApiException(
        HttpStatus.NOT_FOUND,
        SUBSCRIPTION_PLAN_MESSAGES.FIND_ERROR
      );
    }
    return plans;
  },

  /**
   * Get subscription plan by ID
   * @param {string} id - Plan ID
   * @returns {Promise<Object>} Plan details
   */
  getPlanById: async (id) => {
    const plan = await subscriptionPlanRepository.findById(id);
    if (!plan) {
      throw new ApiException(
        HttpStatus.NOT_FOUND,
        SUBSCRIPTION_PLAN_MESSAGES.NOT_FOUND
      );
    }
    return plan;
  },
};

module.exports = subscriptionPlanService;
