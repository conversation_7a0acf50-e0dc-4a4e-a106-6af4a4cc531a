/**
 * User Insight Validation
 *
 * Validation schemas for insight-related endpoints
 */
const { body, param, query } = require('express-validator');
const { VALIDATION } = require('@utils/messages.utils');

/**
 * Validation schemas for insight endpoints
 */
const insightValidation = {
  /**
   * Validate create insight request
   */
  create: [
    body('insightText')
      .isString()
      .notEmpty()
      .withMessage(VALIDATION.REQUIRED)
      .isLength({ max: 250 })
      .withMessage('Insight text must be at most 250 characters'),
    body('sourceUrl')
      .optional()
      .isString()
      .withMessage('Source URL must be a string'),
    body('pdCategoryId').isUUID(4).withMessage('Invalid PD category ID'),
    body('focusIds')
      .optional()
      .isArray()
      .withMessage('Focus IDs must be an array'),
    body('focusIds.*').optional().isUUID(4).withMessage('Invalid focus ID'),
    body('wtdCategoryIds')
      .optional()
      .isArray()
      .withMessage('WTD category IDs must be an array'),
    body('wtdCategoryIds.*')
      .optional()
      .isUUID(4)
      .withMessage('Invalid WTD category ID'),
  ],

  /**
   * Validate get all insights request
   */
  getAll: [
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
    query('userId')
      .optional()
      .isUUID(4)
      .withMessage('User ID must be a valid UUID'),
    query('focusIds')
      .optional()
      .isArray()
      .withMessage('Focus IDs must be an array of UUIDs'),
    query('focusIds.*')
      .optional()
      .isUUID(4)
      .withMessage('Each focus ID must be a valid UUID'),
    query('pdCategoryIds')
      .optional()
      .isArray()
      .withMessage('PD category IDs must be an array of UUIDs'),
    query('pdCategoryIds.*')
      .optional()
      .isUUID(4)
      .withMessage('Each PD category ID must be a valid UUID'),
    query('wtdCategoryIds')
      .optional()
      .isArray()
      .withMessage('WTD category IDs must be an array of UUIDs'),
    query('wtdCategoryIds.*')
      .optional()
      .isUUID(4)
      .withMessage('Each WTD category ID must be a valid UUID'),
  ],

  /**
   * Validate get insight by ID request
   */
  getById: [param('id').isUUID(4).withMessage('Invalid insight ID')],

  /**
   * Validate get bookmarked insights request
   */
  getBookmarkedInsights: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 10000 })
      .withMessage('Limit must be between 1 and 100'),
  ],

  /**
   * Validate toggle bookmark request
   */
  toggleBookmark: [
    param('insightId').isUUID(4).withMessage('Insight ID must be a valid UUID'),
  ],

  /**
   * Validate toggle like request
   */
  toggleLike: [
    param('insightId').isUUID(4).withMessage('Insight ID must be a valid UUID'),
  ],

  /**
   * Validate toggle implement request
   */
  toggleImplement: [
    param('insightId').isUUID(4).withMessage('Insight ID must be a valid UUID'),
  ],

  /**
   * Validate get implemented insights request
   */
  getImplementedInsights: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
  ],

  /**
   * Validate add contribution request
   */
  addContribution: [
    param('insightId').isUUID(4).withMessage('Insight ID must be a valid UUID'),
    body('content')
      .notEmpty()
      .withMessage('Contribution content is required')
      .isLength({ min: 1, max: 1000 })
      .withMessage('Contribution content must be between 1 and 1000 characters')
      .trim(),
  ],

  /**
   * Validate get contributions by insight request
   */
  getContributionsByInsight: [
    param('insightId').isUUID(4).withMessage('Insight ID must be a valid UUID'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('sortBy')
      .optional()
      .isIn(['createdAt', 'updatedAt'])
      .withMessage('Sort by must be either createdAt or updatedAt'),
    query('sortOrder')
      .optional()
      .isIn(['ASC', 'DESC', 'asc', 'desc'])
      .withMessage('Sort order must be ASC or DESC'),
  ],

  /**
   * Validate toggle contribution like request
   */
  toggleContributionLike: [
    param('contributionId')
      .isUUID(4)
      .withMessage('Contribution ID must be a valid UUID'),
  ],

  /**
   * Validate report insight request
   */
  reportInsight: [
    param('insightId').isUUID(4).withMessage('Insight ID must be a valid UUID'),
    body('reason')
      .optional()
      .isString()
      .withMessage('Reason must be a string')
      .isLength({ max: 1000 })
      .withMessage('Reason must be at most 1000 characters')
      .trim(),
  ],

  /**
   * Validate report contribution request
   */
  reportContribution: [
    param('contributionId')
      .isUUID(4)
      .withMessage('Contribution ID must be a valid UUID'),
    body('reason')
      .optional()
      .isString()
      .withMessage('Reason must be a string')
      .isLength({ max: 1000 })
      .withMessage('Reason must be at most 1000 characters')
      .trim(),
  ],

  /**
   * Validate delete contribution request
   */
  deleteContribution: [
    param('contributionId')
      .isUUID(4)
      .withMessage('Contribution ID must be a valid UUID'),
  ],
};

module.exports = insightValidation;
