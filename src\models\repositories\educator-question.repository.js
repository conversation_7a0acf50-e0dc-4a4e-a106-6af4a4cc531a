/**
 * EducatorQuestion Repository (Refactored)
 *
 * Handles data access operations for the EducatorQuestion model
 * Structure inspired by insight.repository.js
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { Sequelize } = require('sequelize');
const { Op } = Sequelize;
const { v4: uuidv4 } = require('uuid');
const { EDUCATOR_QUESTION } = require('@utils/messages.utils');
const { EducatorQuestionStatus } = require('@utils/enums.utils');

// --- Base Repository ---
class BaseRepository {
  constructor() {
    this.models = {
      EducatorQuestion: databaseService.getEducatorQuestionModel(),
      User: databaseService.getUserModel(),
      Focus: databaseService.getFocusModel(),
      WtdCategory: databaseService.getWtdCategoryModel(),
      BookmarkedEducatorQuestion:
        databaseService.getBookmarkedEducatorQuestionModel(),
      LikedEducatorQuestion: databaseService.getLikedEducatorQuestionModel(),
      EducatorQuestionFocus: databaseService.getEducatorQuestionFocusModel(),
      EducatorQuestionWtdCategory:
        databaseService.getEducatorQuestionWtdCategoryModel(),
      ImplementedEducatorQuestion:
        databaseService.getImplementedEducatorQuestionModel(),
    };
  }

  _getEducatorQuestionAttributes() {
    return {
      exclude: [],
      include: [
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "LikedEducatorQuestion" WHERE "LikedEducatorQuestion"."educatorQuestionId" = "EducatorQuestion"."id")`
            ),
          'likesCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "BookmarkedEducatorQuestion" WHERE "BookmarkedEducatorQuestion"."educatorQuestionId" = "EducatorQuestion"."id")`
            ),
          'bookmarksCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "ImplementedEducatorQuestion" WHERE "ImplementedEducatorQuestion"."educatorQuestionId" = "EducatorQuestion"."id")`
            ),
          'implementsCount',
        ],
      ],
    };
  }

  _getEducatorQuestionIncludes() {
    return [
      {
        model: this.models.User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'profilePic', 'userType'],
      },
      {
        model: this.models.Focus,
        as: 'focus',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        model: this.models.WtdCategory,
        as: 'wtdCategories',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
    ];
  }
}

// --- Search Repository ---
class EducatorQuestionSearchRepository extends BaseRepository {
  async buildWhereClause({
    search,
    focusIds,
    wtdCategoryIds,
    queryUserId,
    status,
  }) {
    const whereClause = {};
    if (status) whereClause.status = status;
    if (queryUserId) whereClause.createdBy = queryUserId;
    if (search) {
      whereClause.text = { [Op.iLike]: `%${search}%` };
    }
    // Filter by focusIds
    if (focusIds?.length > 0) {
      const questionIdsWithFocus =
        await this.models.EducatorQuestionFocus.findAll({
          attributes: ['educatorQuestionId'],
          where: { focusId: { [Op.in]: focusIds } },
          raw: true,
        });
      const ids = questionIdsWithFocus.map((item) => item.educatorQuestionId);
      if (ids.length > 0) {
        whereClause.id = { [Op.in]: ids };
      } else {
        whereClause.id = null;
      }
    }
    // Filter by wtdCategoryIds
    if (wtdCategoryIds?.length > 0) {
      const questionIdsWithWtd =
        await this.models.EducatorQuestionWtdCategory.findAll({
          attributes: ['educatorQuestionId'],
          where: { wtdCategoryId: { [Op.in]: wtdCategoryIds } },
          raw: true,
        });
      const ids = questionIdsWithWtd.map((item) => item.educatorQuestionId);
      if (ids.length > 0) {
        if (whereClause.id) {
          const existingIds = whereClause.id[Op.in];
          const intersection = existingIds.filter((id) => ids.includes(id));
          whereClause.id = {
            [Op.in]: intersection.length > 0 ? intersection : [null],
          };
        } else {
          whereClause.id = { [Op.in]: ids };
        }
      } else {
        whereClause.id = null;
      }
    }
    return whereClause;
  }
}

// --- Helper ---
class EducatorQuestionHelper extends BaseRepository {
  async enrichEducatorQuestionsWithUserData(questions, userId) {
    if (!userId) return questions;
    const questionIds = questions.map((q) => q.id);
    const [bookmarks, likes, implementations] = await Promise.all([
      this.models.BookmarkedEducatorQuestion.findAll({
        where: { userId, educatorQuestionId: { [Op.in]: questionIds } },
        raw: true,
      }),
      this.models.LikedEducatorQuestion.findAll({
        where: { userId, educatorQuestionId: { [Op.in]: questionIds } },
        raw: true,
      }),
      this.models.ImplementedEducatorQuestion.findAll({
        where: { userId, educatorQuestionId: { [Op.in]: questionIds } },
        raw: true,
      }),
    ]);
    const bookmarkedIds = new Set(bookmarks.map((b) => b.educatorQuestionId));
    const likedIds = new Set(likes.map((l) => l.educatorQuestionId));
    const implementedIds = new Set(
      implementations.map((i) => i.educatorQuestionId)
    );
    return questions.map((q) => {
      const data = q.toJSON ? q.toJSON() : q;
      data.isBookmarked = bookmarkedIds.has(q.id);
      data.isLiked = likedIds.has(q.id);
      data.isImplemented = implementedIds.has(q.id);
      return data;
    });
  }

  async enrichEducatorQuestionWithUserData(question, userId) {
    if (!userId) return question;
    const [bookmark, like, implementation] = await Promise.all([
      this.models.BookmarkedEducatorQuestion.findOne({
        where: { userId, educatorQuestionId: question.id },
      }),
      this.models.LikedEducatorQuestion.findOne({
        where: { userId, educatorQuestionId: question.id },
      }),
      this.models.ImplementedEducatorQuestion.findOne({
        where: { userId, educatorQuestionId: question.id },
      }),
    ]);
    const data = question.toJSON ? question.toJSON() : question;
    data.isBookmarked = !!bookmark;
    data.isLiked = !!like;
    data.isImplemented = !!implementation;
    return data;
  }

  async createFocusAssociations(questionId, focusIds, transaction) {
    if (!focusIds?.length) return;
    const focusEntries = focusIds.map((focusId) => ({
      id: uuidv4(),
      educatorQuestionId: questionId,
      focusId,
    }));
    await this.models.EducatorQuestionFocus.bulkCreate(focusEntries, {
      transaction,
    });
  }

  async createWtdCategoryAssociations(questionId, wtdCategoryIds, transaction) {
    if (!wtdCategoryIds?.length) return;
    const wtdEntries = wtdCategoryIds.map((wtdCategoryId) => ({
      id: uuidv4(),
      educatorQuestionId: questionId,
      wtdCategoryId,
    }));
    await this.models.EducatorQuestionWtdCategory.bulkCreate(wtdEntries, {
      transaction,
    });
  }

  async updateFocusAssociations(questionId, focusIds, transaction) {
    await this.models.EducatorQuestionFocus.destroy({
      where: { educatorQuestionId: questionId },
      transaction,
    });
    if (focusIds?.length > 0) {
      await this.createFocusAssociations(questionId, focusIds, transaction);
    }
  }

  async updateWtdCategoryAssociations(questionId, wtdCategoryIds, transaction) {
    await this.models.EducatorQuestionWtdCategory.destroy({
      where: { educatorQuestionId: questionId },
      transaction,
    });
    if (wtdCategoryIds?.length > 0) {
      await this.createWtdCategoryAssociations(
        questionId,
        wtdCategoryIds,
        transaction
      );
    }
  }
}

// --- Main Repository ---
class EducatorQuestionRepository extends BaseRepository {
  constructor() {
    super();
    this.searchRepo = new EducatorQuestionSearchRepository();
    this.helper = new EducatorQuestionHelper();
  }

  async create(data, loggedInUser) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      const question = await this.models.EducatorQuestion.create(
        {
          text: data.text,
          sourceUrl: data.sourceUrl,
          pdCategoryId: data.pdCategoryId,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
      if (data.focusIds?.length) {
        await this.helper.createFocusAssociations(
          question.id,
          data.focusIds,
          transaction
        );
      }
      if (data.wtdCategoryIds?.length) {
        await this.helper.createWtdCategoryAssociations(
          question.id,
          data.wtdCategoryIds,
          transaction
        );
      }
      await transaction.commit();
      return await this.findById(question.id, loggedInUser.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async findAll(data, loggedInUser) {
    const {
      search = '',
      focusIds = [],
      wtdCategoryIds = [],
      queryUserId = null,
      status = EducatorQuestionStatus.APPROVED,
    } = data || {};
    try {
      const where = await this.searchRepo.buildWhereClause({
        search,
        focusIds,
        wtdCategoryIds,
        queryUserId,
        status,
      });
      const questions = await this.models.EducatorQuestion.findAll({
        where,
        order: [['createdAt', 'DESC']],
        attributes: this._getEducatorQuestionAttributes(),
        include: this._getEducatorQuestionIncludes(),
        subQuery: false,
      });
      const enriched = await this.helper.enrichEducatorQuestionsWithUserData(
        questions,
        loggedInUser?.id
      );
      return { questions: enriched };
    } catch (error) {
      throw error;
    }
  }

  async findById(questionId, loggedInUser = null) {
    try {
      const question = await this.models.EducatorQuestion.findOne({
        where: { id: questionId },
        attributes: this._getEducatorQuestionAttributes(),
        include: this._getEducatorQuestionIncludes(),
      });
      if (!question) {
        throw new ApiException(
          ApiException.NOT_FOUND,
          EDUCATOR_QUESTION.NOT_FOUND
        );
      }
      return await this.helper.enrichEducatorQuestionWithUserData(
        question,
        loggedInUser?.id
      );
    } catch (error) {
      throw error;
    }
  }

  async update(questionId, data, loggedInUser) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      const question = await this.models.EducatorQuestion.findOne({
        where: { id: questionId },
      });
      if (!question) {
        throw new ApiException(
          ApiException.NOT_FOUND,
          EDUCATOR_QUESTION.NOT_FOUND
        );
      }
      Object.assign(question, data);
      await question.save({ transaction });
      if (data.focusIds) {
        await this.helper.updateFocusAssociations(
          question.id,
          data.focusIds,
          transaction
        );
      }
      if (data.wtdCategoryIds) {
        await this.helper.updateWtdCategoryAssociations(
          question.id,
          data.wtdCategoryIds,
          transaction
        );
      }
      await transaction.commit();
      return await this.findById(question.id, loggedInUser?.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async delete(questionId) {
    try {
      const question = await this.models.EducatorQuestion.findOne({
        where: { id: questionId },
      });
      if (!question) {
        throw new ApiException(
          ApiException.NOT_FOUND,
          EDUCATOR_QUESTION.NOT_FOUND
        );
      }
      await question.destroy();
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Toggle like status for an educator question
   * @param {string} userId - User UUID
   * @param {string} educatorQuestionId - EducatorQuestion UUID
   * @returns {Promise<Object>} Object with isLiked status
   */
  async toggleLike(userId, educatorQuestionId) {
    try {
      const LikedEducatorQuestion = this.models.LikedEducatorQuestion;
      // Check if already liked
      const existingLike = await LikedEducatorQuestion.findOne({
        where: { userId, educatorQuestionId },
      });
      if (existingLike) {
        await existingLike.destroy();
        return { isLiked: false };
      }
      await LikedEducatorQuestion.create({ userId, educatorQuestionId });
      return { isLiked: true };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Toggle implement status for an educator question
   * @param {string} userId - User UUID
   * @param {string} educatorQuestionId - EducatorQuestion UUID
   * @returns {Promise<Object>} Object with isImplemented status
   */
  async toggleImplement(userId, educatorQuestionId) {
    try {
      const ImplementedEducatorQuestion =
        this.models.ImplementedEducatorQuestion;
      // Check if already implemented
      const existing = await ImplementedEducatorQuestion.findOne({
        where: { userId, educatorQuestionId },
      });
      if (existing) {
        await existing.destroy();
        return { isImplemented: false };
      }
      await ImplementedEducatorQuestion.create({ userId, educatorQuestionId });
      return { isImplemented: true };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Toggle bookmark status for an educator question
   * @param {string} userId - User UUID
   * @param {string} educatorQuestionId - EducatorQuestion UUID
   * @returns {Promise<Object>} Object with isBookmarked status
   */
  async toggleBookmark(userId, educatorQuestionId) {
    try {
      const BookmarkedEducatorQuestion = this.models.BookmarkedEducatorQuestion;
      // Check if already bookmarked
      const existingBookmark = await BookmarkedEducatorQuestion.findOne({
        where: { userId, educatorQuestionId },
      });
      if (existingBookmark) {
        await existingBookmark.destroy();
        return { isBookmarked: false };
      }
      await BookmarkedEducatorQuestion.create({ userId, educatorQuestionId });
      return { isBookmarked: true };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all bookmarked educator questions for a user
   * @param {string} userId - User UUID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Object with bookmarked questions and pagination
   */
  async getBookmarkedEducatorQuestions(userId, { page = 1, limit = 10 }) {
    try {
      // Get total count of bookmarked questions
      const totalCount = await this.models.BookmarkedEducatorQuestion.count({
        where: { userId },
      });

      if (totalCount === 0) {
        return {
          questions: [],
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPrevPage: false,
          },
        };
      }

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(totalCount / limit);

      // Get bookmarked question IDs with pagination
      const bookmarks = await this.models.BookmarkedEducatorQuestion.findAll({
        where: { userId },
        attributes: ['educatorQuestionId'],
        order: [['createdAt', 'DESC']],
        limit,
        offset,
        raw: true,
      });

      const questionIds = bookmarks.map((b) => b.educatorQuestionId);

      const bookmarkedQuestions = await this.models.EducatorQuestion.findAll({
        where: {
          id: { [Op.in]: questionIds },
        },
        include: this._getEducatorQuestionIncludes(),
        attributes: this._getEducatorQuestionAttributes(),
        order: [['createdAt', 'DESC']],
        subQuery: false,
      });

      const enriched = await this.helper.enrichEducatorQuestionsWithUserData(
        bookmarkedQuestions,
        userId
      );

      return {
        questions: enriched,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new EducatorQuestionRepository();
