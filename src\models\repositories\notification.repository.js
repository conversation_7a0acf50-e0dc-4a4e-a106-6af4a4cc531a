const databaseService = require('@config/database.config');
const commonRepository = require('./common.repository');
const { PAGINATION } = require('@utils/constants');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { NOTIFICATION } = require('@utils/messages.utils');

class NotificationRepository {
  constructor() {
    this.Notification = databaseService.getNotificationModel();
  }

  /**
   * Create a new notification
   * @param {Object} data - Notification data
   * @returns {Promise<Object>} Created notification
   */
  async createNotification(data) {
    try {
      return await this.Notification.create(data);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get notifications for a user (receiverUserId) with pagination and pagination info
   * @param {string} userId - The receiver's userId
   * @param {number} page
   * @param {number} limit
   * @returns {Promise<{data: Array, pagination: Object}>}
   */
  async getUserNotificationsWithPagination(
    userId,
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT
  ) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);
      const where = { receiverUserId: userId };
      const notifications = await this.Notification.findAll({
        where,
        order: [['createdAt', 'DESC']],
        limit,
        offset,
      });
      const count = await this.Notification.count({ where });
      return {
        data: notifications,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find a single notification by criteria
   * @param {Object} criteria - Sequelize where clause
   * @returns {Promise<Object|null>} The notification or null
   */
  async findNotification(criteria) {
    try {
      return await this.Notification.findOne({ where: criteria });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check and mark a notification as read in a single operation
   * @param {string} notificationId - The notification ID
   * @param {string} userId - The user ID to verify ownership
   * @returns {Promise<Object>} The updated notification
   * @throws {ApiException} If notification not found or user not authorized
   */
  async checkAndReadNotification(notificationId, userId) {
    try {
      const notification = await this.Notification.findOne({
        where: {
          id: notificationId,
          receiverUserId: userId,
        },
      });

      if (!notification) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          NOTIFICATION.NOTIFICATION_NOT_FOUND
        );
      }

      await notification.update({ isRead: true });
      return notification;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new NotificationRepository();
