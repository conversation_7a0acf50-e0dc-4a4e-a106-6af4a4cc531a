const notificationRepository = require('@repositories/notification.repository');
const {
  sendPushNotification,
} = require('@integrations/firebase/notification.service');
const userRepository = require('@repositories/user.repository');
const { NotificationType } = require('@utils/enums.utils');
const { EDUCATOR_QUESTION_NOTIFICATION } = require('@utils/messages.utils');

/**
 * Notify the creator of an educator question when it is approved by admin
 * @param {Object} question - The educator question object
 */
async function notifyEducatorQuestionApproved(question) {
  const provider = question.creator;
  try {
    if (provider && provider.deviceToken) {
      const notificationText = `Congratulations! Your question is live.`;
      const commonNotificationPayload = {
        userId: null,
        receiverUserId: provider.id,
        type: NotificationType.EDUCATOR_QUESTION_APPROVED,
        title: EDUCATOR_QUESTION_NOTIFICATION.QUESTION_APPROVED_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { questionId: question.id },
      });
      await sendPushNotification({
        deviceToken: provider.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          questionId: question.id,
          redirectUrl: `/questions?notification=true`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

module.exports = {
  notifyEducatorQuestionApproved,
  // TODO: Add more notification functions as needed (contribution, like, etc.)
};
