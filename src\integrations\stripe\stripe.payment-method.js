const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripePaymentMethod {
  /**
   * Create a setup intent for securely collecting payment method details
   * @param {string} customerId - Stripe customer ID
   * @returns {Promise<Object>} Setup intent with client secret
   */
  async createSetupIntent(customerId) {
    try {
      const setupIntent = await stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
      });
      return setupIntent;
    } catch (error) {
      console.error('[Stripe] Failed to create setup intent:', error);
      throw error;
    }
  }

  /**
   * Attach a payment method to a customer
   * @param {string} paymentMethodId - Stripe payment method ID
   * @param {string} customerId - Stripe customer ID
   * @returns {Promise<Object>} Attached payment method
   */
  async attachToCustomer(paymentMethodId, customerId) {
    try {
      return await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });
    } catch (error) {
      console.error(
        '[Stripe] Failed to attach payment method to customer:',
        error
      );
      throw error;
    }
  }

  /**
   * Detach and remove a payment method
   * @param {string} paymentMethodId - Payment method ID
   * @returns {Promise<Object>} Detached payment method
   */
  async detachAndRemove(paymentMethodId) {
    try {
      return await stripe.paymentMethods.detach(paymentMethodId);
    } catch (error) {
      console.error('[Stripe] Failed to detach payment method:', error);
      throw error;
    }
  }

  /**
   * Get all payment methods for a customer
   * @param {string} customerId - Customer ID
   * @param {Object} options - List options
   * @returns {Promise<Array>} List of payment methods
   */
  async getAllForCustomer(customerId, options = {}) {
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
        ...options,
      });
      return paymentMethods.data;
    } catch (error) {
      console.error(
        '[Stripe] Failed to fetch customer payment methods:',
        error
      );
      throw error;
    }
  }

  /**
   * Get default payment method for a customer
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object|null>} Default payment method or null
   */
  async getDefaultForCustomer(customerId) {
    try {
      const customer = await stripe.customers.retrieve(customerId, {
        expand: ['default_payment_method'],
      });
      return customer.default_payment_method;
    } catch (error) {
      console.error('[Stripe] Failed to fetch default payment method:', error);
      throw error;
    }
  }

  /**
   * Set a payment method as default and update customer
   * @param {string} customerId - Customer ID
   * @param {string} paymentMethodId - Payment method ID
   * @returns {Promise<Object>} Updated customer with default payment method
   */
  async setAsDefault(customerId, paymentMethodId) {
    try {
      const customer = await stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
      return customer;
    } catch (error) {
      console.error('[Stripe] Failed to set default payment method:', error);
      throw error;
    }
  }

  /**
   * Get payment method details
   * @param {string} paymentMethodId - Payment method ID
   * @returns {Promise<Object>} Payment method details
   */
  async getDetails(paymentMethodId) {
    try {
      return await stripe.paymentMethods.retrieve(paymentMethodId);
    } catch (error) {
      console.error('[Stripe] Failed to fetch payment method details:', error);
      throw error;
    }
  }
}

module.exports = new StripePaymentMethod();
