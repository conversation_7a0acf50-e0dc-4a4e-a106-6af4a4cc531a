/**
 * OnboardingConfig Service
 *
 * Handles onboarding config-related business logic
 */
const onboardingConfigRepository = require('@models/repositories/onboarding-config.repository');
const userRepository = require('@models/repositories/user.repository');

/**
 * OnboardingConfig service
 */
const onboardingConfigService = {
  /**
   * Get onboarding config by user type
   * @param {string} userType - User type
   * @returns {Promise<Object>} Onboarding config
   */
  getConfigByUserType: async (userType) => {
    return await onboardingConfigRepository.findByUserType(userType);
  },
  /**
   * Update currentStep for a user
   * @param {string} userId - User ID
   * @param {number} currentStep - New current step
   * @returns {Promise<Object>} Updated user
   */
  updateCurrentStep: async (userId, currentStep) => {
    return await userRepository.update(userId, { currentStep });
  },
};

module.exports = onboardingConfigService;
