/**
 * Admin User Service
 * Handles business logic for admin user CRUD
 */
const { AdminUserRepository } = require('@models/repositories/user.repository');
const userRepository = require('@models/repositories/user.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { AUTH: AUTH_MESSAGES } = require('@utils/messages.utils');
const StripeCustomer = require('@integrations/stripe/stripe.customer');
const crypto = require('crypto');
const sendGridService = require('@integrations/sendgrid/sendgrid.service');
const { AUTH: AUTH_CONSTANTS } = require('@utils/constants');
const { CreatedVia } = require('@utils/enums.utils');

const userService = {
  /**
   * Create a new user (admin)
   */
  createUser: async (userData) => {
    try {
      // Check if user already exists with this email
      const existingUser = await userRepository.findByEmail(userData.email);
      if (existingUser) {
        throw new ApiException(
          HttpStatus.CONFLICT,
          AUTH_MESSAGES.EMAIL_ALREADY_EXISTS
        );
      }

      // Generate reset token and expiry
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetTokenExpiry = new Date(
        Date.now() + AUTH_CONSTANTS.RESET_TOKEN_EXPIRY_MS
      );
      userData.resetToken = resetToken;
      userData.resetTokenExpiry = resetTokenExpiry;

      // Set createdVia to 'ADMIN' for admin-created users
      userData.createdVia = CreatedVia.ADMIN;

      const user =
        await AdminUserRepository.createUserWithSubscription(userData);

      // Create Stripe customer
      const customer = await StripeCustomer.create(user);

      // Update user with Stripe customer ID
      await userRepository.update(user.id, { stripeCustomerId: customer.id });
      user.stripeCustomerId = customer.id;

      // Send SET_PASSWORD email
      const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
      await sendGridService.sendSetPasswordEmail(user.email, {
        USER_NAME: `${user.firstName} ${user.lastName}`,
        SET_PASSWORD_URL: resetUrl,
      });

      return user;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update an existing user (admin)
   */
  updateUser: async (userId, updateData) => {
    try {
      return AdminUserRepository.updateUser(userId, updateData);
    } catch (error) {
      throw error;
    }
  },

  /**
   * List all users (admin) with pagination and consistent structure
   */
  listUsers: async (query, page, limit) => {
    return await AdminUserRepository.listUsers(query, page, limit);
  },

  /**
   * Retrieve a particular user (admin)
   */
  getUserByUserId: async (userId) => {
    return await AdminUserRepository.getUserById(userId);
  },

  /**
   * Delete a user (admin)
   */
  deleteUser: async (userId) => {
    return AdminUserRepository.deleteUser(userId);
  },
};

module.exports = userService;
