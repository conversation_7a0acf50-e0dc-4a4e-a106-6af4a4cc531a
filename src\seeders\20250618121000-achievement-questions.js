'use strict';
const { UserType, QuestionType } = require('../utils/enums.utils');
const { v4: uuidv4 } = require('uuid');

// Questions for Educator Achievements
const achievementFormsData = {
  'WTD Reflector': [
    {
      text: 'Which insight have you loved lately? (Shout out the provider who inspired this!)',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: true,
    },
    {
      text: 'What inspired you to implement this insight or what were you hoping to achieve?',
      questionType: QuestionType.TEXT,
      order: 2,
      isRequired: true,
    },
    {
      text: 'How did the insight impact you or change your way of thinking?',
      questionType: QuestionType.TEXT,
      order: 3,
      isRequired: true,
    },
    {
      text: 'What adjustments or modifications did you make (if any)?',
      questionType: QuestionType.TEXT,
      order: 4,
      isRequired: true,
    },
    {
      text: 'How do you plan to continue using this?',
      questionType: QuestionType.TEXT,
      order: 5,
      isRequired: true,
    },
    {
      text: "Do we have your permission to share your submission? If yes, we'll only use your first name.",
      questionType: QuestionType.MULTIPLE_CHOICE,
      order: 6,
      isRequired: true,
      options: [
        { label: 'Yes', value: 'true', order: 1 },
        { label: 'No', value: 'false', order: 2 },
      ],
    },
  ],
  'WTD Visionary': [
    {
      text: 'What is a change you are envisioning for your classroom?',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: true,
    },
    {
      text: 'Why is this change important?',
      questionType: QuestionType.TEXT,
      order: 2,
      isRequired: true,
    },
    {
      text: 'What is your first step?',
      questionType: QuestionType.TEXT,
      order: 3,
      isRequired: true,
    },
    {
      text: 'What challenge do you foresee?',
      questionType: QuestionType.TEXT,
      order: 4,
      isRequired: true,
    },
    {
      text: 'What outcome do you hope for?',
      questionType: QuestionType.TEXT,
      order: 5,
      isRequired: true,
    },
    {
      text: "Do we have your permission to share your submission? If yes, we'll only use your first name.",
      questionType: QuestionType.MULTIPLE_CHOICE,
      order: 6,
      isRequired: true,
      options: [
        { label: 'Yes', value: 'true', order: 1 },
        { label: 'No', value: 'false', order: 2 },
      ],
    },
  ],
  'WTD Innovator': [
    {
      text: 'How did your Visionary idea go in practice?',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: true,
    },
    {
      text: 'Was the impact as expected? Why or why not?',
      questionType: QuestionType.TEXT,
      order: 2,
      isRequired: true,
    },
    {
      text: 'What adjustments did you make based on feedback?',
      questionType: QuestionType.TEXT,
      order: 3,
      isRequired: true,
    },
    {
      text: 'What new insights or challenges arose?',
      questionType: QuestionType.TEXT,
      order: 4,
      isRequired: true,
    },
    {
      text: "What's next?",
      questionType: QuestionType.TEXT,
      order: 5,
      isRequired: true,
    },
    {
      text: "Do we have your permission to share your submission? If yes, we'll only use your first name.",
      questionType: QuestionType.MULTIPLE_CHOICE,
      order: 6,
      isRequired: true,
      options: [
        { label: 'Yes', value: 'true', order: 1 },
        { label: 'No', value: 'false', order: 2 },
      ],
    },
  ],
  'WTD Trailblazer': [
    {
      text: 'Describe an initiative you want to implement to improve your school.',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: true,
    },
    {
      text: 'What inspired it?',
      questionType: QuestionType.TEXT,
      order: 2,
      isRequired: true,
    },
    {
      text: 'What impact will it have?',
      questionType: QuestionType.TEXT,
      order: 3,
      isRequired: true,
    },
    {
      text: 'How will you involve others?',
      questionType: QuestionType.TEXT,
      order: 4,
      isRequired: true,
    },
    {
      text: 'What challenges do you anticipate?',
      questionType: QuestionType.TEXT,
      order: 5,
      isRequired: true,
    },
    {
      text: 'How will you sustain the change?',
      questionType: QuestionType.TEXT,
      order: 6,
      isRequired: true,
    },
    {
      text: 'When will you start?',
      questionType: QuestionType.TEXT,
      order: 7,
      isRequired: true,
    },
    {
      text: "Do we have your permission to share your submission? If yes, we'll only use your first name.",
      questionType: QuestionType.MULTIPLE_CHOICE,
      order: 8,
      isRequired: true,
      options: [
        { label: 'Yes', value: 'true', order: 1 },
        { label: 'No', value: 'false', order: 2 },
      ],
    },
  ],
};

// Questions for Provider Achievements
const providerAchievementFormsData = {
  'WTD Trailblazer': [
    {
      text: 'What change have you made in your life/company inspired by the WTD community?',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: true,
    },
    {
      text: 'What impact did it have?',
      questionType: QuestionType.TEXT,
      order: 2,
      isRequired: true,
    },
    {
      text: 'What do you believe the future of education looks like?',
      questionType: QuestionType.TEXT,
      order: 3,
      isRequired: true,
    },
    {
      text: 'How will you continue advocating for that future?',
      questionType: QuestionType.TEXT,
      order: 4,
      isRequired: true,
    },
    {
      text: "Do we have your permission to share your submission? If yes, we'll only use your company name.",
      questionType: QuestionType.MULTIPLE_CHOICE,
      order: 5,
      isRequired: true,
      options: [
        { label: 'Yes', value: 'true', order: 1 },
        { label: 'No', value: 'false', order: 2 },
      ],
    },
  ],
};

// Map achievement names in DB to form keys
const achievementNameToFormKey = {
  'Complete Reflector Form': 'WTD Reflector',
  'Complete Visionary Form': 'WTD Visionary',
  'Complete Innovator Form': 'WTD Innovator',
  'Complete Trailblazer Form': 'WTD Trailblazer',
};

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Get existing questions to avoid duplicates
      const existingQuestions = await queryInterface.sequelize.query(
        `SELECT "question", "achievementId" FROM "AchievementQuestion"`,
        { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
      );
      const existingMap = new Map(
        existingQuestions.map((q) => [`${q.question}-${q.achievementId}`, true])
      );
      // Get achievements
      const achievements = await queryInterface.sequelize.query(
        `SELECT id, name, "userType" FROM "Achievement" WHERE "userType" IN (:types)`,
        {
          replacements: {
            types: [
              UserType.EDUCATOR,
              UserType.EDUCATOR_PLUS,
              UserType.PROVIDER,
              UserType.PROVIDER_PLUS,
            ],
          },
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        }
      );
      // Process each achievement
      for (const achievement of achievements) {
        const formKey = achievementNameToFormKey[achievement.name];
        const questions = achievement.userType.includes('PROVIDER')
          ? providerAchievementFormsData[formKey] || []
          : achievementFormsData[formKey] || [];
        if (!questions.length) {
          continue;
        }
        for (const question of questions) {
          if (existingMap.has(`${question.text}-${achievement.id}`)) {
            continue;
          }
          const questionId = uuidv4();
          await queryInterface.bulkInsert(
            'AchievementQuestion',
            [
              {
                id: questionId,
                achievementId: achievement.id,
                userType: achievement.userType,
                question: question.text,
                questionType: question.questionType,
                order: question.order,
                isRequired: question.isRequired,
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            ],
            { transaction }
          );
          if (
            question.questionType === QuestionType.MULTIPLE_CHOICE &&
            question.options
          ) {
            const options = question.options.map((option, index) => ({
              id: uuidv4(),
              questionId,
              label: option.label,
              value: option.value,
              order: option.order || index + 1,
              isCorrect: null,
              createdAt: new Date(),
              updatedAt: new Date(),
            }));
            await queryInterface.bulkInsert(
              'AchievementQuestionOption',
              options,
              { transaction }
            );
          }
        }
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Get all questions for the achievements we created
      const achievementNames = [
        ...Object.keys(achievementFormsData),
        ...Object.keys(providerAchievementFormsData),
      ];
      const achievements = await queryInterface.sequelize.query(
        'SELECT id FROM "Achievement" WHERE name IN (:names)',
        {
          replacements: { names: achievementNames },
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        }
      );
      const achievementIds = achievements.map((a) => a.id);
      await queryInterface.sequelize.query(
        `DELETE FROM "AchievementQuestionOption" WHERE "questionId" IN 
        (SELECT "id" FROM "AchievementQuestion" WHERE "achievementId" IN (:ids))`,
        { replacements: { ids: achievementIds }, transaction }
      );
      await queryInterface.sequelize.query(
        `DELETE FROM "AchievementQuestion" WHERE "achievementId" IN (:ids)`,
        { replacements: { ids: achievementIds }, transaction }
      );
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
