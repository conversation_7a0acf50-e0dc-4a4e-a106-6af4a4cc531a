'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('AchievementQuestionOption', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      questionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'AchievementQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      value: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      label: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      isCorrect: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
    await queryInterface.addIndex('AchievementQuestionOption', ['questionId'], {
      name: 'achievement_question_option_question_id_idx',
    });
    await queryInterface.addIndex('AchievementQuestionOption', ['order'], {
      name: 'achievement_question_option_order_idx',
    });
    await queryInterface.addIndex('AchievementQuestionOption', ['isCorrect'], {
      name: 'achievement_question_option_is_correct_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'AchievementQuestionOption',
      'achievement_question_option_is_correct_idx'
    );
    await queryInterface.removeIndex(
      'AchievementQuestionOption',
      'achievement_question_option_order_idx'
    );
    await queryInterface.removeIndex(
      'AchievementQuestionOption',
      'achievement_question_option_question_id_idx'
    );
    await queryInterface.dropTable('AchievementQuestionOption');
  },
};
