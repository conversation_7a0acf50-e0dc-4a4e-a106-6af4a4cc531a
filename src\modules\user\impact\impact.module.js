/**
 * User Impact Module
 *
 * This module handles user impact-related functionality
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('@middlewares/auth.middleware');
const { validate } = require('@middlewares/validation.middleware');
const impactController = require('./impact.controller');
const impactValidation = require('./impact.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get user's impact data
  router.get('/', authenticate, impactController.getImpact);

  // Get user's achievements for current milestone
  router.get('/achievements', authenticate, impactController.getAchievements);

  // Get milestone questions with options
  router.get(
    '/milestones/:milestoneId/questions',
    authenticate,
    validate(impactValidation.getMilestoneQuestions),
    impactController.getMilestoneQuestions
  );

  // Get achievement questions with options
  router.get(
    '/achievements/:achievementId/questions',
    authenticate,
    validate(impactValidation.getAchievementQuestions),
    impactController.getAchievementQuestions
  );

  // Move to next milestone
  router.post(
    '/milestones/:milestoneId/move-to-next',
    authenticate,
    validate(impactValidation.moveToNextMilestone),
    impactController.moveToNextMilestone
  );

  // Submit milestone questions
  router.post(
    '/milestones/:milestoneId/questions',
    authenticate,
    validate(impactValidation.submitMilestoneQuestions),
    impactController.submitMilestoneQuestions
  );

  // Submit achievement questions
  router.post(
    '/achievements/:achievementId/questions',
    authenticate,
    validate(impactValidation.submitAchievementQuestions),
    impactController.submitAchievementQuestions
  );

  return router;
}

module.exports = registerRoutes();
