/**
 * Experience Module
 *
 * This module handles experience-related functionality for users
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('@middlewares/auth.middleware');
const { validate } = require('@middlewares/validation.middleware');
const experienceController = require('./experience.controller');
const experienceValidation = require('./experience.validation');
const paginationMiddleware = require('@middlewares/pagination.middleware');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all experiences with pagination and search
  router.get(
    '/',
    authenticate,
    paginationMiddleware,
    validate(experienceValidation.getAll),
    experienceController.getAllExperiences
  );

  // Get experience details by ID
  router.get(
    '/:experienceId',
    authenticate,
    validate(experienceValidation.getById),
    experienceController.getExperienceDetails
  );

  // Update experience
  router.put(
    '/:experienceId',
    authenticate,
    validate(experienceValidation.update),
    experienceController.updateExperience
  );

  router.get(
    '/week/:weekId',
    authenticate,
    paginationMiddleware,
    validate(experienceValidation.getWeekDetails),
    experienceController.getWeekDetails
  );

  // Get experience discussions
  router.get(
    '/:experienceId/discussions',
    authenticate,
    paginationMiddleware,
    validate(experienceValidation.getDiscussions),
    experienceController.getExperienceDiscussions
  );

  // Get experience reviews
  router.get(
    '/:experienceId/reviews',
    authenticate,
    paginationMiddleware,
    validate(experienceValidation.getReviews),
    experienceController.getExperienceReviews
  );

  // Update experience status
  router.patch(
    '/:experienceId/status',
    authenticate,
    validate(experienceValidation.updateStatus),
    experienceController.updateExperienceStatus
  );

  // Delete experience
  router.delete(
    '/:experienceId',
    authenticate,
    validate(experienceValidation.delete),
    experienceController.deleteExperience
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
