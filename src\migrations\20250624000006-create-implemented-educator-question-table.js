'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('ImplementedEducatorQuestion', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      educatorQuestionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'EducatorQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add unique constraint
    await queryInterface.addIndex(
      'ImplementedEducatorQuestion',
      ['userId', 'educatorQuestionId'],
      {
        unique: true,
        name: 'implemented_educator_question_unique_idx',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ImplementedEducatorQuestion');
  },
};
