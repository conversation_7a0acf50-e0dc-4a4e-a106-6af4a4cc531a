/**
 * Follow Service
 *
 * Handles business logic for user following functionality
 */
const followRepository = require('@models/repositories/follow.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { ApiException } = require('@utils/exception.utils');
const { AchievementType, UserType } = require('@utils/enums.utils');
const userRepository = require('@models/repositories/user.repository');
const { FOLLOW } = require('@utils/messages.utils');

/**
 * Helper function to check if a provider is following another provider
 * @param {Object} user - User object
 * @param {Object} targetUser - Target user object
 * @returns {boolean} True if provider is following another provider
 */
function isProviderFollowingAnotherProvider(user, targetUser) {
  const userId = user.id;
  const userType = user.userType;
  const targetUserId = targetUser.id;
  const targetUserType = targetUser.userType;

  const isProviderUser =
    userType === UserType.PROVIDER || userType === UserType.PROVIDER_PLUS;
  const isOtherProvider =
    targetUserType === UserType.PROVIDER ||
    targetUserType === UserType.PROVIDER_PLUS;
  const isFollowingSelf = targetUserId === userId;

  return isProviderUser && !isFollowingSelf && isOtherProvider;
}

/**
 * Follow Service
 */
const followService = {
  /**
   * Toggle follow status for a user
   * @param {Object} user - User object containing user type and other details
   * @param {string} followingId - User ID of the user being followed
   * @returns {Promise<Object>} Object with isFollowing status
   */
  toggleFollow: async (user, followingId) => {
    try {
      // First check if the target user exists
      const targetUser = await userRepository.findById(followingId);
      if (!targetUser) {
        throw new ApiException(404, FOLLOW.NOT_FOUND);
      }

      const result = await followRepository.toggleFollow(user.id, followingId);

      // Determine the correct achievement type based on user types
      const achievementType = isProviderFollowingAnotherProvider(
        user,
        targetUser
      )
        ? AchievementType.FOLLOW_OTHER_PROVIDERS
        : AchievementType.FOLLOW_PROVIDERS;

      // Update achievement progress with increment or decrement
      await achievementService.updateAchievementProgress(
        user.id,
        achievementType,
        result.isFollowing ? 1 : -1
      );

      return result;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all followers for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Followers and pagination info
   */
  getFollowers: async (userId, { page, limit }) => {
    try {
      return await followRepository.getFollowers(userId, {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all users that a user is following
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Following users and pagination info
   */
  getFollowing: async (userId, { page, limit }) => {
    try {
      return await followRepository.getFollowing(userId, {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },
};

module.exports = followService;
