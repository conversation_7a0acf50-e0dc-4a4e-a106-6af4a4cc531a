/**
 * Experience Week Media Service
 * Handles video watch status operations
 */
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');

class ExperienceWeekMediaService {
  constructor() {
    this.models = {
      ExperienceWeekMediaWatch:
        databaseService.getExperienceWeekMediaWatchModel(),
      ExperienceWeekMedia: databaseService.getExperienceWeekMediaModel(),
    };
  }

  /**
   * Update video watch status
   * @param {string} experienceWeekMediaId - Media ID
   * @param {string} userId - User ID
   * @param {Object} data - Watch status data
   * @param {number} [data.watchDuration] - Duration watched in seconds
   * @param {boolean} [data.isCompleted] - Whether video is completed
   * @returns {Promise<Object>} Updated watch record
   */
  async updateWatchStatus(experienceWeekMediaId, userId, data) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Verify the media exists
      const media = await this.models.ExperienceWeekMedia.findByPk(
        experienceWeekMediaId
      );
      if (!media) {
        throw new ApiException(HttpStatus.NOT_FOUND, 'Video not found');
      }

      // Find or create watch record
      const [watchRecord, created] =
        await this.models.ExperienceWeekMediaWatch.findOrCreate({
          where: {
            experienceWeekMediaId,
            userId,
          },
          defaults: {
            id: uuidv4(),
            watchDuration: data.watchDuration || 0,
            isCompleted: data.isCompleted || false,
            lastWatchedAt: new Date(),
          },
          transaction,
        });

      // If record exists, update it
      if (!created) {
        await watchRecord.update(
          {
            watchDuration: data.watchDuration || watchRecord.watchDuration,
            isCompleted:
              data.isCompleted !== undefined
                ? data.isCompleted
                : watchRecord.isCompleted,
            lastWatchedAt: new Date(),
          },
          { transaction }
        );
      }

      await transaction.commit();
      return watchRecord;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

module.exports = new ExperienceWeekMediaService();
