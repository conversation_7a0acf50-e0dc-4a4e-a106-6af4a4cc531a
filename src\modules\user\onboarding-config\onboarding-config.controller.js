/**
 * OnboardingConfig Controller
 *
 * Handles onboarding config-related HTTP requests
 */
const onboardingConfigService = require('./onboarding-config.service');
const { ApiResponse } = require('@utils/response.utils');
const { ONBOARDING_CONFIG } = require('@utils/messages.utils');

/**
 * OnboardingConfig Controller
 */
const onboardingConfigController = {
  /**
   * Get onboarding config for the authenticated user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getConfig: async (req, res, next) => {
    try {
      const { userType } = req.user;
      const config =
        await onboardingConfigService.getConfigByUserType(userType);
      return ApiResponse.success(res, ONBOARDING_CONFIG.RETRIEVED, config);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update currentStep for the authenticated user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateCurrentStep: async (req, res, next) => {
    try {
      const userId = req.user.id;
      const { currentStep } = req.body;
      const updatedUser = await onboardingConfigService.updateCurrentStep(
        userId,
        currentStep
      );
      return ApiResponse.success(res, ONBOARDING_CONFIG.UPDATED, updatedUser);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = onboardingConfigController;
