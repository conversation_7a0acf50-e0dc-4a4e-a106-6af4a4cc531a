/**
 * Subscription utility functions
 */
const { ApiException } = require('@utils/exception.utils');
const {
  HttpStatus,
  SubscriptionStatus,
  SubscriptionPlanSlug,
} = require('@utils/enums.utils');
const { SUBSCRIPTION } = require('@utils/messages.utils');

/**
 * Map Stripe status to internal subscription status
 * @param {string} stripeStatus - Stripe subscription status
 * @returns {string} Internal subscription status
 */
const mapStripeStatus = (stripeStatus) => {
  switch (stripeStatus) {
    case 'active':
      return SubscriptionStatus.ACTIVE;
    case 'incomplete':
      return SubscriptionStatus.INCOMPLETE;
    case 'incomplete_expired':
      return SubscriptionStatus.INCOMPLETE_EXPIRED;
    case 'past_due':
      return SubscriptionStatus.PAST_DUE;
    case 'canceled':
      return SubscriptionStatus.CANCELED;
    case 'unpaid':
      return SubscriptionStatus.UNPAID;
    case 'trialing':
      return SubscriptionStatus.TRIALING;
    default:
      throw new ApiException(
        HttpStatus.BAD_REQUEST,
        SUBSCRIPTION.INVALID_STATUS
      );
  }
};

/**
 * Checks if the user's active subscription is a specific plan.
 * @param {Object} activeSubscription - The user's active subscription object.
 * @param {string} planSlug - The plan slug to check against.
 * @returns {boolean}
 */
function isUserOnPlan(activeSubscription, planSlug) {
  return (
    activeSubscription &&
    activeSubscription.plan &&
    activeSubscription.plan.slug === planSlug.toLowerCase()
  );
}

/**
 * Checks if the user is on the Educator Free plan.
 * @param {Object} activeSubscription
 * @returns {boolean}
 */
function isEducatorFree(activeSubscription) {
  return isUserOnPlan(activeSubscription, SubscriptionPlanSlug.EDUCATOR_FREE);
}

module.exports = {
  mapStripeStatus,
  isUserOnPlan,
  isEducatorFree,
};
