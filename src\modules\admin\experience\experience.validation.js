/**
 * Experience Validation
 *
 * Validation schemas for experience operations
 */
const { body, param, query } = require('express-validator');

/**
 * Experience validation schemas
 */
const experienceValidation = {
  /**
   * Get all experiences validation schema
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('userId')
      .optional()
      .isUUID(4)
      .withMessage('User ID must be a valid UUID'),
  ],

  /**
   * Get experience by ID validation schema
   */
  getById: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
  ],

  /**
   * Get week details validation schema
   */
  getWeekDetails: [
    param('weekId').isUUID().withMessage('Invalid week ID format'),
  ],

  /**
   * Update experience validation schema
   */
  update: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
    body('title')
      .optional()
      .notEmpty()
      .withMessage('Experience title is required'),
    body('experienceLength')
      .optional()
      .isInt({ min: 1, max: 52 })
      .withMessage('Experience length must be between 1 and 52 weeks'),
    body('shortDescription').optional().isString(),
    body('longDescription').optional().isString(),
    body('personalNote').optional().isString(),
    body('pdCategoryIds')
      .optional()
      .isArray()
      .withMessage('PD categories must be an array'),
    body('pdCategoryIds.*')
      .optional()
      .isUUID()
      .withMessage('Each PD category ID must be a valid UUID'),
    body('wtdCategoryIds')
      .optional()
      .isArray()
      .withMessage('WTD categories must be an array'),
    body('wtdCategoryIds.*')
      .optional()
      .isUUID()
      .withMessage('Each WTD category ID must be a valid UUID'),
    body('media').optional().isArray().withMessage('Media must be an array'),
    body('media.*.type')
      .optional()
      .isIn(['IMAGE', 'VIDEO', 'DOCUMENT', 'LINK', 'AUDIO'])
      .withMessage('Media type must be IMAGE, VIDEO, DOCUMENT, LINK, or AUDIO'),
    body('media.*.url')
      .optional()
      .isString()
      .withMessage('Media URL must be a string'),
    body('media.*.title')
      .optional()
      .isString()
      .withMessage('Media title must be a string'),
    body('weeks').optional().isArray().withMessage('Weeks must be an array'),
    body('weeks.*.weekNumber')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Week number must be a positive integer'),
    body('weeks.*.title')
      .optional()
      .isString()
      .withMessage('Week title must be a string'),
    body('weeks.*.weeklyWhy')
      .optional()
      .isString()
      .withMessage('Weekly why must be a string'),
    body('weeks.*.insights')
      .optional()
      .isArray({ max: 5 })
      .withMessage('Insights must be an array with maximum 5 items'),
    body('weeks.*.insights.*.text')
      .optional()
      .notEmpty()
      .withMessage('Insight text is required'),
    body('weeks.*.insights.*.sourceUrl')
      .optional()
      .isString()
      .withMessage('Source URL must be a string'),
  ],

  /**
   * Get reviews validation schema
   */
  getReviews: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
  ],

  /**
   * Get discussions validation schema
   */
  getDiscussions: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
  ],

  /**
   * Update experience status validation schema
   */
  updateStatus: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
    body('status')
      .isIn(['PENDING', 'APPROVED', 'REJECTED'])
      .withMessage('Invalid status value'),
  ],

  /**
   * Delete experience validation schema
   */
  delete: [
    param('experienceId').isUUID().withMessage('Invalid experience ID format'),
  ],
};

module.exports = experienceValidation;
