'use strict';
const { v4: uuidv4 } = require('uuid');
const { UserType } = require('../utils/enums.utils');

/**
 * Achievements Seeder (idempotent, full coverage)
 * - targetValue is passed as a field for each achievement.
 * - achievementType is now ALL CAPS (for consistency).
 */
module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Achievements for all milestones and types, with targetValue and achievementType in ALL CAPS
      const achievementSets = [
        // ---- EDUCATOR/EDUCATOR_PLUS ----
        {
          userTypes: [UserType.EDUCATOR, UserType.EDUCATOR_PLUS],
          milestones: [
            {
              name: 'WTD Beginner',
              achievements: [
                {
                  name: 'Create WTD Account',
                  achievementType: 'CREATE_ACCOUNT',
                  description: 'Sign up on the WTD platform.',
                  targetValue: 1,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Contributor',
              achievements: [
                {
                  name: 'Contribute to Three Insights',
                  achievementType: 'INSIGHT_CONTRIBUTED',
                  description: 'Contribute to three insights.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Implement Three Insights',
                  achievementType: 'INSIGHT_IMPLEMENTED',
                  description: 'Implement three insights.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Like Three Insights',
                  achievementType: 'INSIGHT_LIKED',
                  description: 'Like three insights.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Save One Insight to Toolkit',
                  achievementType: 'INSIGHT_SAVED',
                  description: 'Save one insight to toolkit.',
                  targetValue: 1,
                  isActive: true,
                },
                {
                  name: 'Update Profile Picture',
                  achievementType: 'PROFILE_PICTURE_UPDATE',
                  description: 'Set or update your profile picture.',
                  targetValue: 1,
                  isActive: true,
                },
                {
                  name: 'Answer Ice Breaker Questions on Profile',
                  achievementType: 'ICE_BREAKER_ANSWERED',
                  description: 'Answer ice breaker questions on your profile.',
                  targetValue: 1,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Reflector',
              achievements: [
                {
                  name: 'Contribute to Three More Insights',
                  achievementType: 'INSIGHT_CONTRIBUTED_2',
                  description: 'Contribute to three more insights.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Follow Three Providers',
                  achievementType: 'FOLLOW_PROVIDERS',
                  description: 'Follow three providers.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Save Three More Insights to Toolkit',
                  achievementType: 'INSIGHT_SAVED_2',
                  description: 'Save three more insights to toolkit.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Like Three More Insights',
                  achievementType: 'INSIGHT_LIKED_2',
                  description: 'Like three more insights.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Complete Reflector Form',
                  achievementType: 'REFLECTOR_FORM',
                  description: 'Submit a completed reflector form.',
                  targetValue: 1,
                  isActive: true,
                  formTitle: 'Reflector Form Submission',
                },
              ],
            },
            {
              name: 'WTD Encourager',
              achievements: [
                {
                  name: 'Like Five Educators Contributions',
                  achievementType: 'LIKE_EDUCATOR_CONTRIBUTIONS',
                  description: "Like five educators' contributions.",
                  targetValue: 5,
                  isActive: true,
                },
                {
                  name: 'Contribute to Five More Insights',
                  achievementType: 'INSIGHT_CONTRIBUTED_3',
                  description: 'Contribute to five more insights.',
                  targetValue: 5,
                  isActive: true,
                },
                {
                  name: 'Follow Five Providers',
                  achievementType: 'FOLLOW_PROVIDERS_2',
                  description: 'Follow five providers.',
                  targetValue: 5,
                  isActive: true,
                },
                {
                  name: 'Save Five More Insights to Toolkit',
                  achievementType: 'INSIGHT_SAVED_3',
                  description: 'Save five more insights to toolkit.',
                  targetValue: 5,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Catalyst',
              achievements: [
                {
                  name: 'Implement Ten More Insights',
                  achievementType: 'INSIGHT_IMPLEMENTED_2',
                  description: 'Implement ten more insights.',
                  targetValue: 10,
                  isActive: true,
                },
                {
                  name: 'Contribute to Ten More Insights',
                  achievementType: 'INSIGHT_CONTRIBUTED_4',
                  description: 'Contribute to ten more insights.',
                  targetValue: 10,
                  isActive: true,
                },
                {
                  name: 'Complete One Experience',
                  achievementType: 'EXPERIENCE_COMPLETED',
                  description: 'Complete one experience.',
                  targetValue: 1,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Visionary',
              achievements: [
                {
                  name: 'Contribute to 15 More Insights',
                  achievementType: 'INSIGHT_CONTRIBUTED_5',
                  description: 'Contribute to 15 more insights.',
                  targetValue: 15,
                  isActive: true,
                },
                {
                  name: 'Implement 15 More Insights',
                  achievementType: 'INSIGHT_IMPLEMENTED_3',
                  description: 'Implement 15 more insights.',
                  targetValue: 15,
                  isActive: true,
                },
                {
                  name: 'Complete Visionary Form',
                  achievementType: 'VISIONARY_FORM',
                  description: 'Submit the visionary form.',
                  targetValue: 1,
                  isActive: true,
                  formTitle: 'Visionary Form Submission',
                },
              ],
            },
            {
              name: 'WTD Innovator',
              achievements: [
                {
                  name: 'Complete Another Experience',
                  achievementType: 'EXPERIENCE_COMPLETED_2',
                  description: 'Complete another experience.',
                  targetValue: 1,
                  isActive: true,
                },
                {
                  name: 'Complete Innovator Form',
                  achievementType: 'INNOVATOR_FORM',
                  description: 'Submit the innovator form.',
                  targetValue: 1,
                  isActive: true,
                  formTitle: 'Innovator Form Submission',
                },
              ],
            },
            {
              name: 'WTD Trailblazer',
              achievements: [
                {
                  name: 'Complete Trailblazer Form',
                  achievementType: 'TRAILBLAZER_FORM',
                  description: 'Submit the trailblazer form.',
                  targetValue: 1,
                  isActive: true,
                  formTitle: 'Trailblazer Form Submission',
                },
              ],
            },
          ],
        },
        // ---- PROVIDER/PROVIDER_PLUS ----
        {
          userTypes: [UserType.PROVIDER, UserType.PROVIDER_PLUS],
          milestones: [
            {
              name: 'WTD Beginner',
              achievements: [
                {
                  name: 'Create WTD Account',
                  achievementType: 'CREATE_ACCOUNT',
                  description: 'Sign up on the WTD platform.',
                  targetValue: 1,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Contributor',
              achievements: [
                {
                  name: 'Post Three Insights',
                  achievementType: 'INSIGHT_POSTED',
                  description: 'Post three insights as a provider.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Like Three Other Providers Insights',
                  achievementType: 'PROVIDER_INSIGHT_LIKED',
                  description: "Like three other providers' insights.",
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Save Insight to Toolkit',
                  achievementType: 'INSIGHT_SAVED',
                  description: 'Save one insight to toolkit.',
                  targetValue: 1,
                  isActive: true,
                },
                {
                  name: 'Update Profile Picture',
                  achievementType: 'PROFILE_PICTURE_UPDATE',
                  description: 'Set or update your profile picture.',
                  targetValue: 1,
                  isActive: true,
                },
                {
                  name: 'Answer Ice Breaker Questions on Profile',
                  achievementType: 'ICE_BREAKER_ANSWERED',
                  description: 'Answer ice breaker questions on your profile.',
                  targetValue: 1,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Reflector',
              achievements: [
                {
                  name: 'Contribute to Three Other Providers Insights',
                  achievementType: 'CONTRIBUTE_OTHER_PROVIDER',
                  description: "Contribute to three other providers' insights.",
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Post Three More Insights',
                  achievementType: 'INSIGHT_POSTED_2',
                  description: 'Post three more insights.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Follow Three Other Providers',
                  achievementType: 'FOLLOW_OTHER_PROVIDERS',
                  description: 'Follow three other providers.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Save Three More Insights to Toolkit',
                  achievementType: 'INSIGHT_SAVED_2',
                  description: 'Save three more insights to toolkit.',
                  targetValue: 3,
                  isActive: true,
                },
                {
                  name: 'Like Three More Insights',
                  achievementType: 'PROVIDER_INSIGHT_LIKED_2',
                  description: 'Like three more insights.',
                  targetValue: 3,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Encourager',
              achievements: [
                {
                  name: 'Like Five Educators Contributions',
                  achievementType: 'LIKE_EDUCATOR_CONTRIBUTIONS',
                  description: "Like five educators' contributions.",
                  targetValue: 5,
                  isActive: true,
                },
                {
                  name: 'Contribute to Five More Insights',
                  achievementType: 'CONTRIBUTE_OTHER_PROVIDER_2',
                  description: 'Contribute to five more insights.',
                  targetValue: 5,
                  isActive: true,
                },
                {
                  name: 'Follow Five Providers',
                  achievementType: 'FOLLOW_OTHER_PROVIDERS_2',
                  description: 'Follow five providers.',
                  targetValue: 5,
                  isActive: true,
                },
                {
                  name: 'Save Five More Insights to Toolkit',
                  achievementType: 'INSIGHT_SAVED_3',
                  description: 'Save five more insights to toolkit.',
                  targetValue: 5,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Catalyst',
              achievements: [
                {
                  name: 'Post Five More Insights',
                  achievementType: 'INSIGHT_POSTED_3',
                  description: 'Post five more insights.',
                  targetValue: 5,
                  isActive: true,
                },
                {
                  name: 'Create One Experience',
                  achievementType: 'EXPERIENCE_CREATED',
                  description: 'Create one experience.',
                  targetValue: 1,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Visionary',
              achievements: [
                {
                  name: 'Contribute to 10 Other Providers Insights',
                  achievementType: 'CONTRIBUTE_OTHER_PROVIDER_3',
                  description: "Contribute to 10 other providers' insights.",
                  targetValue: 10,
                  isActive: true,
                },
                {
                  name: 'Post 10 More Insights',
                  achievementType: 'INSIGHT_POSTED_4',
                  description: 'Post 10 more insights.',
                  targetValue: 10,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Innovator',
              achievements: [
                {
                  name: 'Create Another Experience',
                  achievementType: 'EXPERIENCE_CREATED_2',
                  description: 'Create another experience.',
                  targetValue: 1,
                  isActive: true,
                },
              ],
            },
            {
              name: 'WTD Trailblazer',
              achievements: [
                {
                  name: 'Complete Trailblazer Form',
                  achievementType: 'TRAILBLAZER_FORM',
                  description: 'Submit the trailblazer form.',
                  targetValue: 1,
                  isActive: true,
                  formTitle: 'Trailblazer Form Submission',
                },
              ],
            },
          ],
        },
      ];

      // Get existing achievements
      const existingAchievements = await queryInterface.sequelize.query(
        `SELECT "name", "milestoneId" FROM "Achievement"`,
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        }
      );

      const existingMap = new Map(
        existingAchievements.map((a) => [`${a.name}-${a.milestoneId}`, true])
      );

      // Process each achievement set
      for (const set of achievementSets) {
        for (const userType of set.userTypes) {
          for (const milestone of set.milestones) {
            // Get milestone ID
            const [milestoneRecord] = await queryInterface.sequelize.query(
              `SELECT "id" FROM "Milestone" WHERE "name" = :name AND "userType" = :type`,
              {
                replacements: {
                  name: milestone.name,
                  type: userType,
                },
                type: queryInterface.sequelize.QueryTypes.SELECT,
                transaction,
              }
            );

            if (!milestoneRecord) {
              continue;
            }

            // Filter out existing achievements and add IDs for new ones
            const newAchievements = milestone.achievements
              .filter(
                (a) => !existingMap.has(`${a.name}-${milestoneRecord.id}`)
              )
              .map((a) => ({
                ...a,
                id: uuidv4(),
                milestoneId: milestoneRecord.id,
                userType: userType,
                createdAt: new Date(),
                updatedAt: new Date(),
              }));

            if (newAchievements.length > 0) {
              await queryInterface.bulkInsert('Achievement', newAchievements, {
                transaction,
              });
            }
          }
        }
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete('Achievement', null, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
