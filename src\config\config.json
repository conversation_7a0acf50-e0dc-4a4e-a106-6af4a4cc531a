{"development": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}, "test": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}, "production": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}, "local": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": false}}}