/**
 * InsightView Repository
 * Handles database operations for InsightView model
 */
const databaseService = require('@config/database.config');
const subscriptionService = require('@modules/user/subscription/subscription.service');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { INSIGHT } = require('@utils/messages.utils');
const { Op } = require('sequelize');
const { isEducatorFree } = require('@utils/subscription.utils');

class InsightViewRepository {
  constructor() {
    this.models = {
      InsightView: databaseService.getInsightViewModel(),
    };
  }

  /**
   * Check if user has exceeded monthly insight viewing limit
   * @param {string} userId - User ID
   * @param {string} insightId - Insight ID
   * @returns {Promise<void>}
   * @throws {ApiException} If limit exceeded
   */
  async checkMonthlyInsightLimit(userId, insightId) {
    try {
      // Get user's active subscription
      const activeSubscription =
        await subscriptionService.getActiveSubscription(userId);

      // Check if user has the EDUCATOR_FREE plan
      if (isEducatorFree(activeSubscription)) {
        // Get current monthly view count
        const monthlyViewCount = await this.getMonthlyViewCount(userId);

        // Check if limit exceeded
        if (monthlyViewCount >= activeSubscription.plan.insightsLimit) {
          throw new ApiException(
            HttpStatus.FORBIDDEN,
            INSIGHT.MONTHLY_LIMIT_EXCEEDED
          );
        }

        // Record the view
        await this.recordView(userId, insightId);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Record a view for an insight by a user
   * @param {string} userId - User ID
   * @param {string} insightId - Insight ID
   * @returns {Promise<Object>} Created view record
   */
  async recordView(userId, insightId) {
    try {
      // Always create a new view record to count each view
      const view = await this.models.InsightView.create({
        userId,
        insightId,
        viewedAt: new Date(),
      });

      return view;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the count of insights viewed by a user in the current month
   * @param {string} userId - User ID
   * @returns {Promise<number>} Count of insights viewed this month
   */
  async getMonthlyViewCount(userId) {
    try {
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const endOfMonth = new Date();
      endOfMonth.setMonth(endOfMonth.getMonth() + 1);
      endOfMonth.setDate(0);
      endOfMonth.setHours(23, 59, 59, 999);

      const count = await this.models.InsightView.count({
        where: {
          userId,
          viewedAt: {
            [Op.between]: [startOfMonth, endOfMonth],
          },
        },
      });

      return count;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a user has viewed a specific insight
   * @param {string} userId - User ID
   * @param {string} insightId - Insight ID
   * @returns {Promise<boolean>} True if user has viewed the insight
   */
  async hasUserViewedInsight(userId, insightId) {
    try {
      const view = await this.models.InsightView.findOne({
        where: {
          userId,
          insightId,
        },
      });

      return !!view;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new InsightViewRepository();
