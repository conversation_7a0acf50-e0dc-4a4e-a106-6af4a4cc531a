/**
 * Database Configuration and Connection Management
 */
const { Sequelize } = require('sequelize');
const Logger = require('@utils/logger.utils');
const { DB, IS_LOCAL } = require('@config/app.config');

// Import all models
const Admin = require('@models/admin.model');
const User = require('@models/user.model');
const Insight = require('@models/insight.model');
const InsightView = require('@models/insight-view.model');
const Focus = require('@models/focus.model');
const PdCategory = require('@models/pd-category.model');
const WtdCategory = require('@models/wtd-category.model');
const InsightFocus = require('@models/insight-focus.model');
const InsightWtdCategory = require('@models/insight-wtd-category.model');
const BookmarkedInsight = require('@models/bookmarked-insight.model');
const LikedInsight = require('@models/liked-insight.model');
const ImplementedInsight = require('@models/implemented-insight.model');
const Membership = require('@models/membership.model');
const WtdIceBreaker = require('@models/wtd-ice-breaker.model');
const UserFocus = require('@models/user-focus.model');
const OnboardingConfig = require('@models/onboarding-config.model');
const Follow = require('@models/follow.model');
const Milestone = require('@models/milestone.model');
const MilestoneQuestion = require('@models/milestone-question.model');
const MilestoneQuestionOption = require('@models/milestone-question-option.model');
const UserMilestoneResponse = require('@models/user-milestone-response.model');
const Achievement = require('@models/achievement.model');
const UserAchievement = require('@models/user-achievement.model');
const UserMilestone = require('@models/user-milestone.model');
const AchievementQuestion = require('@models/achievement-question.model');
const AchievementQuestionOption = require('@models/achievement-question-option.model');
const UserAchievementResponse = require('@models/user-achievement-response.model');
const SubscriptionPlan = require('@models/subscription-plan.model');
const PaymentMethod = require('@models/payment-method.model');
const UserSubscription = require('@models/user-subscription.model');
const Notification = require('@models/notification.model');

// Import experience-related models
const Experience = require('@models/experience.model');
const ExperienceEnrollment = require('@models/experience-enrollment.model');
const ExperienceWeekProgress = require('@models/experience-week-progress.model');
const ExperienceMedia = require('@models/experience-media.model');
const ExperienceWeek = require('@models/experience-week.model');
const ExperienceWeekMedia = require('@models/experience-week-media.model');
const ExperienceWeekInsight = require('@models/experience-week-insight.model');

const ExperiencePdCategory = require('@models/experience-pd-category.model');
const ExperienceWtdCategory = require('@models/experience-wtd-category.model');
const ExperienceReview = require('@models/experience-review.model');
const ExperienceDiscussion = require('@models/experience-discussion.model');
const ExperienceDiscussionLike = require('@models/experience-discussion-like.model');

// Contribution models
const Contribution = require('@models/contribution.model');
const ContributionLike = require('@models/contribution-like.model');

// Import report models
const InsightReport = require('@models/insight-report.model');
const ContributionReport = require('@models/contribution-report.model');

// Import new experience-related model
const ExperienceWeekMediaWatch = require('@models/experience-week-media-watch.model');

// Import EducatorQuestion-related models
const EducatorQuestion = require('@models/educator-question.model');
const EducatorQuestionFocus = require('@models/educator-question-focus.model');
const EducatorQuestionWtdCategory = require('@models/educator-question-wtd-category.model');
const BookmarkedEducatorQuestion = require('@models/bookmarked-educator-question.model');
const LikedEducatorQuestion = require('@models/liked-educator-question.model');
const ImplementedEducatorQuestion = require('@models/implemented-educator-question.model');
const EducatorQuestionContribution = require('@models/educator-question-contribution.model');
const EducatorQuestionContributionLike = require('@models/educator-question-contribution-like.model');
const EducatorQuestionView = require('@models/educator-question-view.model');

// Database instance
let sequelize;

/**
 * Database service
 */
const databaseService = {
  /**
   * Initialize database connection
   * @param {Object} app - Express application
   * @returns {Object} Sequelize instance
   */
  init: async (app) => {
    try {
      // Get database configuration
      const env = process.env.NODE_ENV || 'development';
      Logger.info(`Initializing database connection for environment: ${env}`);

      // Configure SSL based on environment
      const sslConfig = IS_LOCAL
        ? false // Disable SSL for localhost
        : {
            // Enable SSL for development and production
            require: true,
            rejectUnauthorized: false, // Allow self-signed certificates
          };

      Logger.info(
        `SSL ${IS_LOCAL ? 'disabled' : 'enabled'} for database connection`
      );

      // Create Sequelize instance using configuration
      if (process.env.DATABASE_URL) {
        Logger.info('Connecting to database using DATABASE_URL');
        sequelize = new Sequelize(process.env.DATABASE_URL, {
          dialect: 'postgres',
          logging: DB.logging,
          define: DB.define,
          pool: DB.pool,
          dialectOptions: {
            ssl: sslConfig,
          },
        });
      } else {
        Logger.info(
          'Connecting to database using individual configuration parameters'
        );
        sequelize = new Sequelize(DB.database, DB.username, DB.password, {
          host: DB.host,
          port: DB.port,
          dialect: DB.dialect,
          logging: DB.logging,
          define: DB.define,
          pool: DB.pool,
          dialectOptions: {
            ssl: sslConfig,
          },
        });
      }

      // Test connection
      await sequelize.authenticate();
      Logger.success('Database connection established successfully');

      // Attach sequelize to app
      if (app) {
        app.set('sequelize', sequelize);
        Logger.info('Sequelize instance attached to app');
      }

      // Initialize models
      Admin.init(sequelize);
      User.init(sequelize);
      Membership.init(sequelize);
      WtdIceBreaker.init(sequelize);
      OnboardingConfig.init(sequelize);
      SubscriptionPlan.init(sequelize);
      PaymentMethod.init(sequelize);
      UserSubscription.init(sequelize);
      Notification.init(sequelize);

      // Initialize junction table models
      InsightFocus.init(sequelize);
      InsightWtdCategory.init(sequelize);
      BookmarkedInsight.init(sequelize);
      LikedInsight.init(sequelize);
      ImplementedInsight.init(sequelize);
      UserFocus.init(sequelize);
      Follow.init(sequelize);

      ExperiencePdCategory.init(sequelize);
      ExperienceWtdCategory.init(sequelize);

      // Initialize contribution models
      Contribution.init(sequelize);
      ContributionLike.init(sequelize);

      // Initialize report models
      InsightReport.init(sequelize);
      ContributionReport.init(sequelize);

      // Initialize other models
      Insight.init(sequelize);
      InsightView.init(sequelize);
      Focus.init(sequelize);
      PdCategory.init(sequelize);
      WtdCategory.init(sequelize);

      // Initialize experience models
      Experience.init(sequelize);
      ExperienceEnrollment.init(sequelize);
      ExperienceWeekProgress.init(sequelize);
      ExperienceMedia.init(sequelize);
      ExperienceWeek.init(sequelize);
      ExperienceWeekMedia.init(sequelize);
      ExperienceWeekInsight.init(sequelize);
      ExperienceReview.init(sequelize);
      ExperienceDiscussion.init(sequelize);
      ExperienceDiscussionLike.init(sequelize);

      // Initialize new experience-related model
      ExperienceWeekMediaWatch.init(sequelize);

      // Initialize milestone model
      Milestone.init(sequelize);
      MilestoneQuestion.init(sequelize);
      MilestoneQuestionOption.init(sequelize);
      UserMilestoneResponse.init(sequelize);
      Achievement.init(sequelize);
      UserAchievement.init(sequelize);
      UserMilestone.init(sequelize);
      AchievementQuestion.init(sequelize);
      AchievementQuestionOption.init(sequelize);
      UserAchievementResponse.init(sequelize);

      // Initialize EducatorQuestion models
      EducatorQuestion.init(sequelize);
      EducatorQuestionFocus.init(sequelize);
      EducatorQuestionWtdCategory.init(sequelize);
      BookmarkedEducatorQuestion.init(sequelize);
      LikedEducatorQuestion.init(sequelize);
      ImplementedEducatorQuestion.init(sequelize);
      EducatorQuestionContribution.init(sequelize);
      EducatorQuestionContributionLike.init(sequelize);
      EducatorQuestionView.init(sequelize);

      // Create a models object for associations
      const models = {
        Admin,
        User,
        Insight,
        InsightView,
        Focus,
        PdCategory,
        WtdCategory,
        InsightFocus,
        InsightWtdCategory,
        BookmarkedInsight,
        LikedInsight,
        ImplementedInsight,
        Membership,
        WtdIceBreaker,
        UserFocus,
        OnboardingConfig,
        Follow,
        Experience,
        ExperienceEnrollment,
        ExperienceWeekProgress,
        ExperienceMedia,
        ExperienceWeek,
        ExperienceWeekMedia,
        ExperienceWeekInsight,
        ExperiencePdCategory,
        ExperienceWtdCategory,
        ExperienceReview,
        ExperienceDiscussion,
        ExperienceDiscussionLike,
        Contribution,
        ContributionLike,
        InsightReport,
        ContributionReport,
        ExperienceWeekMediaWatch,
        Milestone,
        MilestoneQuestion,
        MilestoneQuestionOption,
        UserMilestoneResponse,
        Achievement,
        UserAchievement,
        UserMilestone,
        AchievementQuestion,
        AchievementQuestionOption,
        UserAchievementResponse,
        SubscriptionPlan,
        PaymentMethod,
        UserSubscription,
        Notification,
        EducatorQuestion,
        EducatorQuestionFocus,
        EducatorQuestionWtdCategory,
        BookmarkedEducatorQuestion,
        LikedEducatorQuestion,
        ImplementedEducatorQuestion,
        EducatorQuestionContribution,
        EducatorQuestionContributionLike,
        EducatorQuestionView,
      };

      // Set up associations
      Object.values(models).forEach((model) => {
        if (model.associate) {
          model.associate(models);
        }
      });

      return sequelize;
    } catch (error) {
      Logger.error('Unable to connect to the database', error);
      Logger.warn(
        'Continuing without database connection. Some features may not work.'
      );
    }
  },

  /**
   * Get Sequelize instance
   * @returns {Object} Sequelize instance
   */
  getSequelize: () => {
    if (!sequelize) {
      throw new Error('Database not initialized');
    }
    return sequelize;
  },

  /**
   * Close database connection
   */
  close: async () => {
    if (sequelize) {
      await sequelize.close();
      Logger.info('Database connection closed');
    }
  },

  /**
   * Get database configuration for the current environment
   * @returns {Object} Database configuration
   */
  getConfig: () => {
    return DB;
  },

  /**
   * Get Admin model
   * @returns {Object} Admin model
   */
  getAdminModel: () => {
    return Admin;
  },

  /**
   * Get User model
   * @returns {Object} User model
   */
  getUserModel: () => {
    return User;
  },

  /**
   * Get Insight model
   * @returns {Object} Insight model
   */
  getInsightModel: () => {
    return Insight;
  },

  /**
   * Get InsightView model
   * @returns {Object} InsightView model
   */
  getInsightViewModel: () => {
    return InsightView;
  },

  /**
   * Get Focus model
   * @returns {Object} Focus model
   */
  getFocusModel: () => {
    return Focus;
  },

  /**
   * Get PdCategory model
   * @returns {Object} PdCategory model
   */
  getPdCategoryModel: () => {
    return PdCategory;
  },

  /**
   * Get WtdCategory model
   * @returns {Object} WtdCategory model
   */
  getWtdCategoryModel: () => {
    return WtdCategory;
  },

  /**
   * Get InsightFocus model
   * @returns {Object} InsightFocus model
   */
  getInsightFocusModel: () => {
    return InsightFocus;
  },

  /**
   * Get InsightWtdCategory model
   * @returns {Object} InsightWtdCategory model
   */
  getInsightWtdCategoryModel: () => {
    return InsightWtdCategory;
  },

  /**
   * Get BookmarkedInsight model
   * @returns {Object} BookmarkedInsight model
   */
  getBookmarkedInsightModel: () => {
    return BookmarkedInsight;
  },

  /**
   * Get LikedInsight model
   * @returns {Object} LikedInsight model
   */
  getLikedInsightModel: () => {
    return LikedInsight;
  },

  /**
   * Get ImplementedInsight model
   * @returns {Object} ImplementedInsight model
   */
  getImplementedInsightModel: () => {
    return ImplementedInsight;
  },

  /**
   * Get Membership model
   * @returns {Object} Membership model
   */
  getMembershipModel: () => {
    return Membership;
  },

  /**
   * Get Contribution model
   * @returns {Object} Contribution model
   */
  getContributionModel: () => {
    return Contribution;
  },

  /**
   * Get ContributionLike model
   * @returns {Object} ContributionLike model
   */
  getContributionLikeModel: () => {
    return ContributionLike;
  },

  /**
   * Get InsightReport model
   * @returns {Object} InsightReport model
   */
  getInsightReportModel: () => {
    return InsightReport;
  },

  /**
   * Get ContributionReport model
   * @returns {Object} ContributionReport model
   */
  getContributionReportModel: () => {
    return ContributionReport;
  },

  /**
   * Get Experience model
   * @returns {Object} Experience model
   */
  getExperienceModel: () => {
    return Experience;
  },

  /**
   * Get ExperienceEnrollment model
   * @returns {Object} ExperienceEnrollment model
   */
  getExperienceEnrollmentModel: () => {
    return ExperienceEnrollment;
  },

  /**
   * Get ExperienceWeekProgress model
   * @returns {Object} ExperienceWeekProgress model
   */
  getExperienceWeekProgressModel: () => {
    return ExperienceWeekProgress;
  },

  /**
   * Get ExperienceMedia model
   * @returns {Object} ExperienceMedia model
   */
  getExperienceMediaModel: () => {
    return ExperienceMedia;
  },

  /**
   * Get ExperienceWeek model
   * @returns {Object} ExperienceWeek model
   */
  getExperienceWeekModel: () => {
    return ExperienceWeek;
  },

  /**
   * Get ExperienceWeekMedia model
   * @returns {Object} ExperienceWeekMedia model
   */
  getExperienceWeekMediaModel: () => {
    return ExperienceWeekMedia;
  },

  /**
   * Get ExperienceWeekInsight model
   * @returns {Object} ExperienceWeekInsight model
   */
  getExperienceWeekInsightModel: () => {
    return ExperienceWeekInsight;
  },

  /**
   * Get ExperiencePdCategory model
   * @returns {Object} ExperiencePdCategory model
   */
  getExperiencePdCategoryModel: () => {
    return ExperiencePdCategory;
  },

  /**
   * Get ExperienceWtdCategory model
   * @returns {Object} ExperienceWtdCategory model
   */
  getExperienceWtdCategoryModel: () => {
    return ExperienceWtdCategory;
  },

  /**
   * Get Experience Review model
   * @returns {Object} Experience Review model
   */
  getExperienceReviewModel: () => {
    return ExperienceReview;
  },

  /**
   * Get ExperienceDiscussion model
   * @returns {Object} ExperienceDiscussion model
   */
  getExperienceDiscussionModel: () => {
    return ExperienceDiscussion;
  },

  /**
   * Get ExperienceDiscussionLike model
   * @returns {Object} ExperienceDiscussionLike model
   */
  getExperienceDiscussionLikeModel: () => {
    return ExperienceDiscussionLike;
  },

  /**
   * Get WtdIceBreaker model
   * @returns {Object} WtdIceBreaker model
   */
  getWtdIceBreakerModel: () => {
    return WtdIceBreaker;
  },

  /**
   * Get UserFocus model
   * @returns {Object} UserFocus model
   */
  getUserFocusModel: () => {
    return UserFocus;
  },

  /**
   * Get OnboardingConfig model
   * @returns {Object} OnboardingConfig model
   */
  getOnboardingConfigModel: () => {
    return OnboardingConfig;
  },

  /**
   * Get Follow model
   * @returns {Object} Follow model
   */
  getFollowModel: () => {
    return Follow;
  },

  /**
   * Get ExperienceWeekMediaWatch model
   * @returns {Object} ExperienceWeekMediaWatch model
   */
  getExperienceWeekMediaWatchModel: () => {
    return ExperienceWeekMediaWatch;
  },

  /**
   * Get Milestone model
   * @returns {Object} Milestone model
   */
  getMilestoneModel: () => {
    return Milestone;
  },

  /**
   * Get MilestoneQuestion model
   * @returns {Object} MilestoneQuestion model
   */
  getMilestoneQuestionModel: () => {
    return MilestoneQuestion;
  },

  /**
   * Get MilestoneQuestionOption model
   * @returns {Object} MilestoneQuestionOption model
   */
  getMilestoneQuestionOptionModel: () => {
    return MilestoneQuestionOption;
  },

  /**
   * Get UserMilestoneResponse model
   * @returns {Object} UserMilestoneResponse model
   */
  getUserMilestoneResponseModel: () => {
    return UserMilestoneResponse;
  },

  /**
   * Get Achievement model
   * @returns {Object} Achievement model
   */
  getAchievementModel: () => {
    return Achievement;
  },

  /**
   * Get UserAchievement model
   * @returns {Object} UserAchievement model
   */
  getUserAchievementModel: () => {
    return UserAchievement;
  },

  /**
   * Get UserMilestone model
   * @returns {Object} UserMilestone model
   */
  getUserMilestoneModel: () => {
    return UserMilestone;
  },

  /**
   * Get AchievementQuestion model
   * @returns {Object} AchievementQuestion model
   */
  getAchievementQuestionModel: () => {
    return AchievementQuestion;
  },

  /**
   * Get AchievementQuestionOption model
   * @returns {Object} AchievementQuestionOption model
   */
  getAchievementQuestionOptionModel: () => {
    return AchievementQuestionOption;
  },

  /**
   * Get UserAchievementResponse model
   * @returns {Object} UserAchievementResponse model
   */
  getUserAchievementResponseModel: () => {
    return UserAchievementResponse;
  },

  /**
   * Get SubscriptionPlan model
   * @returns {Object} SubscriptionPlan model
   */
  getSubscriptionPlanModel: () => {
    return SubscriptionPlan;
  },

  /**
   * Get PaymentMethod model
   * @returns {Object} PaymentMethod model
   */
  getPaymentMethodModel: () => {
    return PaymentMethod;
  },

  /**
   * Get UserSubscription model
   * @returns {Object} UserSubscription model
   */
  getUserSubscriptionModel: () => {
    return UserSubscription;
  },

  /**
   * Get Notification model
   * @returns {Object} Notification model
   */
  getNotificationModel: () => {
    return Notification;
  },

  /**
   * Get EducatorQuestion model
   * @returns {Object} EducatorQuestion model
   */
  getEducatorQuestionModel: () => {
    return EducatorQuestion;
  },

  /**
   * Get EducatorQuestionFocus model
   * @returns {Object} EducatorQuestionFocus model
   */
  getEducatorQuestionFocusModel: () => {
    return EducatorQuestionFocus;
  },

  /**
   * Get EducatorQuestionWtdCategory model
   * @returns {Object} EducatorQuestionWtdCategory model
   */
  getEducatorQuestionWtdCategoryModel: () => {
    return EducatorQuestionWtdCategory;
  },

  /**
   * Get BookmarkedEducatorQuestion model
   * @returns {Object} BookmarkedEducatorQuestion model
   */
  getBookmarkedEducatorQuestionModel: () => {
    return BookmarkedEducatorQuestion;
  },

  /**
   * Get LikedEducatorQuestion model
   * @returns {Object} LikedEducatorQuestion model
   */
  getLikedEducatorQuestionModel: () => {
    return LikedEducatorQuestion;
  },

  /**
   * Get ImplementedEducatorQuestion model
   * @returns {Object} ImplementedEducatorQuestion model
   */
  getImplementedEducatorQuestionModel: () => {
    return ImplementedEducatorQuestion;
  },

  /**
   * Get EducatorQuestionContribution model
   * @returns {Object} EducatorQuestionContribution model
   */
  getEducatorQuestionContributionModel: () => {
    return EducatorQuestionContribution;
  },

  /**
   * Get EducatorQuestionContributionLike model
   * @returns {Object} EducatorQuestionContributionLike model
   */
  getEducatorQuestionContributionLikeModel: () => {
    return EducatorQuestionContributionLike;
  },

  /**
   * Get EducatorQuestionView model
   * @returns {Object} EducatorQuestionView model
   */
  getEducatorQuestionViewModel: () => {
    return EducatorQuestionView;
  },

  /**
   * Get all models
   * @returns {Object} All models
   */
  getModels: () => ({
    Admin,
    User,
    Insight,
    InsightView,
    Focus,
    PdCategory,
    WtdCategory,
    InsightFocus,
    InsightWtdCategory,
    BookmarkedInsight,
    LikedInsight,
    ImplementedInsight,
    Membership,
    WtdIceBreaker,
    UserFocus,
    OnboardingConfig,
    Follow,
    Experience,
    ExperienceEnrollment,
    ExperienceWeekProgress,
    ExperienceMedia,
    ExperienceWeek,
    ExperienceWeekMedia,
    ExperienceWeekInsight,
    ExperiencePdCategory,
    ExperienceWtdCategory,
    ExperienceReview,
    ExperienceDiscussion,
    ExperienceDiscussionLike,
    Contribution,
    ContributionLike,
    InsightReport,
    ContributionReport,
    ExperienceWeekMediaWatch,
    Milestone,
    MilestoneQuestion,
    MilestoneQuestionOption,
    UserMilestoneResponse,
    Achievement,
    UserAchievement,
    UserMilestone,
    AchievementQuestion,
    AchievementQuestionOption,
    UserAchievementResponse,
    SubscriptionPlan,
    PaymentMethod,
    UserSubscription,
    Notification,
    EducatorQuestion,
    EducatorQuestionFocus,
    EducatorQuestionWtdCategory,
    BookmarkedEducatorQuestion,
    LikedEducatorQuestion,
    ImplementedEducatorQuestion,
    EducatorQuestionContribution,
    EducatorQuestionContributionLike,
    EducatorQuestionView,
  }),

  /**
   * Get specific model
   * @param {string} modelName - Model name
   * @returns {Object} Model
   */
  getModel: (modelName) => {
    const models = databaseService.getModels();
    return models[modelName];
  },
};

module.exports = databaseService;
