openapi: 3.0.0
info:
  title: WTD Platform User Impact API
  version: 1.0.0
  description: API endpoints for accessing user impact and progress data

paths:
  /user/impact:
    get:
      tags:
        - User Impact
      summary: Get User Impact Data
      description: Get user's impact data including activity metrics and milestone progress
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successfully retrieved impact data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImpactResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Impact data not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/impact/achievements:
    get:
      tags:
        - User Impact
      summary: Get User Achievements
      description: Get user's achievements for current milestone including activity metrics and achievement progress
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successfully retrieved achievements data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchievementsResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Achievements data not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/impact/milestones/{milestoneId}/questions:
    get:
      tags:
        - User Impact
      summary: Get milestone questions
      description: Get all questions for a specific milestone
      security:
        - BearerAuth: []
      parameters:
        - name: milestoneId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the milestone
      responses:
        '200':
          $ref: '#/components/responses/MilestoneQuestionsSuccess'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
        - User Impact
      summary: Submit milestone questions
      description: Submit answers for milestone questions. Answers can be either text strings or UUIDs for option-based questions.
      security:
        - BearerAuth: []
      parameters:
        - name: milestoneId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the milestone
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitMilestoneQuestionsRequest'
            example:
              answers: [
                {
                  "questionId": "123e4567-e89b-12d3-a456-************",
                  "answer": "This is my text answer"
                },
                {
                  "questionId": "123e4567-e89b-12d3-a456-************",
                  "answer": "123e4567-e89b-12d3-a456-************"
                }
              ]
      responses:
        '200':
          description: Successfully submitted answers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitMilestoneQuestionsResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/impact/milestones/{milestoneId}/move-to-next:
    post:
      tags:
        - User Impact
      summary: Move to next milestone
      description: Moves the user to the next milestone after completing the current one
      security:
        - BearerAuth: []
      parameters:
        - name: milestoneId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the current milestone
      responses:
        '200':
          description: Successfully moved to next milestone
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MoveToNextMilestoneResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          description: Milestone not found or no next milestone available
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/impact/achievements/{achievementId}/questions:
    get:
      tags:
        - User Impact
      summary: Get achievement questions
      description: Get all questions for a specific achievement, including options for each question.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/AchievementIdParam'
      responses:
        '200':
          $ref: '#/components/responses/MilestoneQuestionsSuccess'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
        - User Impact
      summary: Submit achievement questions
      description: Submit answers for achievement questions. Answers can be either text strings or UUIDs for option-based questions.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/AchievementIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitMilestoneQuestionsRequest'
            examples:
              default:
                $ref: '#/components/examples/SubmitAchievementQuestionsExample'
      responses:
        '200':
          description: Successfully submitted answers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitMilestoneQuestionsResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'


components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ActivityMetrics:
      type: object
      properties:
        insightsShared:
          type: integer
          description: Number of insights shared by the user
          example: 5
        insightsLiked:
          type: integer
          description: Number of insights liked by the user
          example: 10
        experiencesCreated:
          type: integer
          description: Number of experiences created by the user
          example: 3
        contributionsMade:
          type: integer
          description: Number of contributions made by the user
          example: 7

    Achievement:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the achievement
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: Name of the achievement
          example: "Share First Insight"
        isCompleted:
          type: boolean
          description: Whether the achievement has been completed
          example: true
        targetValue:
          type: integer
          description: Target value required to complete the achievement
          example: 1
        progress:
          type: integer
          description: Current progress percentage towards completion
          example: 100
        completedAt:
          type: string
          format: date-time
          description: Date and time when the achievement was completed
          example: "2024-03-15T10:30:00Z"
          nullable: true

    Milestone:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "Level 1"
        isCompleted:
          type: boolean
          example: false
        formSubmission:
          type: boolean
          description: Whether this milestone requires form submission
          example: true

    AchievementsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Achievements retrieved successfully"
        data:
          type: object
          properties:
            activityMetrics:
              $ref: '#/components/schemas/ActivityMetrics'
            completedAchievements:
              type: integer
              description: Number of completed achievements
              example: 3
            totalAchievements:
              type: integer
              description: Total number of achievements
              example: 5
            achievements:
              type: array
              items:
                $ref: '#/components/schemas/Achievement'

    ImpactResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Impact data retrieved successfully"
        data:
          type: object
          properties:
            activityMetrics:
              $ref: '#/components/schemas/ActivityMetrics'
            currentMilestone:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                  example: "123e4567-e89b-12d3-a456-************"
                name:
                  type: string
                  example: "Level 1"
                icon:
                  type: string
                  example: "star"
                quote:
                  type: string
                  example: "Every journey begins with a single step"
                quoteAuthor:
                  type: string
                  example: "Lao Tzu"
                isCurrent:
                  type: boolean
                  example: true
                isCompleted:
                  type: boolean
                  description: Whether all achievements in this milestone are completed
                  example: false
                congratulationsMessage:
                  type: string
                  description: Congratulation message for the milestone
                  example: "Congratulations on reaching this milestone! Your dedication to growth is inspiring."
            nextMilestone:
              type: object
              description: Information about the next milestone to achieve
              properties:
                id:
                  type: string
                  description: Unique identifier of the next milestone
                  example: "milestone-123"
                name:
                  type: string
                  description: Name of the next milestone
                  example: "Growth Catalyst"
                achievements:
                  type: array
                  description: List of achievements required for this milestone
                  items:
                    $ref: '#/components/schemas/Achievement'
                formSubmission:
                  type: boolean
                  description: Indicates if this milestone requires form submission
                  example: true
            totalMilestones:
              type: integer
              description: Total number of milestones
              example: 5
            completedMilestones:
              type: integer
              description: Number of completed milestones
              example: 2
            milestones:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                    example: "123e4567-e89b-12d3-a456-************"
                  name:
                    type: string
                    example: "Level 1"
                  isCompleted:
                    type: boolean
                    example: false
                  isCurrent:
                    type: boolean
                    example: true
                  progress:
                    type: integer
                    example: 60

    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          example: 400
        error:
          type: string
          example: "Bad Request"
        message:
          type: string
          example: "Invalid request parameters"
        errors:
          type: string
          example: "Detailed error message"

    SuccessResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Operation successful"
        data:
          type: object
          description: Response data (varies by endpoint)

    MilestoneQuestion:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier of the question
          example: "question-123"
        milestoneId:
          type: string
          description: ID of the milestone this question belongs to
          example: "milestone-123"
        question:
          type: string
          description: The question text
          example: "What was your biggest learning from this milestone?"
        type:
          type: string
          description: Type of the question (e.g., 'multiple_choice', 'text')
          example: "multiple_choice"
        isRequired:
          type: boolean
          description: Whether this question must be answered
          example: true
        order:
          type: integer
          description: Display order of the question
          example: 1
        options:
          type: array
          description: Available options for the question (if applicable)
          items:
            $ref: '#/components/schemas/MilestoneQuestionOption'

    MilestoneQuestionOption:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier of the option
          example: "option-123"
        questionId:
          type: string
          description: ID of the question this option belongs to
          example: "question-123"
        text:
          type: string
          description: The option text
          example: "I learned how to better manage my time"
        order:
          type: integer
          description: Display order of the option
          example: 1

    SubmitMilestoneQuestionsRequest:
      type: object
      required:
        - answers
      properties:
        answers:
          type: array
          description: Array of question answers
          items:
            type: object
            required:
              - questionId
              - answer
            properties:
              questionId:
                type: string
                format: uuid
                description: ID of the question
              answer:
                type: string
                description: Answer can be either a text string or a UUID for option-based questions

    SubmitMilestoneQuestionsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Questions submitted successfully"
        data:
          type: object
          properties:
            milestoneId:
              type: string
              format: uuid
              example: "123e4567-e89b-12d3-a456-************"

    NextMilestoneData:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "bac7dbd0-1fe8-4ed8-9c19-275bad915e33"
        name:
          type: string
          example: "WTD Contributor"
        formSubmission:
          type: boolean
          example: false

    MoveToNextMilestoneResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Successfully moved to WTD Contributor"
        data:
          $ref: '#/components/schemas/NextMilestoneData'

    MilestoneQuestionsResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Milestone questions retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/MilestoneQuestion'

  parameters:
    AchievementIdParam:
      name: achievementId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the achievement

  responses:
    MilestoneQuestionsSuccess:
      description: Successfully retrieved milestone questions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/MilestoneQuestionsResponse'

    BadRequestError:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 400
            error: "Bad Request"
            message: "Invalid request parameters"
            errors: "Detailed error message"

    UnauthorizedError:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 401
            error: "Unauthorized"
            message: "Authentication required"
            errors: "Please login to access this resource"

    NotFoundError:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 404
            error: "Not Found"
            message: "Resource not found"
            errors: "The requested resource does not exist"

    InternalServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 500
            error: "Internal Server Error"
            message: "An unexpected error occurred"
            errors: "Server error details"

  examples:
    SubmitAchievementQuestionsExample:
      summary: Example submission for achievement questions
      value:
        answers:
          - questionId: "123e4567-e89b-12d3-a456-************"
            response: "This is my text answer"
          - questionId: "123e4567-e89b-12d3-a456-************"
            response: "123e4567-e89b-12d3-a456-************"
            selectedOptionId: "123e4567-e89b-12d3-a456-************" 