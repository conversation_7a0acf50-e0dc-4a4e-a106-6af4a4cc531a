openapi: 3.0.0
info:
  title: WTD Platform Onboarding Config API
  version: 1.0.0
  description: API endpoints for managing onboarding screen configurations

paths:
  /admin/onboarding-config:
    get:
      tags:
        - Onboarding Config
      summary: List All Onboarding Configs
      description: Get a list of all onboarding configs with pagination, search, and filtering by user type.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
        - $ref: '#/components/parameters/UserTypeParam'
      responses:
        '200':
          description: Successfully retrieved onboarding configs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      tags:
        - Onboarding Config
      summary: Create Onboarding Config
      description: Create a new onboarding screen configuration.
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOnboardingConfigRequest'
      responses:
        '201':
          description: Successfully created onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /admin/onboarding-config/{id}:
    get:
      tags:
        - Onboarding Config
      summary: Get Onboarding Config by ID
      description: Retrieve a single onboarding configuration by its UUID.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/OnboardingIdParam'
      responses:
        '200':
          description: Successfully retrieved onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - Onboarding Config
      summary: Update Onboarding Config
      description: Update an existing onboarding screen configuration.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/OnboardingIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOnboardingConfigRequest'
      responses:
        '200':
          description: Successfully updated onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - Onboarding Config
      summary: Delete Onboarding Config
      description: Delete an onboarding configuration by its UUID.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/OnboardingIdParam'
      responses:
        '200':
          description: Successfully deleted onboarding config
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Onboarding config deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 400
              error:
                type: string
                example: Bad Request
              message:
                type: string
                example: Invalid request parameters

    Unauthorized:
      description: Unauthorized access
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 401
              error:
                type: string
                example: Unauthorized
              message:
                type: string
                example: Authentication required

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 404
              error:
                type: string
                example: Not Found
              message:
                type: string
                example: Resource not found

  parameters:
    OnboardingIdParam:
      name: id
      in: path
      required: true
      description: The UUID of the onboarding config.
      schema:
        type: string
        format: uuid
    PageParam:
      name: page
      in: query
      required: false
      description: The page number for pagination.
      schema:
        type: integer
        default: 1
    LimitParam:
      name: limit
      in: query
      required: false
      description: The number of items per page.
      schema:
        type: integer
        default: 10
    SearchParam:
      name: search
      in: query
      required: false
      description: A search term to filter results by title or description.
      schema:
        type: string
    UserTypeParam:
      name: userType
      in: query
      required: false
      description: Filter by user type.
      schema:
        $ref: '#/components/schemas/UserType'

  schemas:
    UserType:
      type: string
      enum:
        - EDUCATOR
        - EDUCATOR_PLUS
        - PROVIDER_PLUS
      description: The type of user.

    OnboardingConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
        imageUrl:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        userType:
          $ref: '#/components/schemas/UserType'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateOnboardingConfigRequest:
      type: object
      required:
        - userType
      properties:
        title:
          type: string
          description: Title of the onboarding screen
          example: "Welcome to WTD!"
        description:
          type: string
          description: Description text for the onboarding screen
          example: "Get started with our platform."
        imageUrl:
          type: string
          description: URL of the image to display
          example: "https://example.com/welcome.jpg"
        userType:
          $ref: '#/components/schemas/UserType'

    UpdateOnboardingConfigRequest:
      type: object
      properties:
        title:
          type: string
          description: Updated title of the onboarding screen
          example: "Welcome to WTD!"
        description:
          type: string
          description: Updated description text for the onboarding screen
          example: "Welcome to the WTD Platform"
        imageUrl:
          type: string
          description: Updated URL of the image to display
          example: "https://example.com/new-welcome.jpg"
        userType:
          $ref: '#/components/schemas/UserType'

    OnboardingConfigResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding config created successfully"
        data:
          $ref: '#/components/schemas/OnboardingConfig'

    OnboardingConfigListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding configs retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/OnboardingConfig'
        pagination:
          $ref: '#/components/schemas/Pagination'

    DeleteResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding config deleted successfully"

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 12
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 2
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: true
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false 