'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('AchievementQuestion', 'userType', {
      type: Sequelize.STRING,
      allowNull: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Down migration would require recreating the ENUM, which is not recommended for production data.
    // You may want to throw an error or leave this as a no-op.
  },
};
