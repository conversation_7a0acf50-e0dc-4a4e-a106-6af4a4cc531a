'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('MilestoneQuestionOption', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      questionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'MilestoneQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      value: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      label: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      isCorrect: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
    await queryInterface.addIndex('MilestoneQuestionOption', ['questionId'], {
      name: 'milestone_question_option_question_id_idx',
    });
    await queryInterface.addIndex('MilestoneQuestionOption', ['order'], {
      name: 'milestone_question_option_order_idx',
    });
    await queryInterface.addIndex('MilestoneQuestionOption', ['isCorrect'], {
      name: 'milestone_question_option_is_correct_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'MilestoneQuestionOption',
      'milestone_question_option_is_correct_idx'
    );
    await queryInterface.removeIndex(
      'MilestoneQuestionOption',
      'milestone_question_option_order_idx'
    );
    await queryInterface.removeIndex(
      'MilestoneQuestionOption',
      'milestone_question_option_question_id_idx'
    );
    await queryInterface.dropTable('MilestoneQuestionOption');
  },
};
