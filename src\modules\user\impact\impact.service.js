/**
 * User Impact Service
 *
 * Handles business logic for user impact and progress tracking
 */
const impactRepository = require('@models/repositories/impact.repository');
const achievementQuestionsRepository = require('@models/repositories/achievement-questions.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { IMPACT } = require('@utils/messages.utils');
const achievementService = require('@modules/user/experience/services/achievement.service');

/**
 * Impact service
 */
const impactService = {
  /**
   * Get user's impact data including activity metrics and milestones
   * @param {Object} user - User object from request
   * @returns {Promise<Object>} User's impact data
   */
  getImpact: async (user) => {
    try {
      // Get user's activity metrics
      const activityMetrics =
        await impactRepository.getUserActivityMetrics(user);

      // Get user's milestones with progress
      const milestones = await impactRepository.getMilestonesWithProgress(user);

      return {
        userType: user.userType,
        activityMetrics,
        ...milestones,
      };
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  },

  /**
   * Get user's achievements for current milestone
   * @param {Object} user - User object from request
   * @returns {Promise<Object>} User's achievements data
   */
  getAchievements: async (user) => {
    try {
      const result = await impactRepository.getAchievementsWithProgress(user);
      if (result.achievements && Array.isArray(result.achievements)) {
        // Add hasQuestions to each achievement
        const achievementsWithQuestions = await Promise.all(
          result.achievements.map(async (achievement) => ({
            ...achievement,
            hasQuestions:
              await achievementQuestionsRepository.hasAchievementQuestions(
                achievement.id
              ),
          }))
        );
        return { ...result, achievements: achievementsWithQuestions };
      }
      return result;
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  },

  /**
   * Get milestone questions with options
   * @param {string} milestoneId - ID of the milestone
   * @returns {Promise<Array>} List of questions with their options
   */
  getMilestoneQuestions: async (milestoneId) => {
    try {
      return await impactRepository.getMilestoneQuestions(milestoneId);
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  },

  /**
   * Get achievement questions with options
   * @param {string} achievementId - ID of the achievement
   * @returns {Promise<Array>} List of questions with their options
   */
  getAchievementQuestions: async (achievementId) => {
    try {
      return await achievementQuestionsRepository.getAchievementQuestions(
        achievementId
      );
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  },

  /**
   * Move to next milestone
   * @param {string} userId - ID of the user
   * @param {string} milestoneId - ID of the current milestone
   */
  async moveToNextMilestone(userId, milestoneId) {
    return impactRepository.moveToNextMilestone(userId, milestoneId);
  },

  /**
   * Submit milestone questions
   * @param {string} userId - ID of the user
   * @param {string} milestoneId - ID of the milestone
   * @param {Array<Object>} answers - Array of question answers
   */
  async submitMilestoneQuestions(userId, milestoneId, answers) {
    try {
      return await impactRepository.submitMilestoneQuestions(
        userId,
        milestoneId,
        answers
      );
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || IMPACT.RETRIEVAL_FAILED
      );
    }
  },

  /**
   * Submit achievement questions
   * @param {string} userId - ID of the user
   * @param {string} achievementId - ID of the achievement
   * @param {Array<Object>} answers - Array of question answers
   */
  async submitAchievementQuestions(userId, achievementId, answers) {
    try {
      const result =
        await achievementQuestionsRepository.submitAchievementResponses(
          userId,
          achievementId,
          answers
        );

      // Fetch the achievement to get its type
      const achievement =
        await achievementQuestionsRepository.models.Achievement.findOne({
          where: { id: achievementId, isActive: true },
        });

      if (achievement) {
        await achievementService.updateAchievementProgress(
          userId,
          achievement.achievementType
        );
      }
      return result;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = impactService;
