/**
 * Contribution Repository
 * Handles database operations for contributions and contribution likes
 */
const databaseService = require('@config/database.config');
const commonRepository = require('./common.repository');
const { PAGINATION } = require('@utils/constants');
const { REPORT } = require('@utils/messages.utils');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');

class BaseRepository {
  constructor() {
    this.models = {
      Contribution: databaseService.getContributionModel(),
      ContributionLike: databaseService.getContributionLikeModel(),
      User: databaseService.getUserModel(),
      Insight: databaseService.getInsightModel(),
    };
  }

  _getContributionAttributes(userId = null) {
    const { sequelize } = databaseService.getContributionModel();
    const attributes = { include: [] };
    // likesCount is always included, cast as INTEGER
    attributes.include.push([
      sequelize.literal(`
        (SELECT COUNT(*)::INTEGER FROM "ContributionLike"
         WHERE "ContributionLike"."contributionId" = "Contribution"."id")
      `),
      'likesCount',
    ]);
    if (userId) {
      attributes.include.push(
        [
          sequelize.literal(`
            EXISTS (
              SELECT 1 FROM "ContributionReport"
              WHERE "ContributionReport"."contributionId" = "Contribution"."id"
                AND "ContributionReport"."reportedBy" = '${userId}'
            )
          `),
          'isReported',
        ],
        [
          sequelize.literal(`
            EXISTS (
              SELECT 1 FROM "ContributionLike"
              WHERE "ContributionLike"."contributionId" = "Contribution"."id"
                AND "ContributionLike"."likedBy" = '${userId}'
            )
          `),
          'isLiked',
        ]
      );
    }
    return attributes;
  }

  _getContributionIncludes() {
    return [
      {
        model: this.models.User,
        as: 'contributor',
        attributes: ['id', 'firstName', 'lastName', 'email', 'profilePic'],
      },
    ];
  }
}

class ContributionRepository extends BaseRepository {
  constructor() {
    super();
  }

  /**
   * Find contribution by ID without filters or associations
   * @param {string} id - Contribution ID
   * @returns {Promise<Object|null>} Contribution or null
   */
  async findByIdRaw(id) {
    try {
      return await this.models.Contribution.findOne({ where: { id } });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find contribution by ID
   * @param {string} id - Contribution ID
   * @param {boolean} includeStats - Include like/dislike stats
   * @returns {Promise<Object|null>} Contribution or null
   */
  async findById(id, includeStats = true) {
    try {
      const includeOptions = [
        {
          model: this.models.User,
          as: 'contributor',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'userType',
            'deviceToken',
          ],
        },
        {
          model: this.models.Insight,
          as: 'insight',
          attributes: ['id', 'insightText'],
        },
      ];

      if (includeStats) {
        includeOptions.push({
          model: this.models.ContributionLike,
          as: 'likes',
          attributes: ['id'],
          required: false,
        });
      }

      return await this.models.Contribution.findOne({
        where: { id, isRemoved: false },
        include: includeOptions,
      });
    } catch (error) {
      throw new Error(`Error finding contribution: ${error.message}`);
    }
  }

  /**
   * Create a new contribution
   * @param {Object} contributionData - Contribution data
   * @returns {Promise<Object>} Created contribution
   */
  async create(contributionData) {
    try {
      const contribution =
        await this.models.Contribution.create(contributionData);
      return await this.findById(contribution.id);
    } catch (error) {
      throw new Error(`Error creating contribution: ${error.message}`);
    }
  }

  /**
   * Toggle like on a contribution
   * @param {string} contributionId - Contribution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Like action result
   */
  async toggleLike(contributionId, userId) {
    try {
      return await this.models.ContributionLike.toggleLike(
        contributionId,
        userId
      );
    } catch (error) {
      throw new Error(`Error toggling contribution like: ${error.message}`);
    }
  }

  /**
   * Get contributions by insight with pagination
   * @param {string} insightId - Insight ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (default: 1)
   * @param {number} options.limit - Items per page (default: 10)
   * @param {string} options.orderBy - Order field (default: 'createdAt')
   * @param {string} options.orderDirection - Order direction (default: 'DESC')
   * @param {string} options.userId - User ID for computed isReported attribute
   * @returns {Promise<Object>} Object with contributions and pagination
   */
  async findByInsight(insightId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        orderBy = 'createdAt',
        orderDirection = 'DESC',
        userId = null,
      } = options;

      const offset = commonRepository.calculateOffset(page, limit);
      const whereClause = { insightId, isRemoved: false };

      const { count, rows } = await this.models.Contribution.findAndCountAll({
        where: whereClause,
        include: this._getContributionIncludes(),
        attributes: this._getContributionAttributes(userId),
        limit: parseInt(limit),
        offset,
        order: [[orderBy, orderDirection.toUpperCase()]],
        distinct: true,
      });

      return {
        contributions: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete a contribution
   * @param {string} id - Contribution ID
   * @returns {Promise<boolean>} True if deleted
   */
  async delete(id) {
    try {
      // Use raw find method without filters
      const contribution = await this.findByIdRaw(id);
      if (!contribution) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          REPORT.CONTRIBUTION_NOT_FOUND
        );
      }

      await contribution.destroy();
      return true;
    } catch (error) {
      throw new Error(`Error deleting contribution: ${error.message}`);
    }
  }

  // /**
  //  * Check if user has already contributed to any insight from a different provider
  //  * @param {Object} insight - Insight object
  //  * @param {string} userId - User ID
  //  * @returns {Promise<boolean>} True if user has already contributed to any insight from a different provider
  //  */
  // async hasUserContributed(insight, userId) {
  //   try {
  //     // Check if user has contributed to any insight from this provider
  //     const contributionCount = await this.models.Contribution.count({
  //       include: [
  //         {
  //           model: this.models.Insight,
  //           as: 'insight',
  //           where: {
  //             createdBy: insight.createdBy,
  //           },
  //           required: true,
  //         },
  //       ],
  //       where: {
  //         contributedBy: userId,
  //       },
  //     });

  //     return contributionCount > 0;
  //   } catch (error) {
  //     throw new Error(`Error checking user contribution: ${error.message}`);
  //   }
  // }

  /**
   * Check if user has already contributed to the given insight
   * @param {Object} insight - Insight object
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} True if user has already contributed to this insight
   */
  async hasUserContributed(insight, userId) {
    try {
      const contributionCount = await this.models.Contribution.count({
        where: {
          contributedBy: userId,
          insightId: insight.id,
          isRemoved: false,
        },
      });

      return contributionCount > 0;
    } catch (error) {
      throw new Error(`Error checking user contribution: ${error.message}`);
    }
  }

  /**
   * Soft remove a contribution (admin only)
   * @param {string} id - Contribution ID
   * @returns {Promise<Object>} Updated contribution
   */
  async softRemoveContribution(id) {
    try {
      // Use raw find method without filters
      const contribution = await this.findByIdRaw(id);
      if (!contribution) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          REPORT.CONTRIBUTION_NOT_FOUND
        );
      }

      await contribution.update({
        isRemoved: true,
        removedAt: new Date(),
      });

      return contribution;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ContributionRepository();
