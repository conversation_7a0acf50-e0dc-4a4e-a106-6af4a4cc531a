/**
 * Experience Discussion Like Model
 * Represents likes on experience discussions
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceDiscussionLike extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        discussionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceDiscussion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        likedBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceDiscussionLike',
        tableName: 'ExperienceDiscussionLike',
        timestamps: true,
        indexes: [
          {
            fields: ['discussionId'],
            name: 'experience_discussion_like_discussion_id_idx',
          },
          {
            fields: ['likedBy'],
            name: 'experience_discussion_like_liked_by_idx',
          },
          {
            fields: ['discussionId', 'likedBy'],
            name: 'experience_discussion_like_unique_idx',
            unique: true,
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Experience Discussion
    this.belongsTo(models.ExperienceDiscussion, {
      foreignKey: 'discussionId',
      as: 'discussion',
      onDelete: 'CASCADE',
    });

    // Belongs to User (liker)
    this.belongsTo(models.User, {
      foreignKey: 'likedBy',
      as: 'liker',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = ExperienceDiscussionLike;
