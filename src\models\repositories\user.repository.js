/**
 * User Repository
 *
 * Handles data access operations for the User model
 */
const { ApiException } = require('@utils/exception.utils');
const { USER, AUTH } = require('@utils/messages.utils');
const {
  UserType,
  ExperienceEnrollmentStatus,
  SubscriptionType,
  SubscriptionStatus,
  AccountStatus,
} = require('@utils/enums.utils');
const databaseService = require('@config/database.config');
const { HttpStatus } = require('@utils/enums.utils');
const { VALIDATION } = require('@utils/messages.utils');
const { Op } = require('sequelize');
const impactRepository = require('./impact.repository');
const commonRepository = require('./common.repository');
const { PAGINATION } = require('@utils/constants');
const { SUBSCRIPTION_PLAN } = require('@utils/messages.utils');
const stripeSubscription = require('@integrations/stripe/stripe.subscription');
const userSubscriptionRepository = require('./user-subscription.repository');
const { USER: USER_MESSAGES } = require('@utils/messages.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      User: databaseService.getUserModel(),
      Focus: databaseService.getFocusModel(),
      UserFocus: databaseService.getUserFocusModel(),
      Contribution: databaseService.getContributionModel(),
      Insight: databaseService.getInsightModel(),
      Experience: databaseService.getExperienceModel(),
      WtdIceBreaker: databaseService.getWtdIceBreakerModel(),
      Follow: databaseService.getFollowModel(),
      ExperienceEnrollment: databaseService.getExperienceEnrollmentModel(),
      UserSubscription: databaseService.getUserSubscriptionModel(),
      SubscriptionPlan: databaseService.getSubscriptionPlanModel(),
    };
  }

  _adminGetUserListIncludes() {
    return [
      {
        association: 'focuses',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        association: 'iceBreaker',
        attributes: [
          'teachSubject',
          'contentIn3Words',
          'inspiration',
          'funFact',
          'magicButton',
          'resourcesEmojis',
          'themeSong',
          'impactTeacher',
          'isPrivate',
          'favoriteTeachingMemory',
          'classroomDescription',
          'favoriteClassroomPart',
          'coworkerDescription',
          'classroomExperience',
          'essentialSupply',
          'funniestStudentComment',
          'bestAdviceFromTeacher',
          'dreamFieldTrip',
        ],
      },
      {
        association: 'subscriptions',
        where: {
          status: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING],
        },
        required: false,
        attributes: [
          'id',
          'status',
          'currentPeriodStart',
          'currentPeriodEnd',
          'type',
        ],
        include: [
          {
            association: 'plan',
            attributes: [
              'id',
              'name',
              'description',
              'price',
              'billingInterval',
              'trialDays',
              'insightsLimit',
              'insightsLimitPeriod',
              'targetUserType',
              'canRegisterExperiences',
            ],
          },
        ],
      },
    ];
  }
}

/**
 * Repository for user validation operations
 */
class UserValidationRepository extends BaseRepository {
  async validateFocusIds(focusIds, transaction) {
    if (focusIds?.length > 0) {
      const validFocuses = await this.models.Focus.findAll({
        where: {
          id: {
            [Op.in]: focusIds,
          },
        },
        attributes: ['id'],
        transaction,
        raw: true,
      });

      const validFocusIdSet = new Set(validFocuses.map((f) => f.id));
      const invalidFocusIds = focusIds.filter((id) => !validFocusIdSet.has(id));

      if (invalidFocusIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          VALIDATION.INVALID_FOCUS_ID
        );
      }
    }
  }
}

/**
 * Repository for user update operations
 */
class UserUpdateRepository extends BaseRepository {
  async updateUserFields(user, updateData, transaction) {
    const {
      credential: {
        companyName,
        website,
        state,
        country,
        description,
        isPrivate,
      } = {},
      departmentImpact: { isPrivate: isDepartmentPrivate } = {},
      ...userData
    } = updateData;

    const updateFields = {
      ...userData,
      companyName,
      website,
      state,
      country,
      description,
      isPrivate,
      isDepartmentPrivate,
      school: updateData.school,
      district: updateData.district,
      educationCareerStartDate: updateData.educationCareerStartDate,
      subject: updateData.subject,
    };

    return user.update(updateFields, { transaction });
  }

  async upsertIceBreaker(userId, iceBreakers, transaction) {
    if (!iceBreakers) return null;

    const iceBreakerData = {
      userId,
      teachSubject: iceBreakers.teachSubject,
      contentIn3Words: iceBreakers.contentIn3Words,
      inspiration: iceBreakers.inspiration,
      funFact: iceBreakers.funFact,
      magicButton: iceBreakers.magicButton,
      resourcesEmojis: iceBreakers.resourcesEmojis,
      themeSong: iceBreakers.themeSong,
      impactTeacher: iceBreakers.impactTeacher,
      isPrivate: iceBreakers.isPrivate,
      favoriteTeachingMemory: iceBreakers.favoriteTeachingMemory,
      classroomDescription: iceBreakers.classroomDescription,
      favoriteClassroomPart: iceBreakers.favoriteClassroomPart,
      coworkerDescription: iceBreakers.coworkerDescription,
      classroomExperience: iceBreakers.classroomExperience,
      essentialSupply: iceBreakers.essentialSupply,
      funniestStudentComment: iceBreakers.funniestStudentComment,
      bestAdviceFromTeacher: iceBreakers.bestAdviceFromTeacher,
      dreamFieldTrip: iceBreakers.dreamFieldTrip,
    };

    return this.models.WtdIceBreaker.upsert(iceBreakerData, {
      transaction,
      conflictFields: ['userId'],
    });
  }

  async updateUserFocus(userId, focusIds, transaction) {
    if (focusIds === undefined) return null;

    // Remove existing focus associations
    await this.models.UserFocus.destroy({
      where: { userId },
      transaction,
    });

    // Create new focus associations if provided
    if (focusIds?.length > 0) {
      const focusAssociations = focusIds.map((focusId) => ({
        userId,
        focusId,
      }));

      return this.models.UserFocus.bulkCreate(focusAssociations, {
        transaction,
        ignoreDuplicates: true,
      });
    }

    return null;
  }
}

/**
 * User Helper class for complex operations
 */
class UserHelper extends BaseRepository {
  async createFocusAssociations(userId, focusIds, transaction) {
    if (focusIds?.length > 0) {
      const focusRecords = focusIds.map((focusId) => ({
        userId,
        focusId,
      }));
      await this.models.UserFocus.bulkCreate(focusRecords, {
        transaction,
        ignoreDuplicates: true,
      });
    }
  }

  async getProfileData(user) {
    const [
      contributionsCount,
      followersCount,
      followingCount,
      experiencesCreatedCount,
      experiencesCompletedCount,
      insightsCreatedCount,
    ] = await Promise.all([
      this.getContributionsCount(user.id),
      this.getFollowersCount(user.id),
      this.getFollowingCount(user.id),
      this.getExperiencesCreatedCount(user.id),
      this.getExperiencesCompletedCount(user.id),
      this.getInsightsCreatedCount(user.id),
    ]);

    const { currentMilestone } =
      await impactRepository.getMilestonesWithProgress(user);

    return {
      id: user.id,
      profilePic: user.profilePic,
      firstName: user.firstName,
      lastName: user.lastName,
      userType: user.userType,
      position: user.position,
      currentMilestone: currentMilestone.name,
      school: user.school,
      district: user.district,
      educationCareerStartDate: user.educationCareerStartDate,
      subject: user.subject,
      credential: {
        companyName: user.companyName,
        website: user.website,
        focus: user.focuses,
        state: user.state,
        country: user.country,
        description: user.description,
        isPrivate: user.isPrivate,
      },
      departmentImpact: {
        currentMilestone: currentMilestone.name,
        contributions: contributionsCount,
        followers: followersCount,
        following: followingCount,
        insightCreated: insightsCreatedCount,
        experiencesCreated: experiencesCreatedCount,
        experiencesCompleted: experiencesCompletedCount,
        isPrivate: user.isDepartmentPrivate,
      },
      iceBreakers: user.iceBreaker,
    };
  }

  async getContributionsCount(userId) {
    try {
      return await this.models.Contribution.count({
        where: { contributedBy: userId },
      });
    } catch (error) {
      console.error('Error in getContributionsCount:', error);
      throw error;
    }
  }

  async getFollowingCount(userId) {
    try {
      return await this.models.Follow.count({
        where: { followerId: userId },
      });
    } catch (error) {
      console.error('Error in getFollowingCount:', error);
      throw error;
    }
  }

  async getExperiencesCompletedCount(userId) {
    try {
      return await this.models.ExperienceEnrollment.count({
        where: { userId: userId, status: ExperienceEnrollmentStatus.COMPLETED },
      });
    } catch (error) {
      console.error('Error in getExperiencesCompletedCount:', error);
      throw error;
    }
  }

  async getInsightsCreatedCount(userId) {
    try {
      return await this.models.Insight.count({
        where: { createdBy: userId },
      });
    } catch (error) {
      console.error('Error in getInsightsCreatedCount:', error);
      throw error;
    }
  }

  async getExperiencesCreatedCount(userId) {
    try {
      return await this.models.Experience.count({
        where: { createdBy: userId },
      });
    } catch (error) {
      console.error('Error in getExperiencesCreatedCount:', error);
      throw error;
    }
  }

  async getFollowersCount(userId) {
    try {
      return await this.models.Follow.count({
        where: { followingId: userId },
      });
    } catch (error) {
      console.error('Error in getFollowersCount:', error);
      throw error;
    }
  }
}

/**
 * Main User Repository class
 */
class UserRepository extends BaseRepository {
  constructor() {
    super();
    this.validationRepository = new UserValidationRepository();
    this.updateRepository = new UserUpdateRepository();
    this.helper = new UserHelper();
  }

  /**
   * Get user profile includes for queries
   * @returns {Array} Array of include objects
   */
  _getUserProfileIncludes() {
    return [
      {
        association: 'focuses',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        association: 'iceBreaker',
        attributes: [
          'teachSubject',
          'contentIn3Words',
          'inspiration',
          'funFact',
          'magicButton',
          'resourcesEmojis',
          'themeSong',
          'impactTeacher',
          'isPrivate',
          'favoriteTeachingMemory',
          'classroomDescription',
          'favoriteClassroomPart',
          'coworkerDescription',
          'classroomExperience',
          'essentialSupply',
          'funniestStudentComment',
          'bestAdviceFromTeacher',
          'dreamFieldTrip',
        ],
      },
    ];
  }

  /**
   * Find user by ID
   * @param {string} id - User UUID
   * @param {Object} options - Query options
   * @returns {Promise<User>} User instance
   * @throws {ApiException} If user not found
   */
  async findById(id, options = {}) {
    try {
      const user = await this.models.User.findByPk(id, options);

      if (!user) {
        throw new ApiException(404, USER.USER_NOT_FOUND);
      }

      return user;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find user by email
   * @param {string} email - User email
   * @returns {Promise<User|null>} User instance or null
   */
  async findByEmail(email) {
    try {
      return await this.models.User.findOne({ where: { email } });
    } catch (error) {
      console.error('Error in findByEmail repository:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<User>} Created user
   * @throws {ApiException} If email already exists
   */
  async create(userData) {
    try {
      const existingUser = await this.findByEmail(userData.email);

      if (existingUser) {
        throw new ApiException(409, AUTH.EMAIL_ALREADY_EXISTS);
      }

      return await this.models.User.create(userData);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update user
   * @param {string} id - User UUID
   * @param {Object} updateData - Data to update
   * @returns {Promise<User>} Updated user
   * @throws {ApiException} If user not found
   */
  async update(id, updateData) {
    try {
      const user = await this.findById(id);
      await user.update(updateData);
      return user;
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete user
   * @param {string} id - User UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If user not found
   */
  async delete(id) {
    try {
      const user = await this.findById(id);
      await user.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }

  /**
   * Change user password
   * @param {string} id - User UUID
   * @param {string} newPassword - New password
   * @returns {Promise<User>} Updated user
   * @throws {ApiException} If user not found
   */
  async changePassword(id, newPassword) {
    try {
      const user = await this.models.User.findByPk(id);

      if (!user) {
        throw new ApiException(404, USER.USER_NOT_FOUND);
      }

      user.password = newPassword;
      await user.save();
      return user;
    } catch (error) {
      console.error('Error in changePassword repository:', error);
      throw error;
    }
  }

  /**
   * Create user with focus associations
   * @param {Object} requestBody - User data including focus array
   * @returns {Promise<Object>} Created user with focus associations
   */
  async createUserWithFocus(requestBody) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Validate all focus IDs exist if focus array is provided
      await this.validationRepository.validateFocusIds(
        requestBody.focus,
        transaction
      );

      // Create user with modified data for PROVIDER_PLUS
      const user = await this.create(
        {
          ...requestBody,
          position:
            requestBody.userType === UserType.PROVIDER_PLUS
              ? null
              : requestBody.position,
          companyName:
            requestBody.userType === UserType.PROVIDER_PLUS
              ? requestBody.companyName
              : null,
        },
        { transaction }
      );

      // Create focus associations if focus array exists
      await this.helper.createFocusAssociations(
        user.id,
        requestBody.focus,
        transaction
      );

      // Commit the transaction
      await transaction.commit();

      // Return user without focus associations
      return this.findById(user.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get user profile data with all necessary associations and counts
   * @param {string} userId - User UUID
   * @returns {Promise<Object>} Formatted user profile data
   */
  async getUserProfileData(userId) {
    try {
      const user = await this.findById(userId, {
        include: this._getUserProfileIncludes(),
      });

      return await this.helper.getProfileData(user);
    } catch (error) {
      console.error('Error in getUserProfileData repository:', error);
      throw error;
    }
  }

  /**
   * Update user profile with optimized operations
   * @param {string} userId - User UUID
   * @param {Object} updateData - Profile update data
   * @returns {Promise<Object>} Updated user profile data
   */
  async updateProfile(userId, updateData) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Extract focus from updateData for separate handling
      const {
        credential: { focus, ...credentialFields } = {},
        iceBreakers,
        ...restUpdateData
      } = updateData;

      // Reconstruct updateData with credential fields but without focus
      const updateDataForFields = {
        ...restUpdateData,
        credential: credentialFields,
      };

      // Validate focus IDs if provided
      if (focus !== undefined) {
        await this.validationRepository.validateFocusIds(focus, transaction);
      }

      // Get user for update
      const user = await this.findById(userId, { transaction });

      // Prepare parallel update operations
      const updateOperations = [];

      // 1. Update user fields
      updateOperations.push(
        this.updateRepository.updateUserFields(
          user,
          updateDataForFields,
          transaction
        )
      );

      // 2. Upsert ice breaker if provided
      if (iceBreakers) {
        updateOperations.push(
          this.updateRepository.upsertIceBreaker(
            userId,
            iceBreakers,
            transaction
          )
        );
      }

      // 3. Update user focus if provided
      if (focus !== undefined) {
        updateOperations.push(
          this.updateRepository.updateUserFocus(userId, focus, transaction)
        );
      }

      // Execute all updates in parallel
      await Promise.all(updateOperations.filter(Boolean));
      await transaction.commit();

      // Return updated user profile data
      return this.getUserProfileData(userId);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Find user by reset token
   * @param {string} token - Reset token
   * @returns {Promise<User|null>} User instance or null
   */
  async findByResetToken(token) {
    try {
      return await this.models.User.findOne({
        where: {
          resetToken: token,
          resetTokenExpiry: {
            [Op.gt]: new Date(), // greater than current time
          },
        },
      });
    } catch (error) {
      console.error('Error in findByResetToken repository:', error);
      throw error;
    }
  }

  async updateAccountStatus(userId, status) {
    if (!AccountStatus.isValid(status)) {
      throw new ApiException(
        HttpStatus.BAD_REQUEST,
        USER_MESSAGES.INVALID_ACCOUNT_STATUS
      );
    }
    return this.models.User.update(
      { accountStatus: status },
      { where: { id: userId } }
    );
  }
}

// Insert AdminUserHelper class here
class AdminUserHelper {
  constructor(models) {
    this.models = models;
  }

  async switchUserSubscription(userId, updateData, transaction) {
    const { subscriptionPlanId, currentPeriodStart, currentPeriodEnd } =
      updateData;
    try {
      const shouldSwitchSubscription =
        subscriptionPlanId && currentPeriodStart && currentPeriodEnd;
      if (!shouldSwitchSubscription) return;

      // Cancel current active subscription if exists
      const currentSub =
        await userSubscriptionRepository.getActiveSubscription(userId);
      if (currentSub) {
        if (currentSub.stripeSubscriptionId) {
          await stripeSubscription.cancelImmediately(
            currentSub.stripeSubscriptionId
          );
        }
        await userSubscriptionRepository.updateSubscription(currentSub.id, {
          status: SubscriptionStatus.CANCELED,
          canceledAt: new Date(),
        });
      }
      // Always create offline subscription in DB
      await this.models.UserSubscription.create(
        {
          userId,
          subscriptionPlanId,
          type: SubscriptionType.OFFLINE,
          status: SubscriptionStatus.ACTIVE,
          currentPeriodStart,
          currentPeriodEnd,
        },
        { transaction }
      );
    } catch (error) {
      throw error;
    }
  }

  async getUserWithFocuses(userId) {
    const user = await this.models.User.findByPk(userId, {
      attributes: {
        exclude: ['resetToken', 'resetTokenExpiry'],
      },
      include: [
        {
          association: 'focuses',
          attributes: ['id', 'name'],
          through: { attributes: [] },
        },
        {
          association: 'iceBreaker',
          attributes: [
            'teachSubject',
            'contentIn3Words',
            'inspiration',
            'funFact',
            'magicButton',
            'resourcesEmojis',
            'themeSong',
            'impactTeacher',
            'isPrivate',
            'favoriteTeachingMemory',
            'classroomDescription',
            'favoriteClassroomPart',
            'coworkerDescription',
            'classroomExperience',
            'essentialSupply',
            'funniestStudentComment',
            'bestAdviceFromTeacher',
            'dreamFieldTrip',
          ],
        },
        {
          association: 'subscriptions',
          where: { status: 'ACTIVE' },
          required: false,
          attributes: [
            'id',
            'status',
            'currentPeriodStart',
            'currentPeriodEnd',
            'type',
          ],
          include: [
            {
              association: 'plan',
              attributes: [
                'id',
                'name',
                'description',
                'price',
                'billingInterval',
                'trialDays',
                'insightsLimit',
                'insightsLimitPeriod',
                'targetUserType',
                'canRegisterExperiences',
              ],
            },
          ],
        },
      ],
    });
    if (!user) {
      throw new ApiException(404, USER.USER_NOT_FOUND);
    }
    const userObj = user.toJSON();
    if (Array.isArray(userObj.subscriptions)) {
      userObj.subscription = userObj.subscriptions[0] || null;
      delete userObj.subscriptions;
    }
    return userObj;
  }

  async validateFocusIds(focusIds, transaction) {
    try {
      if (focusIds?.length > 0) {
        const validFocuses = await this.models.Focus.findAll({
          where: {
            id: {
              [Op.in]: focusIds,
            },
          },
          attributes: ['id'],
          transaction,
          raw: true,
        });

        const validFocusIdSet = new Set(validFocuses.map((f) => f.id));
        const invalidFocusIds = focusIds.filter(
          (id) => !validFocusIdSet.has(id)
        );

        if (invalidFocusIds.length > 0) {
          throw new ApiException(
            HttpStatus.BAD_REQUEST,
            VALIDATION.INVALID_FOCUS_ID
          );
        }
      }
    } catch (error) {
      throw error;
    }
  }

  async createFocusAssociations(userId, focusIds, transaction) {
    try {
      if (focusIds?.length > 0) {
        const focusRecords = focusIds.map((focusId) => ({
          userId,
          focusId,
        }));
        await this.models.UserFocus.bulkCreate(focusRecords, {
          transaction,
          ignoreDuplicates: true,
        });
      }
    } catch (error) {
      throw error;
    }
  }
}

class AdminUserRepository extends BaseRepository {
  constructor() {
    super();
    this.validationRepository = new UserValidationRepository();
    this.updateRepository = new UserUpdateRepository();
    this.helper = new UserHelper();
    this.adminHelper = new AdminUserHelper(this.models);
  }

  /**
   * Create a new user as admin (with focus associations and optional subscription)
   */
  async createUserWithSubscription(userData) {
    const { subscriptionPlanId, currentPeriodStart, currentPeriodEnd } =
      userData;
    const transaction = await databaseService.getSequelize().transaction();
    try {
      await this.adminHelper.validateFocusIds(userData.focus, transaction);
      const user = await this.models.User.create(userData, { transaction });
      await this.adminHelper.createFocusAssociations(
        user.id,
        userData.focus,
        transaction
      );

      if (subscriptionPlanId && currentPeriodStart && currentPeriodEnd) {
        // Check if the subscription plan exists
        const plan = await this.models.SubscriptionPlan.findByPk(
          subscriptionPlanId,
          { transaction }
        );
        if (!plan) {
          throw new ApiException(
            HttpStatus.NOT_FOUND,
            SUBSCRIPTION_PLAN.NOT_FOUND
          );
        }
        await this.models.UserSubscription.create(
          {
            userId: user.id,
            subscriptionPlanId,
            type: SubscriptionType.OFFLINE,
            status: SubscriptionStatus.ACTIVE,
            currentPeriodStart,
            currentPeriodEnd,
          },
          { transaction }
        );
      }
      await transaction.commit();
      return await this.adminHelper.getUserWithFocuses(user.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update an existing user as admin
   */
  async updateUser(userId, updateData) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Check for email uniqueness if email is being updated
      if (updateData.email) {
        const existingUser = await this.models.User.findOne({
          where: {
            email: updateData.email,
            id: { [Op.ne]: userId },
          },
          transaction,
        });
        if (existingUser) {
          throw new ApiException(
            HttpStatus.CONFLICT,
            AUTH.EMAIL_ALREADY_EXISTS
          );
        }
      }
      const user = await this.models.User.findByPk(userId, { transaction });
      if (!user) {
        throw new ApiException(HttpStatus.NOT_FOUND, USER.USER_NOT_FOUND);
      }

      // Remove subscription fields from updateData so they don't update the user table
      const {
        subscriptionPlanId: _,
        currentPeriodStart: __,
        currentPeriodEnd: ___,
        ...userFields
      } = updateData;
      await user.update(userFields, { transaction });

      if (updateData.focus) {
        await this.adminHelper.validateFocusIds(updateData.focus, transaction);
        await this.adminHelper.createFocusAssociations(
          userId,
          updateData.focus,
          transaction
        );
      }

      await this.adminHelper.switchUserSubscription(
        userId,
        updateData,
        transaction
      );

      await transaction.commit();

      return await this.adminHelper.getUserWithFocuses(user.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * List users as admin (with optional filters, pagination, and search)
   * Returns { data: usersWithSingleSubscription, pagination }
   */
  async listUsers(
    filters,
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT
  ) {
    const { search, userType, createdVia, accountStatus } = filters;
    try {
      let where = {};
      if (search) {
        where = {
          ...where,
          [Op.or]: [
            { firstName: { [Op.iLike]: `%${search}%` } },
            { lastName: { [Op.iLike]: `%${search}%` } },
            { email: { [Op.iLike]: `%${search}%` } },
          ],
        };
      }
      if (userType) {
        where.userType = userType;
      }
      if (createdVia) {
        where.createdVia = createdVia;
      }
      if (accountStatus) {
        where.accountStatus = accountStatus;
      }

      const offset = commonRepository.calculateOffset(page, limit);
      const total = await this.models.User.count({ where });

      const users = await this.models.User.findAll({
        attributes: {
          exclude: ['resetToken', 'resetTokenExpiry'],
        },
        include: this._adminGetUserListIncludes(),
        where,
        offset,
        limit,
        order: [['createdAt', 'DESC']],
      });

      // Map subscriptions array to a single subscription object
      const usersWithSingleSubscription = users.map((user) => {
        const userObj = user.toJSON();
        if (Array.isArray(userObj.subscriptions)) {
          userObj.subscription = userObj.subscriptions[0] || null;
          delete userObj.subscriptions;
        }
        return userObj;
      });

      const pagination = commonRepository.buildPaginationInfo(
        total,
        page,
        limit
      );

      return { data: usersWithSingleSubscription, pagination };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get a user by id with admin includes (focuses, iceBreaker, active subscription as object)
   */
  async getUserById(userId) {
    try {
      const user = await this.models.User.findByPk(userId, {
        attributes: {
          exclude: ['resetToken', 'resetTokenExpiry'],
        },
        include: this._adminGetUserListIncludes(),
      });
      if (!user) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          AUTH_MESSAGES.USER_NOT_FOUND
        );
      }
      const userObj = user.toJSON();
      if (Array.isArray(userObj.subscriptions)) {
        userObj.subscription = userObj.subscriptions[0] || null;
        delete userObj.subscriptions;
      }
      return userObj;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete a user as admin
   */
  async deleteUser(userId) {
    const user = await this.models.User.findByPk(userId);
    if (!user)
      throw new ApiException(HttpStatus.NOT_FOUND, USER.USER_NOT_FOUND);
    await user.destroy();
    return true;
  }
}

module.exports = new UserRepository();
module.exports.AdminUserRepository = new AdminUserRepository();
