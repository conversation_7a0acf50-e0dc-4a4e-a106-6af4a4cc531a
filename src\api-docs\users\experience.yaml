openapi: 3.0.0
info:
  title: WTD Platform User Experience API
  version: 1.0.0
  description: API endpoints for managing experiences as a user

paths:
  /user/experience:
    post:
      tags:
        - User Experience
      summary: Create Experience
      description: Create a new learning experience with weeks, insights, and media
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExperienceRequest'
      responses:
        '201':
          description: Experience created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceResponse'
        '400':
          description: Bad request - Validation errors
        '401':
          description: Unauthorized - User is not authenticated

    get:
      tags:
        - User Experience
      summary: Get All Experiences
      description: Get a list of all experiences with optional filtering
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/UserIdParam'
      responses:
        '200':
          description: Successfully retrieved experiences
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceListResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /user/experience/{experienceId}:
    get:
      tags:
        - User Experience
      summary: Get Experience by ID
      description: Get a specific experience by its ID with full details
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      responses:
        '200':
          description: Successfully retrieved experience
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Experience not found

  /user/experience/{experienceId}/enroll:
    post:
      tags:
        - User Experience
      summary: Enroll in Experience
      description: Enroll the authenticated user in an experience
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEnrollmentRequest'
      responses:
        '201':
          description: User enrolled successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceEnrollmentCreateResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: User already enrolled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/experience/{experienceId}/week/{weekNumber}/complete:
    patch:
      tags:
        - User Experience
      summary: Complete a specific week
      description: Mark a specific week as completed for the enrolled user
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
        - name: weekNumber
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 52
          description: Week number to complete
      responses:
        '200':
          description: Week completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompleteWeekResponse'
        '400':
          description: Bad request - Week not accessible or invalid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Enrollment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/experience/week/{weekId}:
    get:
      tags:
        - User Experience
      summary: Get Week Details
      description: Get detailed information about a specific week including weeklyWhy, insights, and engagement statistics
      security:
        - BearerAuth: []
      parameters:
        - name: weekId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the week to retrieve
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Week details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeekDetailsResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Week not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/experience/{experienceId}/progress:
    get:
      tags:
        - User Experience
      summary: Get enrollment progress
      description: Get detailed progress information for a user's enrollment
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      responses:
        '200':
          description: Enrollment progress retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Enrollment progress retrieved successfully
                  data:
                    type: object
                    properties:
                      weekProgress:
                        type: array
                        items:
                          $ref: '#/components/schemas/ExperienceWeekProgress'
                      stats:
                        $ref: '#/components/schemas/EnrollmentProgress'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Enrollment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/experience/{experienceId}/review:
    post:
      tags:
        - User Experience
      summary: Submit Experience Review
      description: Submit a review for a specific experience. Users can only submit one review per experience.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitReviewRequest'
      responses:
        '201':
          description: Review submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceReviewResponse'
        '400':
          description: Bad request - Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict - User has already reviewed this experience
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    get:
      tags:
        - User Experience
      summary: Get Experience Reviews
      description: Get all reviews for a specific experience with pagination
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved reviews
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceReviewListResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/experience/{experienceId}/discussion:
    post:
      tags:
        - User Experience
      summary: Create Discussion
      description: Create a new discussion for a specific experience
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDiscussionRequest'
      responses:
        '201':
          description: Discussion created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscussionResponse'
        '400':
          description: Bad request - Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    get:
      tags:
        - User Experience
      summary: Get Discussions
      description: Get all discussions for a specific experience with pagination
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved discussions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscussionListResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/experience/discussion/{discussionId}/like:
    post:
      tags:
        - User Experience
      summary: Toggle Discussion Like
      description: Toggle like status for a discussion
      security:
        - BearerAuth: []
      parameters:
        - name: discussionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the discussion
      responses:
        '200':
          description: Like status toggled successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscussionLikeToggleResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Discussion not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/experience/{experienceWeekMediaId}/watch:
    patch:
      tags:
        - User Experience
      summary: Update video watch status
      description: Updates the watch status of a video in an experience week
      security:
        - BearerAuth: []
      parameters:
        - name: experienceWeekMediaId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the experience week media
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWatchStatusRequest'
      responses:
        '200':
          description: Watch status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WatchStatusResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Video not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    ExperienceIdParam:
      name: experienceId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Experience ID

    UserIdParam:
      name: userId
      in: query
      schema:
        type: string
        format: uuid
      description: Filter experiences by a specific user's ID (optional)

  schemas:
    CreateExperienceRequest:
      type: object
      required:
        - title
        - experienceLength
      properties:
        title:
          type: string
          description: Experience title
          example: "Learning React Framework"
        shortDescription:
          type: string
          description: Brief description of the experience
          example: "A comprehensive React learning experience"
        longDescription:
          type: string
          description: Detailed description of the experience
          example: "This experience covers all fundamentals of React development, including components, state, and lifecycle methods."
        experienceLength:
          type: integer
          minimum: 1
          maximum: 52
          description: Duration in weeks
          example: 4
        personalNote:
          type: string
          description: Creator's personal notes
          example: "Built for frontend bootcamps"
        pdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of PD category IDs
          example: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
        wtdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of WTD category IDs
          example: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]
        media:
          type: array
          items:
            $ref: '#/components/schemas/MediaInput'
          description: Experience-level media
          example:
            - type: "VIDEO"
              url: "https://example.com/intro.mp4"
              title: "Intro to React"
        weeks:
          type: array
          items:
            $ref: '#/components/schemas/WeekInput'
          description: Weekly breakdown of the experience
          example:
            - weekNumber: 1
              title: "JSX & Components"
              weeklyWhy: "To understand UI structure in React"
              media:
                - type: "DOCUMENT"
                  url: "https://example.com/week1-notes.pdf"
                  title: "Week 1 Notes"
              insights:
                - text: "JSX combines HTML with JavaScript logic"
                  sourceUrl: "https://reactjs.org/docs/introducing-jsx.html"
                  focusIds: ["29c7c9cc-0d6f-493f-9f02-5a04f1e99999"]
                  pdCategoryIds: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
                  wtdCategoryIds: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]

    MediaInput:
      type: object
      required:
        - type
        - url
      properties:
        type:
          type: string
          enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
          description: Type of media
          example: "IMAGE"
        url:
          type: string
          format: uri
          description: Media URL
          example: "https://example.com/image.jpg"
        title:
          type: string
          description: Media title
          example: "Thumbnail Image"

    WeekInput:
      type: object
      required:
        - weekNumber
        - title
      properties:
        weekNumber:
          type: integer
          minimum: 1
          description: Week number
          example: 2
        title:
          type: string
          description: Week title
          example: "State Management"
        weeklyWhy:
          type: string
          description: Purpose/rationale for the week
          example: "To manage data flow in React apps"
        media:
          type: array
          items:
            $ref: '#/components/schemas/MediaInput'
          description: Week-specific media
        insights:
          type: array
          items:
            $ref: '#/components/schemas/InsightInput'
          maxItems: 5
          description: Weekly insights (max 5)

    InsightInput:
      type: object
      required:
        - text
      properties:
        text:
          type: string
          description: Insight content
          example: "State allows dynamic updates to UI"
        sourceUrl:
          type: string
          format: uri
          description: Optional source URL
          example: "https://reactjs.org/docs/state-and-lifecycle.html"
        focusIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of focus IDs
          example: ["abc123de-0f99-4abc-8df1-12feabcd1234"]
        pdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of PD category IDs
          example: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
        wtdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of WTD category IDs
          example: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]

    # Response Schemas
    PaginationResponse:
      type: object
      properties:
        page:
          type: integer
          description: Current page number (1-based)
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages available
          example: 5
        totalItems:
          type: integer
          description: Total number of items across all pages
          example: 42

    Experience:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the experience
        title:
          type: string
          description: Experience title
        shortDescription:
          type: string
          description: Brief description of the experience
        longDescription:
          type: string
          description: Detailed description of the experience
        experienceLength:
          type: integer
          description: Duration in weeks
        personalNote:
          type: string
          description: Creator's personal notes
        createdBy:
          type: string
          format: uuid
          description: ID of the user who created the experience
        creator:
          $ref: '#/components/schemas/UserRef'
        media:
          type: array
          items:
            $ref: '#/components/schemas/Media'
          description: Experience-level media
        pdCategories:
          type: array
          items:
            $ref: '#/components/schemas/PdCategoryRef'
          description: Associated PD categories
        wtdCategories:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategoryRef'
          description: Associated WTD categories
        weeks:
          type: array
          items:
            $ref: '#/components/schemas/ExperienceWeek'
          description: Weekly breakdown of the experience
        createdAt:
          type: string
          format: date-time
          description: Date and time when the experience was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the experience was last updated
      example:
        id: "123e4567-e89b-12d3-a456-************"
        title: "Learning React Framework"
        shortDescription: "A comprehensive React learning experience"
        longDescription: "This experience covers all fundamentals of React development, including components, state, and lifecycle methods."
        experienceLength: 4
        personalNote: "Built for frontend bootcamps"
        createdBy: "123e4567-e89b-12d3-a456-426614174010"
        creator:
          id: "123e4567-e89b-12d3-a456-426614174010"
          firstName: "Jane"
          lastName: "Smith"
          email: "<EMAIL>"
        media:
          - id: "123e4567-e89b-12d3-a456-************"
            type: "VIDEO"
            url: "https://example.com/intro.mp4"
            title: "Intro to React"
            order: 1
        pdCategories:
          - id: "b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"
            name: "Frontend Development"
        wtdCategories:
          - id: "f3a5b90a-1f0a-4723-95c9-91b90e400001"
            name: "Technology"
        weeks: []
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    ExperienceWeek:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the week
        experienceId:
          type: string
          format: uuid
          description: ID of the parent experience
        weekNumber:
          type: integer
          description: Week number
        title:
          type: string
          description: Week title
        weeklyWhy:
          type: string
          description: Purpose/rationale for the week
        media:
          type: array
          items:
            $ref: '#/components/schemas/WeekMedia'
          description: Week-specific media
        insights:
          type: array
          items:
            $ref: '#/components/schemas/ExperienceInsight'
          description: Weekly insights
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      example:
        id: "123e4567-e89b-12d3-a456-************"
        experienceId: "123e4567-e89b-12d3-a456-************"
        weekNumber: 1
        title: "JSX & Components"
        weeklyWhy: "To understand UI structure in React"
        media: []
        insights: []
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    Media:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the media
        experienceId:
          type: string
          format: uuid
          description: ID of the parent experience
        type:
          type: string
          enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
          description: Type of media
        url:
          type: string
          format: uri
          description: Media URL
        title:
          type: string
          description: Media title
        order:
          type: integer
          description: Display order
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      example:
        id: "123e4567-e89b-12d3-a456-************"
        experienceId: "123e4567-e89b-12d3-a456-************"
        type: "VIDEO"
        url: "https://example.com/intro.mp4"
        title: "Intro to React"
        order: 1
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    WeekMedia:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the week media
        experienceWeekId:
          type: string
          format: uuid
          description: ID of the parent week
        type:
          type: string
          enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
          description: Type of media
        url:
          type: string
          format: uri
          description: Media URL
        title:
          type: string
          description: Media title
        order:
          type: integer
          description: Display order
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ExperienceInsight:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the insight
        experienceWeekId:
          type: string
          format: uuid
          description: ID of the parent week
        order:
          type: integer
          description: Display order within the week
        text:
          type: string
          description: Insight content
        sourceUrl:
          type: string
          format: uri
          description: Optional source URL
        focuses:
          type: array
          items:
            $ref: '#/components/schemas/FocusRef'
          description: Associated focus areas
        pdCategories:
          type: array
          items:
            $ref: '#/components/schemas/PdCategoryRef'
          description: Associated PD categories
        wtdCategories:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategoryRef'
          description: Associated WTD categories
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UserRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email

    PdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    WtdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    FocusRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    Pagination:
      type: object
      properties:
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 1
        totalItems:
          type: integer
          description: Total number of items
          example: 3
        itemsPerPage:
          type: integer
          description: Number of items per page
          example: 10
        hasNextPage:
          type: boolean
          description: Whether there is a next page
          example: false
        hasPrevPage:
          type: boolean
          description: Whether there is a previous page
          example: false

    ExperienceResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Experience retrieved successfully"
        data:
          $ref: '#/components/schemas/Experience'

    ExperienceListResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "All experiences retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Experience'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ExperienceEnrollment:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Enrollment ID
        experienceId:
          type: string
          format: uuid
          description: Experience ID
        userId:
          type: string
          format: uuid
          description: User ID
        startDate:
          type: string
          format: date
          description: Start date
        status:
          type: string
          enum: [REGISTERED, COMPLETED]
          description: Enrollment status
        currentWeek:
          type: integer
          description: Current week number the user is on
          example: 1
        completedWeeks:
          type: integer
          description: Total number of weeks completed
          example: 0
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
        experience:
          $ref: '#/components/schemas/Experience'
        user:
          $ref: '#/components/schemas/UserRef'
      example:
        id: "d58a2ea7-1294-4798-85af-7bcd621ccd05"
        currentWeek: 1
        completedWeeks: 0
        experienceId: "f49522f3-2fd9-41c7-b1bf-c43bbebc6a28"
        userId: "4277cd39-9168-4032-8ee1-ce051bd039dc"
        startDate: "2025-06-02"
        status: "REGISTERED"
        updatedAt: "2025-06-04T13:02:47.068Z"
        createdAt: "2025-06-04T13:02:47.068Z"

    ExperienceEnrollmentCreateResponse:
      type: object
      properties:
        status:
          type: integer
          example: 201
        message:
          type: string
          example: "User enrolled successfully"
        data:
          type: object
          properties:
            id:
              type: string
              format: uuid
              example: "d58a2ea7-1294-4798-85af-7bcd621ccd05"
            currentWeek:
              type: integer
              example: 1
            completedWeeks:
              type: integer
              example: 0
            experienceId:
              type: string
              format: uuid
              example: "f49522f3-2fd9-41c7-b1bf-c43bbebc6a28"
            userId:
              type: string
              format: uuid
              example: "4277cd39-9168-4032-8ee1-ce051bd039dc"
            startDate:
              type: string
              format: date
              example: "2025-06-02"
            status:
              type: string
              enum: [REGISTERED, COMPLETED]
              example: "REGISTERED"
            updatedAt:
              type: string
              format: date-time
              example: "2025-06-04T13:02:47.068Z"
            createdAt:
              type: string
              format: date-time
              example: "2025-06-04T13:02:47.068Z"

    ExperienceEnrollmentListResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Enrollments retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/ExperienceEnrollment'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: "Error message"
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                description: Field name that caused the error
              message:
                type: string
                description: Error message for the field

    CreateEnrollmentRequest:
      type: object
      required:
        - startDate
      properties:
        startDate:
          type: string
          format: date-time
          description: Start date (must be a Monday)
          example: "2025-06-02T00:00:00.000Z"

    SubmitReviewRequest:
      type: object
      required:
        - courseRating
        - providerRating
      properties:
        courseRating:
          type: number
          format: float
          minimum: 1.0
          maximum: 5.0
          description: Rating for the course content from 1.0 to 5.0
          example: 4.5
        providerRating:
          type: number
          format: float
          minimum: 1.0
          maximum: 5.0
          description: Rating for the experience provider from 1.0 to 5.0
          example: 4.0
        reviewText:
          type: string
          description: Optional detailed review text
          example: "This experience was incredibly valuable and well-structured."

    ExperienceReview:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Review ID
        experienceId:
          type: string
          format: uuid
          description: Experience ID
        userId:
          type: string
          format: uuid
          description: User ID
        courseRating:
          type: number
          format: float
          minimum: 1.0
          maximum: 5.0
          description: Rating for the course content from 1.0 to 5.0
        providerRating:
          type: number
          format: float
          minimum: 1.0
          maximum: 5.0
          description: Rating for the experience provider from 1.0 to 5.0
        reviewText:
          type: string
          description: Review text
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
        user:
          $ref: '#/components/schemas/UserRef'
        experience:
          $ref: '#/components/schemas/Experience'

    ExperienceReviewResponse:
      type: object
      properties:
        status:
          type: integer
          example: 201
        message:
          type: string
          example: "Experience review submitted successfully"
        data:
          type: object
          properties:
            id:
              type: string
              format: uuid
              example: "8027077e-2713-4cef-b91a-8520dff23ef2"
            experienceId:
              type: string
              format: uuid
              example: "b1220c4b-e963-4c5d-9553-7cfbc9dd044e"
            userId:
              type: string
              format: uuid
              example: "d50caaeb-db27-403a-92c8-144c4add7498"
            courseRating:
              type: number
              format: float
              minimum: 1.0
              maximum: 5.0
              example: 4.5
            providerRating:
              type: number
              format: float
              minimum: 1.0
              maximum: 5.0
              example: 4.0
            reviewText:
              type: string
              example: "This experience was incredibly valuable and well-structured."
            updatedAt:
              type: string
              format: date-time
              example: "2025-05-30T12:41:20.266Z"
            createdAt:
              type: string
              format: date-time
              example: "2025-05-30T12:41:20.266Z"

    ExperienceReviewListResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Reviews retrieved successfully"
        data:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
                example: "8027077e-2713-4cef-b91a-8520dff23ef2"
              experienceId:
                type: string
                format: uuid
                example: "b1220c4b-e963-4c5d-9553-7cfbc9dd044e"
              userId:
                type: string
                format: uuid
                example: "d50caaeb-db27-403a-92c8-144c4add7498"
              courseRating:
                type: number
                format: float
                minimum: 1.0
                maximum: 5.0
                example: 4.5
              providerRating:
                type: number
                format: float
                minimum: 1.0
                maximum: 5.0
                example: 4.0
              reviewText:
                type: string
                example: "This experience was incredibly valuable and well-structured."
              createdAt:
                type: string
                format: date-time
                example: "2025-05-30T12:41:20.266Z"
              updatedAt:
                type: string
                format: date-time
                example: "2025-05-30T12:41:20.266Z"
              user:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                    example: "d50caaeb-db27-403a-92c8-144c4add7498"
                  firstName:
                    type: string
                    example: "John"
                  lastName:
                    type: string
                    example: "Doe"
                  email:
                    type: string
                    format: email
                    example: "<EMAIL>"
                  profilePic:
                    type: string
                    nullable: true
                    example: null
        pagination:
          type: object
          properties:
            total:
              type: integer
              example: 2
            page:
              type: integer
              example: 1
            limit:
              type: integer
              example: 10
            totalPages:
              type: integer
              example: 1
            hasNext:
              type: boolean
              example: false
            hasPrevious:
              type: boolean
              example: false

    CreateDiscussionRequest:
      type: object
      required:
        - content
      properties:
        content:
          type: string
          description: The content of the discussion
          minLength: 1
          maxLength: 1000

    DiscussionResponse:
      type: object
      properties:
        status:
          type: integer
          example: 201
        message:
          type: string
          example: "Discussion created successfully"
        data:
          type: object
          properties:
            id:
              type: string
              format: uuid
              example: "1c9ead0e-c4b9-4ca8-a89e-9ede51568fc9"
            content:
              type: string
              example: "Hii"
            experienceId:
              type: string
              format: uuid
              example: "77d4eb08-4328-4965-af0a-7eefff42f061"
            createdBy:
              type: string
              format: uuid
              example: "1a8b55f2-026b-49ae-b2b3-7efe58cdf678"
            updatedAt:
              type: string
              format: date-time
              example: "2025-06-04T07:52:40.471Z"
            createdAt:
              type: string
              format: date-time
              example: "2025-06-04T07:52:40.471Z"

    DiscussionListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Discussions retrieved successfully"
        data:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
                example: "cf53ca07-75a1-4566-ae27-57df955a0413"
              content:
                type: string
                example: "Hii"
              createdAt:
                type: string
                format: date-time
                example: "2025-06-04T07:55:12.994Z"
              updatedAt:
                type: string
                format: date-time
                example: "2025-06-04T07:55:12.994Z"
              isLiked:
                type: boolean
                example: false
              likeCount:
                type: string
                example: "0"
              creator:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                    example: "1a8b55f2-026b-49ae-b2b3-7efe58cdf678"
                  firstName:
                    type: string
                    example: "Emily"
                  lastName:
                    type: string
                    example: "Garcia"
                  email:
                    type: string
                    format: email
                    example: "<EMAIL>"
                  profilePic:
                    type: string
                    nullable: true
                    example: null
        total:
          type: integer
          example: 2
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 10
        totalPages:
          type: integer
          example: 1
        hasNext:
          type: boolean
          example: false
        hasPrevious:
          type: boolean
          example: false

    DiscussionLikeToggleResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Discussion like status toggled successfully"
        data:
          type: object
          properties:
            isLiked:
              type: boolean
              example: true

    ExperienceWeekProgress:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Week progress ID
        enrollmentId:
          type: string
          format: uuid
          description: Enrollment ID
        experienceWeekId:
          type: string
          format: uuid
          description: Experience week ID
        weekNumber:
          type: integer
          description: Week number
          example: 1
        status:
          type: string
          enum: [PENDING, COMPLETED]
          description: Week completion status
          example: PENDING
        completedAt:
          type: string
          format: date-time
          nullable: true
          description: Date when week was completed
        experienceWeek:
          type: object
          properties:
            id:
              type: string
              format: uuid
            weekNumber:
              type: integer
            title:
              type: string
            weeklyWhy:
              type: string
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp

    EnrollmentProgress:
      type: object
      properties:
        totalWeeks:
          type: integer
          description: Total number of weeks in experience
          example: 4
        completedWeeks:
          type: integer
          description: Number of completed weeks
          example: 2
        currentWeek:
          type: integer
          description: Current week number
          example: 3
        progressPercentage:
          type: integer
          description: Completion percentage
          example: 50
        isCompleted:
          type: boolean
          description: Whether all weeks are completed
          example: false

    CompleteWeekResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: Week completed successfully
        data:
          type: object
          properties:
            enrollment:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                  example: "d58a2ea7-1294-4798-85af-7bcd621ccd05"
                experienceId:
                  type: string
                  format: uuid
                  example: "f49522f3-2fd9-41c7-b1bf-c43bbebc6a28"
                userId:
                  type: string
                  format: uuid
                  example: "4277cd39-9168-4032-8ee1-ce051bd039dc"
                startDate:
                  type: string
                  format: date
                  example: "2025-06-02"
                status:
                  type: string
                  enum: [REGISTERED, COMPLETED]
                  example: "COMPLETED"
                currentWeek:
                  type: integer
                  example: 2
                completedWeeks:
                  type: integer
                  example: 1
                createdAt:
                  type: string
                  format: date-time
                  example: "2025-06-04T13:02:47.068Z"
                updatedAt:
                  type: string
                  format: date-time
                  example: "2025-06-04T13:12:48.898Z"
            progress:
              type: object
              properties:
                totalWeeks:
                  type: integer
                  example: 1
                completedWeeks:
                  type: integer
                  example: 1
                currentWeek:
                  type: integer
                  example: 2
                progressPercentage:
                  type: integer
                  example: 100
                isCompleted:
                  type: boolean
                  example: true

    WeekDetailsResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: Week details retrieved successfully
        data:
          $ref: '#/components/schemas/WeekDetails'
        pagination:
          $ref: '#/components/schemas/PaginationResponse'

    WeekDetails:
      type: object
      properties:
        weeklyWhy:
          type: string
          description: The purpose and motivation for this week
          example: "This week focuses on understanding the core principles of effective communication"
        insights:
          type: array
          items:
            $ref: '#/components/schemas/InsightResponse'
        engagementStats:
          $ref: '#/components/schemas/WeekEngagementStats'

    WeekEngagementStats:
      type: object
      properties:
        totalViews:
          type: integer
          description: Total number of views for this week's content
          example: 150
        completedCount:
          type: integer
          description: Number of users who completed this week
          example: 45
        averageTimeSpent:
          type: number
          description: Average time spent on this week's content in minutes
          example: 120.5

    UpdateWatchStatusRequest:
      type: object
      properties:
        watchDuration:
          type: integer
          minimum: 0
          description: Duration watched in seconds
        isCompleted:
          type: boolean
          description: Whether the video was watched completely

    WatchStatus:
      type: object
      properties:
        id:
          type: string
          format: uuid
        experienceWeekMediaId:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        watchDuration:
          type: integer
        isCompleted:
          type: boolean
        lastWatchedAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    WatchStatusResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Watch status updated successfully"
        data:
          $ref: '#/components/schemas/WatchStatus'
