/**
 * AchievementQuestionOption Model
 * Represents options for achievement questions
 */
const { Model, DataTypes } = require('sequelize');

class AchievementQuestionOption extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        questionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'AchievementQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        value: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        label: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
        isCorrect: {
          type: DataTypes.BOOLEAN,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'AchievementQuestionOption',
        tableName: 'AchievementQuestionOption',
        timestamps: true,
        indexes: [
          {
            fields: ['questionId'],
            name: 'achievement_question_option_question_id_idx',
          },
          {
            fields: ['order'],
            name: 'achievement_question_option_order_idx',
          },
          {
            fields: ['isCorrect'],
            name: 'achievement_question_option_is_correct_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to achievement question
    AchievementQuestionOption.belongsTo(models.AchievementQuestion, {
      foreignKey: 'questionId',
      as: 'question',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = AchievementQuestionOption;
