/**
 * Application Configuration
 */
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// Environment
const ENV = process.env.NODE_ENV || 'development';
const IS_PROD = ENV === 'production';
const IS_DEV = ENV === 'development';
const IS_TEST = ENV === 'test';
const IS_LOCAL = ENV === 'local';

// Application
const APP = {
  env: ENV,
  port: process.env.PORT || 3002,
  apiPrefix: '', // Removed '/api/v1' prefix
  jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
};

// Database
const DB = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_NAME || 'wtd_platform',
  dialect: 'postgres',
  logging: false, // Suppress all SQL logs
  define: {
    timestamps: true,
    underscored: false,
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};

// Enums
const PlatformType = {
  WEB: 'web',
  MOBILE: 'mobile',
  API: 'api',
  isValid: (platform) =>
    [PlatformType.WEB, PlatformType.MOBILE, PlatformType.API].includes(
      platform
    ),
};

const UserRole = {
  ADMIN: 'admin',
  USER: 'user',
};

// Swagger options
const SWAGGER = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'WTD Platform',
      version: '1.0.0',
      description: 'WTD Platform API',
    },
    servers: [
      {
        url: `http://localhost:${APP.port}${APP.apiPrefix}`,
        description: 'Development server',
      },
    ],
  },
  apis: [
    path.join(__dirname, '../docs/swagger/**/*.yaml'),
    path.join(__dirname, '../modules/**/*.js'),
  ],
};

/**
 * Configuration Service
 */
const configService = {
  /**
   * Get environment variable value
   * @param {string} key - Environment variable key
   * @param {*} defaultValue - Default value if key is not found
   * @returns {*} Configuration value
   */
  get: (key, defaultValue = null) => {
    return process.env[key] || defaultValue;
  },

  /**
   * Get database configuration
   * @returns {Object} Database configuration
   */
  getDatabase: () => DB,

  /**
   * Get application configuration
   * @returns {Object} Application configuration
   */
  getApp: () => APP,

  /**
   * Get environment
   * @returns {string} Current environment
   */
  getEnv: () => ENV,
};

// Export all configuration
module.exports = {
  ENV,
  IS_PROD,
  IS_DEV,
  IS_TEST,
  IS_LOCAL,
  APP,
  DB,
  PlatformType,
  UserRole,
  SWAGGER,
  configService,
};
