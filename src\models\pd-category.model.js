/**
 * PdCategory Model
 * Represents professional development categories
 */
const { Model, DataTypes } = require('sequelize');

class PdCategory extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
          // Explicitly preserve the original case
          set(value) {
            this.setDataValue('name', value);
          },
        },
      },
      {
        sequelize,
        modelName: 'PdCategory',
        tableName: 'PdCategory',
        timestamps: true,
        indexes: [
          // Create a unique index on the lowercase version of the name
          {
            unique: true,
            fields: [sequelize.fn('lower', sequelize.col('name'))],
            name: 'pd_category_name_lower_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    this.hasMany(models.Insight, {
      foreignKey: 'pdCategoryId',
      as: 'insights',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
    });

    this.hasMany(models.EducatorQuestion, {
      foreignKey: 'pdCategoryId',
      as: 'educatorQuestions',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = PdCategory;
