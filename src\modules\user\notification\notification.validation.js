const { param, query } = require('express-validator');

const notificationValidation = {
  // List notifications validation
  list: [
    query('limit')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Limit must be a positive integer'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
  ],

  // Read notification validation
  read: [param('id').isUUID().withMessage('Invalid notification ID format')],
};

module.exports = notificationValidation;
