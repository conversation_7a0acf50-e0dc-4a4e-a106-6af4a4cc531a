/**
 * EducatorQuestionView Model
 * Represents views of educator questions
 */
const { Model, DataTypes } = require('sequelize');

class EducatorQuestionView extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        educatorQuestionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'EducatorQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'SET NULL',
        },
        ipAddress: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        userAgent: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'EducatorQuestionView',
        tableName: 'EducatorQuestionView',
        timestamps: true,
        indexes: [
          {
            fields: ['educatorQuestionId'],
            name: 'educator_question_view_question_id_idx',
          },
          {
            fields: ['userId'],
            name: 'educator_question_view_user_id_idx',
          },
          {
            fields: ['createdAt'],
            name: 'educator_question_view_created_at_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to educator question
    this.belongsTo(models.EducatorQuestion, {
      foreignKey: 'educatorQuestionId',
      as: 'educatorQuestion',
      onDelete: 'CASCADE',
    });

    // Belongs to user (optional)
    this.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'SET NULL',
    });
  }
}

module.exports = EducatorQuestionView;
