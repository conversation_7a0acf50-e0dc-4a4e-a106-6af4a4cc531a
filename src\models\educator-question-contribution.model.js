/**
 * EducatorQuestionContribution Model
 * Represents contributions to educator questions
 */
const { Model, DataTypes } = require('sequelize');

class EducatorQuestionContribution extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        educatorQuestionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'EducatorQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        content: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        isRemoved: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        removedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'EducatorQuestionContribution',
        tableName: 'EducatorQuestionContribution',
        timestamps: true,
        indexes: [
          {
            fields: ['educatorQuestionId'],
            name: 'educator_question_contribution_question_id_idx',
          },
          {
            fields: ['userId'],
            name: 'educator_question_contribution_user_id_idx',
          },
          {
            fields: ['isRemoved'],
            name: 'educator_question_contribution_is_removed_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to educator question
    this.belongsTo(models.EducatorQuestion, {
      foreignKey: 'educatorQuestionId',
      as: 'educatorQuestion',
      onDelete: 'CASCADE',
    });

    // Belongs to user
    this.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    // Has many likes
    this.hasMany(models.EducatorQuestionContributionLike, {
      foreignKey: 'contributionId',
      as: 'likes',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = EducatorQuestionContribution;
