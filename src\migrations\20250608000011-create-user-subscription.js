'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('UserSubscription', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      subscriptionPlanId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'SubscriptionPlan',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      stripeSubscriptionId: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      currentPeriodStart: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      currentPeriodEnd: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      cancelAtPeriodEnd: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      canceledAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      trialStart: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      trialEnd: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      paymentMethodId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'PaymentMethod',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex('UserSubscription', ['userId'], {
      name: 'user_subscription_user_id_idx',
    });
    await queryInterface.addIndex('UserSubscription', ['subscriptionPlanId'], {
      name: 'user_subscription_plan_id_idx',
    });
    await queryInterface.addIndex('UserSubscription', ['status'], {
      name: 'user_subscription_status_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex(
      'UserSubscription',
      'user_subscription_user_id_idx'
    );
    await queryInterface.removeIndex(
      'UserSubscription',
      'user_subscription_plan_id_idx'
    );
    await queryInterface.removeIndex(
      'UserSubscription',
      'user_subscription_status_idx'
    );

    // Drop the table
    await queryInterface.dropTable('UserSubscription');
  },
};
