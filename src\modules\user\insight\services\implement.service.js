/**
 * Implement Service
 *
 * Handles business logic for implementing insights
 */
const implementedInsightRepository = require('@models/repositories/implemented-insight.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { AchievementType } = require('@utils/enums.utils');
const userRepository = require('@repositories/user.repository');
const insightRepository = require('@models/repositories/insight.repository');
const { notifyInsightImplemented } = require('./notification.service');

/**
 * Implement Service
 */
const implementService = {
  /**
   * Toggle implement status for an insight
   * @param {string} userId - User ID
   * @param {string} insightId - Insight ID
   * @returns {Promise<Object>} Object with isImplemented status
   */
  toggleImplement: async (userId, insightId) => {
    try {
      const result = await implementedInsightRepository.toggleImplement(
        userId,
        insightId
      );

      // Update achievement progress based on implementation status
      await achievementService.updateAchievementProgress(
        userId,
        AchievementType.INSIGHT_IMPLEMENTED,
        result.isImplemented ? 1 : -1
      );

      // Notify the insight creator if implemented
      if (result.isImplemented) {
        const implementer = await userRepository.findById(userId);
        const insight = await insightRepository.findById(insightId);
        await notifyInsightImplemented({ implementer, insight });
      }

      return result;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = implementService;
