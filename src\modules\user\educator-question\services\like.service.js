/**
 * Like Service for EducatorQuestion
 * Handles business logic for liking educator questions
 */
const educatorQuestionRepository = require('@models/repositories/educator-question.repository');
const { ApiException } = require('@utils/exception.utils');
const { EDUCATOR_QUESTION } = require('@utils/messages.utils');

const likeService = {
  /**
   * Toggle like status for an educator question
   * @param {Object} user - User object
   * @param {string} educatorQuestionId - EducatorQuestion ID
   * @returns {Promise<Object>} Object with isLiked status
   */
  toggleLike: async (user, educatorQuestionId) => {
    // 1. Check if the educator question exists
    const question = await educatorQuestionRepository.findById(
      educatorQuestionId,
      user.id
    );
    if (!question) {
      throw new ApiException(404, EDUCATOR_QUESTION.NOT_FOUND);
    }
    // 2. Toggle like
    const result = await educatorQuestionRepository.toggleLike(
      user.id,
      educatorQuestionId
    );
    // 3. (Optional) Add achievement/notification logic here
    return result;
  },
};

module.exports = likeService;
