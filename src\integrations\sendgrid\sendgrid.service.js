const sgMail = require('@sendgrid/mail');
const EMAIL_TEMPLATES = require('./sendgrid.templates');

class SendGridService {
  constructor() {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  }

  /**
   * Send email using SendGrid template
   * @param {Object} options - Email options
   * @param {string} options.to - Recipient email address
   * @param {string} options.templateId - Template ID from EMAIL_TEMPLATES
   * @param {Object} options.dynamicTemplateData - Dynamic data for the template
   * @returns {Promise<void>}
   */
  async sendEmail({ to, templateId, dynamicTemplateData }) {
    try {
      const msg = {
        to,
        from: process.env.SENDGRID_FROM_EMAIL,
        templateId,
        dynamicTemplateData,
      };

      await sgMail.send(msg);
    } catch (error) {
      console.error('SendGrid Error:', error);
      if (error.response) {
        console.error(error.response.body);
      }
      throw error;
    }
  }

  /**
   * Send forgot password email
   * @param {string} to - Recipient email address
   * @param {Object} dynamicTemplateData - Dynamic template data for the email
   * @returns {Promise<void>}
   */
  async sendForgotPasswordEmail(to, dynamicTemplateData) {
    return this.sendEmail({
      to,
      templateId: EMAIL_TEMPLATES.FORGOT_PASSWORD,
      dynamicTemplateData,
    });
  }

  /**
   * Send set password email (for admin-created users)
   * @param {string} to - Recipient email address
   * @param {Object} dynamicTemplateData - Dynamic template data for the email
   * @returns {Promise<void>}
   */
  async sendSetPasswordEmail(to, dynamicTemplateData) {
    return this.sendEmail({
      to,
      templateId: EMAIL_TEMPLATES.SET_PASSWORD,
      dynamicTemplateData,
    });
  }
}

module.exports = new SendGridService();
