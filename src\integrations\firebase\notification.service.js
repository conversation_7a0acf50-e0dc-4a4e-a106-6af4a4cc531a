const admin = require('@config/firebase-admin.config');
const Logger = require('@utils/logger.utils');

/**
 * Sends a push notification to a device using Firebase Cloud Messaging (FCM).
 * @param {Object} params
 * @param {string} params.deviceToken - The FCM device token.
 * @param {string} params.title - Notification title.
 * @param {string} params.body - Notification body.
 * @param {Object} params.data - Optional data payload to send with notification.
 * @returns {Promise<Object>} FCM response or error
 */
async function sendPushNotification({ deviceToken, title, body, data = {} }) {
  if (!deviceToken) {
    Logger.warn('No deviceToken provided for push notification');
    return { error: 'No deviceToken provided' };
  }

  // FCM requires all data values to be strings
  const stringifiedData = {};
  for (const key in data) {
    if (typeof data[key] === 'object') {
      stringifiedData[key] = JSON.stringify(data[key]);
    } else {
      stringifiedData[key] = String(data[key]);
    }
  }

  const message = {
    token: deviceToken,
    notification: {
      title,
      body,
    },
    data: stringifiedData,
    android: {
      priority: 'high',
    },
    apns: {
      payload: {
        aps: {
          alert: { title, body },
          sound: 'default',
        },
      },
    },
    webpush: {
      notification: {
        title,
        body,
      },
      data: stringifiedData,
    },
  };

  // Add log before sending
  Logger.info('[sendPushNotification] Sending push notification', message);

  try {
    const response = await admin.messaging().send(message);
    Logger.info('Push notification sent', { deviceToken, response });
    return response;
  } catch (error) {
    Logger.error('Error sending push notification', error);
    return { error: error.message };
  }
}

/**
 * Sends a silent (data-only) push notification to a device using Firebase Cloud Messaging (FCM).
 * @param {Object} params
 * @param {string} params.deviceToken - The FCM device token.
 * @param {Object} params.data - The data payload to send.
 * @returns {Promise<Object>} FCM response or error
 */
async function sendSilentNotification({ deviceToken, data }) {
  if (!deviceToken) {
    Logger.warn('No deviceToken provided for silent push notification');
    return { error: 'No deviceToken provided' };
  }

  // FCM requires all data values to be strings
  const stringifiedData = {};
  for (const key in data || {}) {
    if (typeof data[key] === 'object') {
      stringifiedData[key] = JSON.stringify(data[key]);
    } else {
      stringifiedData[key] = String(data[key]);
    }
  }

  const message = {
    token: deviceToken,
    data: stringifiedData,
    android: {
      priority: 'high',
    },
    apns: {
      payload: {
        aps: {
          'content-available': 1,
        },
      },
    },
    webpush: {},
  };

  try {
    const response = await admin.messaging().send(message);
    Logger.info('Silent push notification sent', { deviceToken, response });
    return response;
  } catch (error) {
    Logger.error('Error sending silent push notification', error);
    return { error: error.message };
  }
}

module.exports = {
  sendPushNotification,
  sendSilentNotification,
};
