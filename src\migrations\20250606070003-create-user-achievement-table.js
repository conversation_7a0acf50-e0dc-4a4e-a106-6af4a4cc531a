'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('UserAchievement', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      achievementId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Achievement',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      currentValue: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      isCompleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      completedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('UserAchievement', ['userId'], {
      name: 'user_achievement_user_id_idx',
    });

    await queryInterface.addIndex('UserAchievement', ['achievementId'], {
      name: 'user_achievement_achievement_id_idx',
    });

    await queryInterface.addIndex('UserAchievement', ['isCompleted'], {
      name: 'user_achievement_is_completed_idx',
    });

    // Add unique constraint for userId + achievementId
    await queryInterface.addIndex(
      'UserAchievement',
      ['userId', 'achievementId'],
      {
        unique: true,
        name: 'user_achievement_unique_idx',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex(
      'UserAchievement',
      'user_achievement_unique_idx'
    );
    await queryInterface.removeIndex(
      'UserAchievement',
      'user_achievement_is_completed_idx'
    );
    await queryInterface.removeIndex(
      'UserAchievement',
      'user_achievement_achievement_id_idx'
    );
    await queryInterface.removeIndex(
      'UserAchievement',
      'user_achievement_user_id_idx'
    );

    // Drop the table
    await queryInterface.dropTable('UserAchievement');
  },
};
