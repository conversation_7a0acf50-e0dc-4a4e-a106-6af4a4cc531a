/**
 * User Model
 */
const { Model, DataTypes } = require('sequelize');
const bcrypt = require('bcrypt');
const { UserType, CreatedVia, AccountStatus } = require('@utils/enums.utils');

class User extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        firstName: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        lastName: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        email: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          validate: {
            isEmail: true,
          },
          set(value) {
            this.setDataValue('email', value.toLowerCase());
          },
        },
        password: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        userType: {
          type: DataTypes.ENUM(...UserType.values),
          allowNull: false,
          defaultValue: UserType.EDUCATOR,
          field: 'userType',
          type: DataTypes.ENUM(...UserType.values),
          references: {
            model: 'enum_User_userType',
            key: 'enum_User_userType',
          },
        },
        position: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        state: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        country: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        companyName: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        profilePic: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: null,
        },
        website: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        isPrivate: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        isDepartmentPrivate: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        stripeCustomerId: {
          type: DataTypes.STRING,
          allowNull: true,
          unique: true,
        },
        resetToken: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        resetTokenExpiry: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        createdVia: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: CreatedVia.USER,
        },
        deviceToken: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        deviceType: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        currentStep: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        signupToken: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        accountStatus: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: AccountStatus.ACTIVE,
        },
        tokenVersion: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
        },
        school: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        district: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        educationCareerStartDate: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        subject: {
          type: DataTypes.STRING,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'User',
        tableName: 'User',
        timestamps: true,
        indexes: [
          {
            fields: ['resetToken'],
            name: 'user_reset_token_idx',
          },
        ],
        hooks: {
          beforeSave: async (user) => {
            if (user.changed('password')) {
              user.password = await bcrypt.hash(user.password, 10);
            }
          },
        },
      }
    );
  }

  // Define associations
  static associate(models) {
    User.hasMany(models.Membership, {
      foreignKey: 'userId',
      as: 'memberships',
      onDelete: 'CASCADE', // Delete memberships when user is deleted
    });

    // User subscriptions association
    User.hasMany(models.UserSubscription, {
      foreignKey: 'userId',
      as: 'subscriptions',
      onDelete: 'CASCADE',
    });

    // Bookmarked insights association
    User.belongsToMany(models.Insight, {
      through: models.BookmarkedInsight,
      as: 'bookmarkedInsights',
      foreignKey: 'userId',
      otherKey: 'insightId',
      onDelete: 'CASCADE',
    });

    // Liked insights association
    User.belongsToMany(models.Insight, {
      through: models.LikedInsight,
      as: 'likedInsights',
      foreignKey: 'userId',
      otherKey: 'insightId',
      onDelete: 'CASCADE',
    });

    // Implemented insights association
    User.belongsToMany(models.Insight, {
      through: models.ImplementedInsight,
      as: 'implementedInsights',
      foreignKey: 'userId',
      otherKey: 'insightId',
      onDelete: 'CASCADE',
    });

    // Has many contributions
    User.hasMany(models.Contribution, {
      foreignKey: 'contributedBy',
      as: 'contributions',
      onDelete: 'CASCADE',
    });

    // Has many contribution likes
    User.hasMany(models.ContributionLike, {
      foreignKey: 'likedBy',
      as: 'contributionLikes',
      onDelete: 'CASCADE',
    });

    // IceBreaker association
    User.hasOne(models.WtdIceBreaker, {
      foreignKey: 'userId',
      as: 'iceBreaker',
      onDelete: 'CASCADE',
    });

    // Focus association
    User.belongsToMany(models.Focus, {
      through: models.UserFocus,
      foreignKey: 'userId',
      otherKey: 'focusId',
      as: 'focuses',
      onDelete: 'CASCADE',
    });

    // Followers association
    User.belongsToMany(models.User, {
      through: models.Follow,
      as: 'followers',
      foreignKey: 'followingId',
      otherKey: 'followerId',
      onDelete: 'CASCADE',
    });

    // Following association
    User.belongsToMany(models.User, {
      through: models.Follow,
      as: 'following',
      foreignKey: 'followerId',
      otherKey: 'followingId',
      onDelete: 'CASCADE',
    });

    // Has many user achievements
    User.hasMany(models.UserAchievement, {
      foreignKey: 'userId',
      as: 'achievements',
      onDelete: 'CASCADE',
    });

    // Has many user milestones
    User.hasMany(models.UserMilestone, {
      foreignKey: 'userId',
      as: 'milestones',
      onDelete: 'CASCADE',
    });

    // Has many user milestone responses
    User.hasMany(models.UserMilestoneResponse, {
      foreignKey: 'userId',
      as: 'milestoneResponses',
      onDelete: 'CASCADE',
    });

    // Has many notifications as receiver
    User.hasMany(models.Notification, {
      foreignKey: 'receiverUserId',
      as: 'receivedNotifications',
      onDelete: 'CASCADE',
    });

    // Has many notifications as sender
    User.hasMany(models.Notification, {
      foreignKey: 'userId',
      as: 'notifications',
      onDelete: 'CASCADE',
    });

    // Has many insight views
    User.hasMany(models.InsightView, {
      foreignKey: 'userId',
      as: 'insightViews',
      onDelete: 'CASCADE',
    });

    // EducatorQuestion associations
    // Has many educator questions created
    User.hasMany(models.EducatorQuestion, {
      foreignKey: 'createdBy',
      as: 'createdEducatorQuestions',
      onDelete: 'CASCADE',
    });

    // Bookmarked educator questions association
    User.belongsToMany(models.EducatorQuestion, {
      through: models.BookmarkedEducatorQuestion,
      as: 'bookmarkedEducatorQuestions',
      foreignKey: 'userId',
      otherKey: 'educatorQuestionId',
      onDelete: 'CASCADE',
    });

    // Liked educator questions association
    User.belongsToMany(models.EducatorQuestion, {
      through: models.LikedEducatorQuestion,
      as: 'likedEducatorQuestions',
      foreignKey: 'userId',
      otherKey: 'educatorQuestionId',
      onDelete: 'CASCADE',
    });

    // Implemented educator questions association
    User.belongsToMany(models.EducatorQuestion, {
      through: models.ImplementedEducatorQuestion,
      as: 'implementedEducatorQuestions',
      foreignKey: 'userId',
      otherKey: 'educatorQuestionId',
      onDelete: 'CASCADE',
    });

    // Has many educator question contributions
    User.hasMany(models.EducatorQuestionContribution, {
      foreignKey: 'userId',
      as: 'educatorQuestionContributions',
      onDelete: 'CASCADE',
    });

    // Has many educator question contribution likes
    User.hasMany(models.EducatorQuestionContributionLike, {
      foreignKey: 'userId',
      as: 'educatorQuestionContributionLikes',
      onDelete: 'CASCADE',
    });

    // Has many educator question views
    User.hasMany(models.EducatorQuestionView, {
      foreignKey: 'userId',
      as: 'educatorQuestionViews',
      onDelete: 'CASCADE',
    });
  }

  // Instance Methods
  async verifyPassword(password) {
    return bcrypt.compare(password, this.password);
  }

  toJSON() {
    const values = { ...this.get() };
    delete values.password;
    return values;
  }
}

module.exports = User;
