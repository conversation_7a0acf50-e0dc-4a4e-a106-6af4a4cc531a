const notificationRepository = require('@repositories/notification.repository');
const {
  sendPushNotification,
} = require('@integrations/firebase/notification.service');
const userRepository = require('@repositories/user.repository');
const { NotificationType } = require('@utils/enums.utils');
const { NOTIFICATION } = require('@utils/messages.utils');
const insightNotificationService = require('@modules/user/insight/services/notification.service');
const experienceNotificationService = require('@modules/user/experience/services/notification.service');
const followNotificationService = require('@modules/user/follow/services/notification.service');
const followRepository = require('@repositories/follow.repository');

jest.mock('@repositories/notification.repository');
jest.mock('@integrations/firebase/notification.service');
jest.mock('@repositories/user.repository');

describe('Notification Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockUser = {
    id: 'user1',
    firstName: 'John',
    lastName: 'Doe',
    deviceToken: 'token123',
  };
  const mockInsight = { id: 'insight1', createdBy: 'user2' };
  const mockContribution = { id: 'contrib1' };

  test('notifyContributionToInsight sends notification and push', async () => {
    userRepository.findById.mockResolvedValue({ ...mockUser, id: 'user2' });
    await insightNotificationService.notifyContributionToInsight({
      contributor: mockUser,
      insight: { ...mockInsight, createdBy: 'user2' },
      contribution: mockContribution,
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user2',
        type: NotificationType.CONTRIBUTION,
        title: NOTIFICATION.NEW_CONTRIBUTION_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'token123',
        title: NOTIFICATION.NEW_CONTRIBUTION_TITLE,
      })
    );
  });

  test('notifyInsightImplemented sends notification and push', async () => {
    userRepository.findById.mockResolvedValue({ ...mockUser, id: 'user2' });
    await insightNotificationService.notifyInsightImplemented({
      implementer: mockUser,
      insight: { ...mockInsight, createdBy: 'user2' },
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user2',
        type: NotificationType.IMPLEMENTATION,
        title: NOTIFICATION.INSIGHT_IMPLEMENTED_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'token123',
        title: NOTIFICATION.INSIGHT_IMPLEMENTED_TITLE,
      })
    );
  });

  test('notifyInsightApproved sends notification and push', async () => {
    await insightNotificationService.notifyInsightApproved({
      provider: mockUser,
      insight: mockInsight,
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user1',
        type: NotificationType.INSIGHT_APPROVED,
        title: NOTIFICATION.INSIGHT_APPROVED_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'token123',
        title: NOTIFICATION.INSIGHT_APPROVED_TITLE,
      })
    );
  });

  test('notifyContributionLiked sends notification and push', async () => {
    await insightNotificationService.notifyContributionLiked({
      liker: mockUser,
      contribution: mockContribution,
      contributor: { ...mockUser, id: 'user2' },
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user2',
        type: NotificationType.LIKE_CONTRIBUTION,
        title: NOTIFICATION.CONTRIBUTION_LIKED_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'token123',
        title: NOTIFICATION.CONTRIBUTION_LIKED_TITLE,
      })
    );
  });
});

describe('Experience Notification Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockProvider = {
    id: 'provider1',
    firstName: 'Alice',
    lastName: 'Smith',
    deviceToken: 'providerToken',
  };
  const mockUser = {
    id: 'user1',
    firstName: 'Bob',
    lastName: 'Brown',
    deviceToken: 'userToken',
  };
  const mockExperience = { id: 'exp1', title: 'Experience Title' };

  test('notifyExperienceCompletedThisWeek sends notification and push', async () => {
    await experienceNotificationService.notifyExperienceCompletedThisWeek({
      provider: mockProvider,
      experience: mockExperience,
      count: 5,
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'provider1',
        type: NotificationType.EXPERIENCE_COMPLETION,
        title: NOTIFICATION.EXPERIENCE_COMPLETED_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'providerToken',
        title: NOTIFICATION.EXPERIENCE_COMPLETED_TITLE,
      })
    );
  });

  test('notifyExperienceApproved sends notification and push', async () => {
    await experienceNotificationService.notifyExperienceApproved({
      provider: mockProvider,
      experience: mockExperience,
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'provider1',
        type: NotificationType.EXPERIENCE_APPROVED,
        title: NOTIFICATION.EXPERIENCE_APPROVED_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'providerToken',
        title: NOTIFICATION.EXPERIENCE_APPROVED_TITLE,
      })
    );
  });

  test('notifyExperienceStartReminder sends notification and push', async () => {
    await experienceNotificationService.notifyExperienceStartReminder({
      user: mockUser,
      experience: mockExperience,
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user1',
        type: NotificationType.EXPERIENCE_START_REMINDER,
        title: NOTIFICATION.EXPERIENCE_START_REMINDER_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'userToken',
        title: NOTIFICATION.EXPERIENCE_START_REMINDER_TITLE,
      })
    );
  });

  test('notifyExperienceStarted sends notification and push', async () => {
    await experienceNotificationService.notifyExperienceStarted({
      user: mockUser,
      experience: mockExperience,
    });
    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user1',
        type: NotificationType.EXPERIENCE_STARTED,
        title: NOTIFICATION.EXPERIENCE_STARTED_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'userToken',
        title: NOTIFICATION.EXPERIENCE_STARTED_TITLE,
      })
    );
  });
});

describe('Monthly New Followers Notification', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    userRepository.listUsers = jest.fn();
  });

  test('sends notification and push if user has new followers', async () => {
    const mockUsers = [
      { id: 'user1', deviceToken: 'token1' },
      { id: 'user2', deviceToken: 'token2' },
    ];
    userRepository.listUsers.mockResolvedValue({ data: mockUsers });
    followRepository.countNewFollowers = jest
      .fn()
      .mockResolvedValueOnce(3) // user1
      .mockResolvedValueOnce(0); // user2

    await followNotificationService.sendMonthlyNewFollowersNotification(
      new Date('2024-06-01'),
      new Date('2024-07-01')
    );

    expect(notificationRepository.createNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user1',
        type: NotificationType.NEW_FOLLOWERS_MONTHLY,
        title: NOTIFICATION.NEW_FOLLOWERS_MONTHLY_TITLE,
      })
    );
    expect(sendPushNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceToken: 'token1',
        title: NOTIFICATION.NEW_FOLLOWERS_MONTHLY_TITLE,
      })
    );
    // user2 should not get a notification
    expect(notificationRepository.createNotification).toHaveBeenCalledTimes(1);
    expect(sendPushNotification).toHaveBeenCalledTimes(1);
  });
});
