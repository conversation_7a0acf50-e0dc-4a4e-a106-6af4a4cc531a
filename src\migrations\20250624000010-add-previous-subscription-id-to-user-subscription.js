'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      'UserSubscription',
      'previousSubscriptionId',
      {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'UserSubscription',
          key: 'id',
        },
        onDelete: 'SET NULL',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn(
      'UserSubscription',
      'previousSubscriptionId'
    );
  },
};
