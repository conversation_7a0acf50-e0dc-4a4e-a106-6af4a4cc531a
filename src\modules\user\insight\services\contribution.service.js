/**
 * Contribution Service
 * Handles contribution (comments) business logic for insights
 */
const contributionRepository = require('@models/repositories/contribution.repository');
const insightRepository = require('@models/repositories/insight.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { ApiException } = require('@utils/exception.utils');
const { AchievementType, UserType, HttpStatus } = require('@utils/enums.utils');
const { INSIGHT, REPORT } = require('@utils/messages.utils');
const notificationService = require('./notification.service');

/**
 * Helper function to check if a provider is interacting with another provider's content
 * @param {Object} user - User object
 * @param {Object} contentCreator - Content creator object
 * @param {string} contentCreatorId - Content creator ID
 * @returns {boolean} True if provider is interacting with another provider's content
 */
function isProviderInteractingWithAnotherProvider(user, contentCreator) {
  const userId = user.id;
  const userType = user.userType;
  const creatorId = contentCreator.id;
  const creatorUserType = contentCreator.userType;

  const isProviderUser =
    userType === UserType.PROVIDER || userType === UserType.PROVIDER_PLUS;
  const isOtherProvider =
    creatorUserType === UserType.PROVIDER ||
    creatorUserType === UserType.PROVIDER_PLUS;
  const isInteractingWithOwnContent = creatorId === userId;

  return isProviderUser && !isInteractingWithOwnContent && isOtherProvider;
}

/**
 * Helper function to check if a user is liking an educator's contribution
 * @param {Object} user - User object
 * @param {Object} contentCreator - Content creator object
 * @returns {boolean} True if user is liking an educator's contribution
 */
function isuserLikingEducatorContribution(user, contentCreator) {
  const userId = user.id;
  const creatorId = contentCreator.id;
  const creatorUserType = contentCreator.userType;

  const isEducator =
    creatorUserType === UserType.EDUCATOR ||
    creatorUserType === UserType.EDUCATOR_PLUS;
  const isLikingOwnContent = creatorId === userId;

  return !isLikingOwnContent && isEducator;
}

/**
 * Contribution service
 */
const contributionService = {
  /**
   * Add a contribution (comment) to an insight
   * @param {string} insightId - The insight ID
   * @param {string} content - The contribution content
   * @param {Object} user - The user object
   * @returns {Object} Created contribution with user details
   */
  async addContribution(insightId, content, user) {
    const userId = user.id;
    try {
      // Check if insight exists
      const insight = await insightRepository.findById(insightId);
      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      // Check if user has already contributed to this insight
      const hasContributed = await contributionRepository.hasUserContributed(
        insight,
        userId
      );

      // Create contribution using repository
      const contribution = await contributionRepository.create({
        insightId,
        contributedBy: userId,
        content,
      });

      // Only update achievement progress if this is the user's first contribution to this insight
      if (!hasContributed) {
        const achievementType = isProviderInteractingWithAnotherProvider(
          user,
          insight.creator
        )
          ? AchievementType.CONTRIBUTE_OTHER_PROVIDER
          : AchievementType.INSIGHT_CONTRIBUTED;

        // Update achievement progress
        await achievementService.updateAchievementProgress(
          userId,
          achievementType,
          1
        );
      }

      // --- Notification Trigger ---
      await notificationService.notifyContributionToInsight({
        contributor: user,
        insight,
        contribution,
      });

      return contribution;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all contributions for an insight with pagination
   * @param {string} insightId - The insight ID
   * @param {Object} options - Pagination and sorting options
   * @param {string} userId - Current user ID for like status
   * @returns {Object} Contributions with pagination info
   */
  async getContributionsByInsight(insightId, options = {}, userId = null) {
    try {
      const { page, limit, sortBy = 'createdAt', sortOrder = 'DESC' } = options;

      // Check if insight exists
      const insight = await insightRepository.findById(insightId);
      if (!insight) {
        throw new ApiException(HttpStatus.NOT_FOUND, INSIGHT.NOT_FOUND);
      }

      // Get contributions using repository
      const result = await contributionRepository.findByInsight(insightId, {
        page,
        limit,
        orderBy: sortBy,
        orderDirection: sortOrder,
        userId,
      });

      return {
        contributions: result.contributions,
        pagination: result.pagination,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Toggle like/unlike for a contribution
   * @param {string} contributionId - The contribution ID
   * @param {Object} user - The user object
   * @returns {Object} Like status and count
   */
  async toggleContributionLike(contributionId, user) {
    try {
      // Check if contribution exists
      const contribution = await contributionRepository.findById(
        contributionId,
        true
      );
      if (!contribution) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          REPORT.CONTRIBUTION_NOT_FOUND
        );
      }

      // Toggle like using repository
      const result = await contributionRepository.toggleLike(
        contributionId,
        user.id
      );

      // Check if this is a user liking/unliking an educator's contribution
      const isProviderLikingEducator = isuserLikingEducatorContribution(
        user,
        contribution.contributor
      );

      if (isProviderLikingEducator) {
        await achievementService.updateAchievementProgress(
          user.id,
          AchievementType.LIKE_EDUCATOR_CONTRIBUTIONS,
          result.liked ? 1 : -1
        );
      }

      // Trigger notification if liked and not self-like
      if (
        result.liked &&
        contribution.contributor &&
        contribution.contributor.id !== user.id
      ) {
        await notificationService.notifyContributionLiked({
          liker: user,
          contribution,
        });
      }

      return {
        isLiked: result.liked,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Delete a contribution (user can only delete their own)
   * @param {string} contributionId - The contribution ID
   * @param {string} userId - The user ID
   * @returns {boolean} True if deleted
   */
  async deleteContribution(contributionId, userId) {
    try {
      // Check if contribution exists
      const contribution = await contributionRepository.findById(
        contributionId,
        false
      );
      if (!contribution) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          REPORT.CONTRIBUTION_NOT_FOUND
        );
      }

      // Check if user owns this contribution
      if (contribution.contributedBy !== userId) {
        throw new ApiException(
          HttpStatus.FORBIDDEN,
          REPORT.CANNOT_REPORT_OWN_CONTENT
        );
      }

      // Delete the contribution
      return await contributionRepository.delete(contributionId);
    } catch (error) {
      throw error;
    }
  },
};

module.exports = contributionService;
