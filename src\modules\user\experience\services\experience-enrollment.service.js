/**
 * Experience Enrollment Service
 *
 * Handles business logic for experience enrollments
 */
const experienceEnrollmentRepository = require('@models/repositories/experience-enrollment.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { AchievementType } = require('@utils/enums.utils');
const subscriptionService = require('@modules/user/subscription/subscription.service');
const { isEducatorFree, isUserOnPlan } = require('@utils/subscription.utils');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE_ENROLLMENT } = require('@utils/messages.utils');
const { SubscriptionPlanSlug } = require('@utils/enums.utils');

/**
 * Experience Enrollment Service
 */
const experienceEnrollmentService = {
  /**
   * Enroll a user in an experience
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {Object} enrollmentData - Enrollment data from request body
   * @returns {Promise<Object>} Created enrollment
   */
  async enrollUser(experienceId, userId, enrollmentData) {
    try {
      // Restriction: Prevent EDUCATOR_FREE users from enrolling
      const activeSubscription =
        await subscriptionService.getActiveSubscription(userId);

      if (isEducatorFree(activeSubscription)) {
        throw new ApiException(
          HttpStatus.FORBIDDEN,
          EXPERIENCE_ENROLLMENT.EDUCATOR_FREE_EXPERIENCE_RESTRICTED
        );
      }

      // Restriction: Only one active experience at a time for other educator plans
      if (
        isUserOnPlan(
          activeSubscription,
          SubscriptionPlanSlug.EDUCATOR_PLUS_ANNUAL
        ) ||
        isUserOnPlan(
          activeSubscription,
          SubscriptionPlanSlug.EDUCATOR_PLUS_3_MONTH
        )
      ) {
        const activeEnrollment =
          await experienceEnrollmentRepository.getActiveEnrollmentForUser(
            userId
          );
        if (activeEnrollment) {
          throw new ApiException(
            HttpStatus.FORBIDDEN,
            EXPERIENCE_ENROLLMENT.ONE_AT_A_TIME_LIMIT
          );
        }
      }

      const data = {
        experienceId,
        userId,
        ...enrollmentData,
      };
      return await experienceEnrollmentRepository.enrollUser(data);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Complete a specific week for a user
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {number} weekNumber - Week number to complete
   * @returns {Promise<Object>} Updated enrollment with progress
   */
  async completeWeek(experienceId, userId, weekNumber) {
    try {
      const result = await experienceEnrollmentRepository.completeWeek(
        experienceId,
        userId,
        weekNumber
      );

      // If experience is completed, update achievement progress
      if (result.enrollment.status === 'COMPLETED') {
        await achievementService.updateAchievementProgress(
          userId,
          AchievementType.EXPERIENCE_COMPLETED
        );
      }

      return result;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get detailed progress for an enrollment
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Detailed progress information
   */
  async getEnrollmentProgress(experienceId, userId) {
    try {
      return await experienceEnrollmentRepository.getEnrollmentProgress(
        experienceId,
        userId
      );
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceEnrollmentService;
