'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('UserAchievementResponse', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      questionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'AchievementQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      response: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      selectedOptionId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'AchievementQuestionOption',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
    await queryInterface.addIndex(
      'UserAchievementResponse',
      ['userId', 'questionId'],
      {
        unique: true,
        name: 'user_achievement_response_unique_idx',
      }
    );
    await queryInterface.addIndex('UserAchievementResponse', ['userId'], {
      name: 'user_achievement_response_user_id_idx',
    });
    await queryInterface.addIndex('UserAchievementResponse', ['questionId'], {
      name: 'user_achievement_response_question_id_idx',
    });
    await queryInterface.addIndex(
      'UserAchievementResponse',
      ['selectedOptionId'],
      {
        name: 'user_achievement_response_option_id_idx',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'UserAchievementResponse',
      'user_achievement_response_option_id_idx'
    );
    await queryInterface.removeIndex(
      'UserAchievementResponse',
      'user_achievement_response_question_id_idx'
    );
    await queryInterface.removeIndex(
      'UserAchievementResponse',
      'user_achievement_response_user_id_idx'
    );
    await queryInterface.removeIndex(
      'UserAchievementResponse',
      'user_achievement_response_unique_idx'
    );
    await queryInterface.dropTable('UserAchievementResponse');
  },
};
