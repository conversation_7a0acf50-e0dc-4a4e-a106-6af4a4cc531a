'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.addColumn('WtdIceBreaker', 'favoriteTeachingMemory', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'classroomDescription', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'favoriteClassroomPart', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'coworkerDescription', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'classroomExperience', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'essentialSupply', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'funniestStudentComment', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'bestAdviceFromTeacher', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
      queryInterface.addColumn('WtdIceBreaker', 'dreamFieldTrip', {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.removeColumn('WtdIceBreaker', 'favoriteTeachingMemory'),
      queryInterface.removeColumn('WtdIceBreaker', 'classroomDescription'),
      queryInterface.removeColumn('WtdIceBreaker', 'favoriteClassroomPart'),
      queryInterface.removeColumn('WtdIceBreaker', 'coworkerDescription'),
      queryInterface.removeColumn('WtdIceBreaker', 'classroomExperience'),
      queryInterface.removeColumn('WtdIceBreaker', 'essentialSupply'),
      queryInterface.removeColumn('WtdIceBreaker', 'funniestStudentComment'),
      queryInterface.removeColumn('WtdIceBreaker', 'bestAdviceFromTeacher'),
      queryInterface.removeColumn('WtdIceBreaker', 'dreamFieldTrip'),
    ]);
  },
};
