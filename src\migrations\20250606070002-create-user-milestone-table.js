'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('UserMilestone', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      milestoneId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Milestone',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      isCompleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      completedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      progress: {
        type: Sequelize.FLOAT,
        defaultValue: 0.0,
      },
      isCurrent: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('UserMilestone', ['userId'], {
      name: 'user_milestone_user_id_idx',
    });

    await queryInterface.addIndex('UserMilestone', ['milestoneId'], {
      name: 'user_milestone_milestone_id_idx',
    });

    await queryInterface.addIndex('UserMilestone', ['isCompleted'], {
      name: 'user_milestone_is_completed_idx',
    });

    await queryInterface.addIndex('UserMilestone', ['isCurrent'], {
      name: 'user_milestone_is_current_idx',
    });

    // Add unique constraint for userId + milestoneId
    await queryInterface.addIndex('UserMilestone', ['userId', 'milestoneId'], {
      unique: true,
      name: 'user_milestone_unique_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex(
      'UserMilestone',
      'user_milestone_unique_idx'
    );
    await queryInterface.removeIndex(
      'UserMilestone',
      'user_milestone_is_current_idx'
    );
    await queryInterface.removeIndex(
      'UserMilestone',
      'user_milestone_is_completed_idx'
    );
    await queryInterface.removeIndex(
      'UserMilestone',
      'user_milestone_milestone_id_idx'
    );
    await queryInterface.removeIndex(
      'UserMilestone',
      'user_milestone_user_id_idx'
    );

    // Drop the table
    await queryInterface.dropTable('UserMilestone');
  },
};
