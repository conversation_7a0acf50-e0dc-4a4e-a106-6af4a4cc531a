/**
 * Admin Subscription Plan Controller
 *
 * Handles subscription plan-related HTTP requests
 */
const { ApiResponse } = require('@utils/response.utils');
const { SUBSCRIPTION_PLAN } = require('@utils/messages.utils');
const subscriptionPlanService = require('./subscription-plan.service');

/**
 * Subscription Plan controller
 */
const subscriptionPlanController = {
  /**
   * Get all subscription plans
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllSubscriptionPlans: async (req, res, next) => {
    try {
      const { search, targetUserType } = req.query;
      const { page, limit } = req.pagination;

      const result = await subscriptionPlanService.getAllPlans({
        page,
        limit,
        search,
        targetUserType,
      });

      return ApiResponse.success(
        res,
        SUBSCRIPTION_PLAN.ALL_RETRIEVED,
        result.plans,
        { pagination: result.pagination }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get subscription plan by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getSubscriptionPlanById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const plan = await subscriptionPlanService.getPlanById(id);
      if (!plan) {
        return ApiResponse.error(res, SUBSCRIPTION_PLAN.NOT_FOUND, null, 404);
      }
      return ApiResponse.success(res, SUBSCRIPTION_PLAN.RETRIEVED, plan);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = subscriptionPlanController;
