const cron = require('node-cron');
const {
  sendMonthlyNewFollowersNotification,
} = require('./modules/user/follow/services/notification.service');
const {
  notifyExperienceCompletedThisWeek,
  notifyExperienceStartReminder,
  notifyExperienceStarted,
} = require('./modules/user/experience/services/notification.service');
const experienceEnrollmentRepository = require('./models/repositories/experience-enrollment.repository');
const { ExperienceEnrollmentStatus } = require('./utils/enums.utils');
const { Op } = require('sequelize');

/**
 * Monthly New Followers Notification
 * Schedule: 12:00pm EST (17:00 UTC, 22:30 IST) on the last day of the month
 * Trigger: If the user gained any more followers that month compared to the previous month
 * Purpose: Notifies users about the number of new members who followed them this month
 */
cron.schedule('0 17 28-31 * *', async () => {
  const now = new Date();
  // Check if today is the last day of the month
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);

  if (tomorrow.getDate() === 1) {
    // If tomorrow is the 1st, today is the last day
    console.log(
      `[CRON] (PROD) Monthly new followers notification running at`,
      now.toISOString()
    );
    try {
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      await sendMonthlyNewFollowersNotification(startDate, endDate);
      console.log('Monthly new followers notification job executed');
    } catch (error) {
      console.error('Error in monthly new followers cron job:', error);
    }
  }
});

/**
 * Weekly Experience Completion Notification
 * Schedule: 10:00am EST (15:00 UTC, 20:30 IST) every Sunday
 * Trigger: If there are users that completed the Provider's experience last week
 * Purpose: Notifies providers about the number of educators who completed their experience this week
 */
cron.schedule('0 15 * * 0', async () => {
  console.log(
    `[CRON] Weekly experience completion notification running at`,
    new Date().toISOString()
  );
  try {
    const now = new Date();
    // Find last week's Monday and Sunday
    const lastSunday = new Date(now);
    lastSunday.setDate(now.getDate() - now.getDay()); // last Sunday
    const lastMonday = new Date(lastSunday);
    lastMonday.setDate(lastSunday.getDate() - 6); // last Monday

    // Fetch all enrollments completed last week using updatedAt filter
    const enrollments = await experienceEnrollmentRepository.findAllEnrollments(
      {
        status: ExperienceEnrollmentStatus.COMPLETED,
        updatedAt: {
          [Op.gte]: lastMonday,
          [Op.lt]: lastSunday,
        },
      }
    );

    // Group by experience and provider
    const experienceMap = {};
    for (const enrollment of enrollments) {
      const expId = enrollment.experience.id;
      const providerId = enrollment.experience.createdBy;
      const deviceToken = enrollment.experience.creator?.deviceToken;
      if (!experienceMap[expId]) {
        experienceMap[expId] = {
          providerId,
          experience: enrollment.experience,
          count: 0,
          deviceToken,
        };
      }
      experienceMap[expId].count++;
    }

    // Send notification to each provider
    for (const expId in experienceMap) {
      const { providerId, experience, count, deviceToken } =
        experienceMap[expId];
      await notifyExperienceCompletedThisWeek({
        provider: { id: providerId, deviceToken },
        experience,
        count,
      });
    }
    console.log('Weekly experience completion notification job executed');
  } catch (error) {
    console.error('Error in weekly experience completion cron job:', error);
  }
});

/**
 * Daily Experience Start Reminder
 * Schedule: 10:00am EST (15:00 UTC, 20:30 IST) every day
 * Trigger: If a user is enrolled in an experience starting in 5 days
 * Purpose: Reminds users that their registered experience begins on Monday (5 days away)
 */
cron.schedule('0 15 * * *', async () => {
  console.log(
    `[CRON] Daily experience start reminder running at`,
    new Date().toISOString()
  );
  try {
    const now = new Date();
    const reminderDate = new Date(now);
    reminderDate.setDate(now.getDate() + 5);
    reminderDate.setHours(0, 0, 0, 0);

    const enrollments =
      await experienceEnrollmentRepository.findAllEnrollments();

    for (const enrollment of enrollments) {
      if (!enrollment.experience || !enrollment.user) {
        continue;
      }
      const startDate = new Date(enrollment.startDate);
      if (startDate.toDateString() === reminderDate.toDateString()) {
        await notifyExperienceStartReminder({
          user: enrollment.user,
          experience: enrollment.experience,
        });
      }
    }
    console.log('Daily experience start reminder job executed');
  } catch (error) {
    console.error('Error in daily experience start reminder cron job:', error);
  }
});

/**
 * Daily Experience Started Notification
 * Schedule: 10:00am EST (15:00 UTC, 20:30 IST) every day
 * Trigger: If a user is enrolled in an experience starting today
 * Purpose: Notifies users that their experience has officially begun
 */
cron.schedule('0 15 * * *', async () => {
  console.log(
    `[CRON] Daily experience started notification running at`,
    new Date().toISOString()
  );
  try {
    const now = new Date();
    now.setHours(0, 0, 0, 0);

    const enrollments =
      await experienceEnrollmentRepository.findAllEnrollments();

    for (const enrollment of enrollments) {
      if (!enrollment.experience || !enrollment.user) {
        continue;
      }
      const startDate = new Date(enrollment.startDate);
      if (startDate.toDateString() === now.toDateString()) {
        await notifyExperienceStarted({
          user: enrollment.user,
          experience: enrollment.experience,
        });
      }
    }
    console.log('Daily experience started notification job executed');
  } catch (error) {
    console.error(
      'Error in daily experience started notification cron job:',
      error
    );
  }
});

module.exports = cron;

// test cron jobs

// // Test: Monthly New Followers Notification (every 10 minutes)
// cron.schedule('*/10 * * * *', async () => {
//   const now = new Date();
//   console.log(
//     `[CRON] (TEST) Monthly new followers notification running at`,
//     now.toISOString()
//   );
//   try {
//     // Remove date restrictions - always run for testing
//     const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
//     const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
//     await sendMonthlyNewFollowersNotification(startDate, endDate);
//     console.log('Monthly new followers notification job executed (TEST)');
//   } catch (error) {
//     console.error('Error in monthly new followers cron job (TEST):', error);
//   }
// });

// // Test: Weekly Experience Completion Notification (every 10 minutes)
// cron.schedule('*/10 * * * *', async () => {
//   console.log(
//     `[CRON] (TEST) Weekly experience completion notification running at`,
//     new Date().toISOString()
//   );
//   try {
//     // Remove weekly date restrictions - check all completed enrollments for testing
//     const enrollments = await experienceEnrollmentRepository.findAllEnrollments(
//       {
//         status: ExperienceEnrollmentStatus.COMPLETED,
//       }
//     );

//     // Group by experience and provider
//     const experienceMap = {};
//     for (const enrollment of enrollments) {
//       const expId = enrollment.experience.id;
//       const providerId = enrollment.experience.createdBy;
//       const deviceToken = enrollment.experience.creator?.deviceToken;
//       if (!experienceMap[expId]) {
//         experienceMap[expId] = {
//           providerId,
//           experience: enrollment.experience,
//           count: 0,
//           deviceToken,
//         };
//       }
//       experienceMap[expId].count++;
//     }

//     // Send notification to each provider
//     for (const expId in experienceMap) {
//       const { providerId, experience, count, deviceToken } =
//         experienceMap[expId];
//       await notifyExperienceCompletedThisWeek({
//         provider: { id: providerId, deviceToken },
//         experience,
//         count,
//       });
//     }
//     console.log(
//       'Weekly experience completion notification job executed (TEST)'
//     );
//   } catch (error) {
//     console.error(
//       'Error in weekly experience completion cron job (TEST):',
//       error
//     );
//   }
// });

// // Test: Daily Experience Start Reminder (every 10 minutes)
// cron.schedule('*/10 * * * *', async () => {
//   console.log(
//     `[CRON] (TEST) Daily experience start reminder running at`,
//     new Date().toISOString()
//   );
//   try {
//     const enrollments =
//       await experienceEnrollmentRepository.findAllEnrollments();

//     for (const enrollment of enrollments) {
//       if (!enrollment.experience || !enrollment.user) {
//         continue;
//       }
//       // Remove date check - always send reminder for testing
//       await notifyExperienceStartReminder({
//         user: enrollment.user,
//         experience: enrollment.experience,
//       });
//     }
//     console.log('Daily experience start reminder job executed (TEST)');
//   } catch (error) {
//     console.error(
//       'Error in daily experience start reminder cron job (TEST):',
//       error
//     );
//   }
// });

// // Test: Daily Experience Started Notification (every 10 minutes)
// cron.schedule('*/10 * * * *', async () => {
//   console.log(
//     `[CRON] (TEST) Daily experience started notification running at`,
//     new Date().toISOString()
//   );
//   try {
//     const enrollments =
//       await experienceEnrollmentRepository.findAllEnrollments();

//     for (const enrollment of enrollments) {
//       if (!enrollment.experience || !enrollment.user) {
//         continue;
//       }
//       // Remove date check - always send notification for testing
//       await notifyExperienceStarted({
//         user: enrollment.user,
//         experience: enrollment.experience,
//       });
//     }
//     console.log('Daily experience started notification job executed (TEST)');
//   } catch (error) {
//     console.error(
//       'Error in daily experience started notification cron job (TEST):',
//       error
//     );
//   }
// });

// module.exports = cron;
