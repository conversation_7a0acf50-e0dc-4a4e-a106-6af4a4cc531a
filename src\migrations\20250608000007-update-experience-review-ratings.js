'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('ExperienceReview', 'courseRating', {
      type: Sequelize.DECIMAL(2, 1),
      allowNull: false,
    });

    await queryInterface.changeColumn('ExperienceReview', 'providerRating', {
      type: Sequelize.DECIMAL(2, 1),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('ExperienceReview', 'courseRating', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });

    await queryInterface.changeColumn('ExperienceReview', 'providerRating', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
  },
};
