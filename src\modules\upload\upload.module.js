/**
 * Upload Module
 *
 * This module handles file upload functionality
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const { authenticate } = require('@middlewares/auth.middleware');
const uploadController = require('./upload.controller');

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
});

/**
 * Register routes
 */
function registerRoutes() {
  // Upload a file
  router.post('/', upload.single('file'), uploadController.uploadFile);

  // Get file URL by key
  router.get('/:key', uploadController.getFileByKey);

  return router;
}

module.exports = registerRoutes();
