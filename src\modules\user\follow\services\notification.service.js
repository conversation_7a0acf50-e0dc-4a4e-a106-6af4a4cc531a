const followRepository = require('@repositories/follow.repository');
const { AdminUserRepository } = require('@models/repositories/user.repository');
const notificationRepository = require('@repositories/notification.repository');
const {
  sendPushNotification,
} = require('@integrations/firebase/notification.service');
const { NotificationType } = require('@utils/enums.utils');
const { NOTIFICATION } = require('@utils/messages.utils');

/**
 * Send monthly new followers notification to users
 * @param {Date} startDate - Start of the month
 * @param {Date} endDate - End of the month
 */
async function sendMonthlyNewFollowersNotification(startDate, endDate) {
  const { data: users } = await AdminUserRepository.listUsers({}, 1, 10000);
  // Calculate previous month range
  const prevMonthStart = new Date(startDate);
  prevMonthStart.setMonth(prevMonthStart.getMonth() - 1);
  const prevMonthEnd = new Date(endDate);
  prevMonthEnd.setMonth(prevMonthEnd.getMonth() - 1);

  for (const user of users) {
    const currentMonthFollowers = await followRepository.countNewFollowers(
      user.id,
      startDate,
      endDate
    );
    const previousMonthFollowers = await followRepository.countNewFollowers(
      user.id,
      prevMonthStart,
      prevMonthEnd
    );

    // Check if notification already sent for this month
    const alreadySent = await notificationRepository.findNotification({
      receiverUserId: user.id,
      type: NotificationType.NEW_FOLLOWERS_MONTHLY,
      createdAt: {
        $gte: startDate,
        $lt: endDate,
      },
    });

    if (
      !alreadySent &&
      currentMonthFollowers > previousMonthFollowers &&
      currentMonthFollowers > 0 &&
      user.deviceToken
    ) {
      const notificationText = `${currentMonthFollowers} new members followed you this month.`;
      const commonNotificationPayload = {
        userId: null,
        receiverUserId: user.id,
        type: NotificationType.NEW_FOLLOWERS_MONTHLY,
        title: NOTIFICATION.NEW_FOLLOWERS_MONTHLY_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: {},
      });
      await sendPushNotification({
        deviceToken: user.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          redirectUrl: `/lounge?notification=true`,
        },
      });
    }
  }
}

module.exports = {
  sendMonthlyNewFollowersNotification,
};
