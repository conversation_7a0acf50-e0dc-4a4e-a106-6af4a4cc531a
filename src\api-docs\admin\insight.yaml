openapi: 3.0.0
info:
  title: WTD Platform Admin Insights API
  version: 1.0.0
  description: API endpoints for managing insights as an admin

paths:
  /admin/insights:
    get:
      tags:
        - Admin Insights
      summary: List All Insights
      description: Get a list of all insights with pagination, optional search, and status filtering
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
        - $ref: '#/components/parameters/StatusParam'
      responses:
        '200':
          description: Successfully retrieved insights
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightListResponse'
        '401':
          description: Unauthorized - <PERSON><PERSON> is not authenticated

  /admin/insights/{id}:
    get:
      tags:
        - Admin Insights
      summary: Get Insight by ID
      description: Get a specific insight by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/InsightIdParam'
      responses:
        '200':
          description: Successfully retrieved insight
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightResponse'
        '401':
          description: Unauthorized - <PERSON><PERSON> is not authenticated
        '404':
          description: Insight not found

    put:
      tags:
        - Admin Insights
      summary: Update Insight
      description: Update an existing insight
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/InsightIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInsightRequest'
      responses:
        '200':
          description: Insight updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightResponse'
        '400':
          description: Bad Request - Invalid input
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Insight not found

    patch:
      tags:
        - Admin Insights
      summary: Update Insight Status
      description: Update the status of an insight (approve or reject)
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/InsightIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInsightStatusRequest'
      responses:
        '200':
          description: Insight status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightResponse'
        '400':
          description: Bad Request - Insight is already reviewed or invalid status
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Insight not found

    delete:
      tags:
        - Admin Insights
      summary: Delete Insight
      description: Delete an insight
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/InsightIdParam'
      responses:
        '200':
          description: Insight deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponse'
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Insight not found

  /admin/insights/{id}/remove:
    patch:
      tags:
        - Admin Insights
      summary: Soft Remove (Archive) Insight
      description: Soft remove (archive) an insight. This marks the insight as removed but does not permanently delete it.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/InsightIdParam'
      responses:
        '200':
          description: Insight archived (soft removed) successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightResponse'
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Insight not found

  /admin/insights/reports:
    get:
      tags:
        - Content Moderation
      summary: List All Insight Reports
      description: Get a list of all insight reports with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved insight reports
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightReportListResponse'
        '401':
          description: Unauthorized - Admin is not authenticated

  /admin/insights/reports/{reportId}:
    get:
      tags:
        - Content Moderation
      summary: Get Insight Report by ID
      description: Get a specific insight report by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ReportIdParam'
      responses:
        '200':
          description: Successfully retrieved insight report
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightReportResponse'
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Report not found

  /admin/insights/contribution-reports:
    get:
      tags:
        - Content Moderation
      summary: List All Contribution Reports
      description: Get a list of all contribution reports with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved contribution reports
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionReportListResponse'
        '401':
          description: Unauthorized - Admin is not authenticated

  /admin/insights/contribution-reports/{contributionReportId}:
    get:
      tags:
        - Content Moderation
      summary: Get Contribution Report by ID
      description: Get a specific contribution report by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ContributionReportIdParam'
      responses:
        '200':
          description: Successfully retrieved contribution report
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionReportResponse'
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Report not found

  /admin/insights/contribution/{contributionId}:
    delete:
      tags:
        - Content Moderation
      summary: Delete Contribution
      description: Delete a contribution (admin only)
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ContributionIdParam'
      responses:
        '200':
          description: Contribution deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponse'
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Contribution not found

  /admin/insights/contribution/{contributionId}/remove:
    patch:
      tags:
        - Content Moderation
      summary: Soft Remove (Archive) Contribution
      description: Soft remove (archive) a contribution. This marks the contribution as removed but does not permanently delete it.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ContributionIdParam'
      responses:
        '200':
          description: Contribution archived (soft removed) successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionResponse'
        '401':
          description: Unauthorized - Admin is not authenticated
        '404':
          description: Contribution not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Insight:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the insight
        insightText:
          type: string
          description: The insight text
        sourceUrl:
          type: string
          format: uri
          description: Source URL for the insight
        pdCategory:
          $ref: '#/components/schemas/PdCategoryRef'
        focus:
          type: array
          items:
            $ref: '#/components/schemas/FocusRef'
        wtdCategories:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategoryRef'
        status:
          type: string
          enum: [PENDING, APPROVED, REJECTED]
          description: Current status of the insight
        creator:
          $ref: '#/components/schemas/UserRef'
        reviewer:
          $ref: '#/components/schemas/AdminRef'
        reviewedAt:
          type: string
          format: date-time
          description: Date and time when the insight was reviewed
        rejectionReason:
          type: string
          description: Reason for rejection if status is REJECTED
        createdAt:
          type: string
          format: date-time
          description: Date and time when the insight was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the insight was last updated
      example:
        id: "123e4567-e89b-12d3-a456-************"
        insightText: "Regular physical activity improves cognitive function and academic performance."
        sourceUrl: "https://example.com/research-paper"
        pdCategory:
          id: "123e4567-e89b-12d3-a456-************"
          name: "Student Well-Being"
        focus:
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Elementary"
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Secondary"
        wtdCategories:
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Movement"
          - id: "123e4567-e89b-12d3-a456-426614174005"
            name: "Mindset"
        status: "APPROVED"
        creator:
          id: "123e4567-e89b-12d3-a456-************"
          firstName: "John"
          lastName: "Doe"
          email: "<EMAIL>"
        reviewer:
          id: "123e4567-e89b-12d3-a456-************"
          email: "<EMAIL>"
        reviewedAt: "2023-01-02T00:00:00.000Z"
        rejectionReason: null
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-02T00:00:00.000Z"

    PdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    FocusRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    WtdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    UserRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email

    AdminRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email

    UpdateInsightRequest:
      type: object
      properties:
        insightText:
          type: string
          maxLength: 250
          description: The insight text
          example: "Regular physical activity improves cognitive function and academic performance."
        sourceUrl:
          type: string
          format: uri
          description: Optional source URL
          example: "https://example.com/research-paper"
        pdCategoryId:
          type: string
          format: uuid
          description: PD category ID
          example: "123e4567-e89b-12d3-a456-************"
        focusIds:
          type: array
          description: Optional array of focus IDs
          items:
            type: string
            format: uuid
          example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]
        wtdCategoryIds:
          type: array
          description: Optional array of WTD category IDs
          items:
            type: string
            format: uuid
          example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]

    UpdateInsightStatusRequest:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [APPROVED, REJECTED]
          description: New status for the insight
          example: "APPROVED"
        rejectionReason:
          type: string
          description: Reason for rejecting the insight (required if status is REJECTED)
          example: "Content is not appropriate for the platform"

    InsightResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Insight retrieved successfully"
        data:
          $ref: '#/components/schemas/Insight'

    InsightListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "All insights retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Insight'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 3
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 1
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: false
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false

    DeleteResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Insight deleted successfully"

    InsightReport:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the report
        insightId:
          type: string
          format: uuid
          description: ID of the reported insight
        reportedBy:
          type: string
          format: uuid
          description: ID of the user who reported
        reason:
          type: string
          nullable: true
          description: Reason for reporting
        createdAt:
          type: string
          format: date-time
          description: Date and time when the report was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the report was last updated
        reporter:
          $ref: '#/components/schemas/UserRef'
        insight:
          type: object
          properties:
            id:
              type: string
              format: uuid
            insightText:
              type: string
            status:
              type: string
      example:
        id: "123e4567-e89b-12d3-a456-************"
        insightId: "123e4567-e89b-12d3-a456-************"
        reportedBy: "123e4567-e89b-12d3-a456-************"
        reason: "This content contains inappropriate material"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"
        reporter:
          id: "123e4567-e89b-12d3-a456-************"
          firstName: "John"
          lastName: "Doe"
          email: "<EMAIL>"
        insight:
          id: "123e4567-e89b-12d3-a456-************"
          insightText: "This is the reported insight text"
          status: "APPROVED"

    ContributionReport:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the report
        contributionId:
          type: string
          format: uuid
          description: ID of the reported contribution
        reportedBy:
          type: string
          format: uuid
          description: ID of the user who reported
        reason:
          type: string
          nullable: true
          description: Reason for reporting
        createdAt:
          type: string
          format: date-time
          description: Date and time when the report was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the report was last updated
        reporter:
          $ref: '#/components/schemas/UserRef'
        contribution:
          type: object
          properties:
            id:
              type: string
              format: uuid
            content:
              type: string
            insightId:
              type: string
              format: uuid
            insight:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                insightText:
                  type: string
            contributor:
              $ref: '#/components/schemas/UserRef'
      example:
        id: "123e4567-e89b-12d3-a456-************"
        contributionId: "123e4567-e89b-12d3-a456-************"
        reportedBy: "123e4567-e89b-12d3-a456-************"
        reason: "This contribution contains offensive language"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"
        reporter:
          id: "123e4567-e89b-12d3-a456-************"
          firstName: "Jane"
          lastName: "Smith"
          email: "<EMAIL>"
        contribution:
          id: "123e4567-e89b-12d3-a456-************"
          content: "This is the reported contribution content"
          insightId: "123e4567-e89b-12d3-a456-************"
          insight:
            id: "123e4567-e89b-12d3-a456-************"
            insightText: "Original insight text"
          contributor:
            id: "123e4567-e89b-12d3-a456-************"
            firstName: "Bob"
            lastName: "Johnson"
            email: "<EMAIL>"

    InsightReportListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Reports retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/InsightReport'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ContributionReportListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Reports retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/ContributionReport'
        pagination:
          $ref: '#/components/schemas/Pagination'

    InsightReportResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Report retrieved successfully"
        data:
          $ref: '#/components/schemas/InsightReport'

    ContributionReportResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Report retrieved successfully"
        data:
          $ref: '#/components/schemas/ContributionReport'

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter insights by text

    StatusParam:
      name: status
      in: query
      schema:
        type: string
        enum: [PENDING, APPROVED, REJECTED]
      description: Optional filter by insight status

    InsightIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Insight ID

    ContributionIdParam:
      name: contributionId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Contribution ID

    ReportIdParam:
      name: reportId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: The ID of the insight report

    ContributionReportIdParam:
      name: contributionReportId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: The ID of the contribution report