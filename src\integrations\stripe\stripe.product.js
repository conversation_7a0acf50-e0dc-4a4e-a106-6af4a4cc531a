/* eslint-disable camelcase */
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const StripePrice = require('./stripe.price');
const StripeSubscription = require('./stripe.subscription');
const { SubscriptionPlanBillingInterval } = require('@utils/enums.utils');

class StripeProduct {
  /**
   * Maps our billing interval to Strip<PERSON>'s expected format
   * @param {string} interval - Our billing interval
   * @param {string} planKey - Plan key from SUBSCRIPTION_PLAN
   * @returns {Object} Stripe recurring configuration
   */
  mapBillingIntervalToStripe(interval, planKey) {
    const mapping = {
      [SubscriptionPlanBillingInterval.MONTHLY]: 'month',
      [SubscriptionPlanBillingInterval.YEARLY]: 'year',
      [SubscriptionPlanBillingInterval.EVERY_3_MONTHS]: 'month',
    };

    const recurring = {
      interval: mapping[interval],
    };

    // Set interval_count for 3-month plans
    if (interval === SubscriptionPlanBillingInterval.EVERY_3_MONTHS) {
      recurring.interval_count = 3;
    }

    // Set 3-month interval for EDUCATOR_PLUS_3_MONTH plan (legacy support)
    if (planKey === 'EDUCATOR_PLUS_3_MONTH') {
      recurring.interval_count = 3;
    }

    return recurring;
  }

  /**
   * Create a Stripe product
   * @param {Object} data - Product data
   * @param {string} data.name - Product name
   * @param {string} data.description - Product description
   * @param {Object} [data.metadata] - Product metadata
   * @returns {Promise<Object>} Created Stripe product
   */
  async create(data) {
    try {
      return await stripe.products.create({
        name: data.name,
        description: data.description,
        metadata: data.metadata,
      });
    } catch (error) {
      console.error('[Stripe] Failed to create product:', error);
      throw error;
    }
  }

  /**
   * Update a Stripe product
   * @param {string} productId - Product ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated Stripe product
   */
  async update(productId, data) {
    try {
      return await stripe.products.update(productId, {
        name: data.name,
        description: data.description,
      });
    } catch (error) {
      console.error('[Stripe] Failed to update product:', error);
      throw error;
    }
  }

  /**
   * Delete a Stripe product
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} Deleted Stripe product
   */
  async delete(productId) {
    try {
      return await stripe.products.del(productId);
    } catch (error) {
      console.error('[Stripe] Failed to delete product:', error);
      throw error;
    }
  }

  /**
   * Archive a Stripe product
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} Archived Stripe product
   */
  async archive(productId) {
    try {
      return await stripe.products.update(productId, {
        active: false,
      });
    } catch (error) {
      console.error('[Stripe] Failed to archive product:', error);
      throw error;
    }
  }

  /**
   * List Stripe products
   * @param {Object} options - List options
   * @returns {Promise<Object>} List of products
   */
  async list(options = {}) {
    try {
      return await stripe.products.list(options);
    } catch (error) {
      console.error('[Stripe] Failed to list products:', error);
      throw error;
    }
  }

  /**
   * Search Stripe products
   * @param {string} query - Search query
   * @returns {Promise<Object>} Search results
   */
  async search(query) {
    try {
      return await stripe.products.search({
        query,
      });
    } catch (error) {
      console.error('[Stripe] Failed to search products:', error);
      throw error;
    }
  }
  /**
   * Checks if a plan already exists in Stripe by slug
   * @param {string} slug - Unique identifier for the plan
   * @returns {Promise<Object|null>} Existing Stripe product and price if found, null otherwise
   */
  async checkExistingPlan(slug) {
    try {
      const existingProducts = await this.search(
        `active:'true' AND metadata['slug']:'${slug}'`
      );

      if (!existingProducts.data || existingProducts.data.length === 0) {
        return null;
      }

      const product = existingProducts.data[0];
      const prices = await StripePrice.list({
        product: product.id,
        active: true,
      });

      if (!prices.data || prices.data.length === 0) {
        return null;
      }

      const sortedPrices = prices.data.sort((a, b) => b.created - a.created);
      const latestPrice = sortedPrices[0];

      return {
        stripeProductId: product.id,
        stripePriceId: latestPrice.id,
        product,
        price: latestPrice,
      };
    } catch (error) {
      console.error('[Stripe] Failed to check existing plan:', error);
      return null;
    }
  }

  /**
   * Creates a new product and price in Stripe
   * @param {Object} plan - Plan data
   * @param {string} slug - Plan slug
   * @returns {Promise<Object>} Object containing stripeProductId and stripePriceId
   */
  async createProductAndPrice(plan, slug) {
    try {
      const stripeProduct = await this.create({
        name: plan.name,
        description: plan.description,
        metadata: { slug },
      });

      const stripePrice = await StripePrice.create({
        product: stripeProduct.id,
        unitAmount: plan.priceCents,
        currency: 'usd',
        recurring:
          plan.billingInterval !== SubscriptionPlanBillingInterval.ONE_TIME
            ? this.mapBillingIntervalToStripe(plan.billingInterval, slug)
            : undefined,
      });

      await stripe.products.update(stripeProduct.id, {
        default_price: stripePrice.id,
      });

      return {
        stripeProductId: stripeProduct.id,
        stripePriceId: stripePrice.id,
      };
    } catch (error) {
      console.error('[Stripe] Failed to create product and price:', error);
      throw error;
    }
  }

  /**
   * Archives a product and deactivates its associated prices in Stripe
   * @param {string} productId - The Stripe product ID
   * @returns {Promise<void>}
   */
  async deleteProduct(productId) {
    try {
      const prices = await StripePrice.list({ product: productId });
      for (const price of prices.data) {
        if (price.active) {
          await StripePrice.update(price.id, { active: false });
        }
      }
      // Archive the product
      await this.archive(productId);
    } catch (error) {
      console.error(
        '[Stripe] Failed to archive product and deactivate prices:',
        error
      );
      throw error;
    }
  }
}

module.exports = new StripeProduct();
