/**
 * Impact Repository
 *
 * Handles data access operations for user impact and progress tracking
 */
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const databaseService = require('@config/database.config');
const { IMPACT, VALIDATION } = require('@utils/messages.utils');
const subscriptionService = require('@modules/user/subscription/subscription.service');
const { isEducatorFree } = require('@utils/subscription.utils');
const { EDUCATOR_FREE_MILESTONE_LIMIT } = require('@utils/enums.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    // Initialize all required models
    this.models = {
      Insight: databaseService.getInsightModel(),
      Experience: databaseService.getExperienceModel(),
      User: databaseService.getUserModel(),
      Milestone: databaseService.getMilestoneModel(),
      Achievement: databaseService.getAchievementModel(),
      UserMilestone: databaseService.getUserMilestoneModel(),
      UserAchievement: databaseService.getUserAchievementModel(),
      LikedInsight: databaseService.getLikedInsightModel(),
      ImplementedInsight: databaseService.getImplementedInsightModel(),
      Contribution: databaseService.getContributionModel(),
      ExperienceEnrollment: databaseService.getExperienceEnrollmentModel(),
      MilestoneQuestion: databaseService.getMilestoneQuestionModel(),
      MilestoneQuestionOption:
        databaseService.getMilestoneQuestionOptionModel(),
      UserMilestoneResponse: databaseService.getUserMilestoneResponseModel(),
    };
  }
}

/**
 * Helper class for Impact operations
 */
class ImpactHelper extends BaseRepository {
  /**
   * Get user activity metrics
   */
  async getUserActivityMetrics(userId) {
    try {
      // Get all required counts in parallel
      const [
        experiencesCreated,
        experiencesCompleted,
        insightsCreated,
        insightsLiked,
        insightsImplemented,
        insightsContributed,
      ] = await Promise.all([
        this.models.Experience.count({ where: { createdBy: userId } }),
        this.models.ExperienceEnrollment.count({
          where: { userId, status: 'COMPLETED' },
        }),
        this.models.Insight.count({ where: { createdBy: userId } }),
        this.models.LikedInsight.count({ where: { userId } }),
        this.models.ImplementedInsight.count({ where: { userId } }),
        this.models.Contribution.count({ where: { contributedBy: userId } }),
      ]);

      return {
        experiencesCreated,
        experiencesCompleted,
        insightsCreated,
        insightsLiked,
        insightsImplemented,
        insightsContributed,
      };
    } catch (error) {
      console.error('Error fetching user activity metrics:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get user's active milestones
   */
  async getUserMilestones(userType) {
    try {
      // Get all active milestones for the user type, ordered by sequence
      return this.models.Milestone.findAll({
        where: { isActive: true, userType },
        order: [['order', 'ASC']],
      });
    } catch (error) {
      console.error('Error fetching user milestones:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get user's achievements
   */
  async getUserAchievements(userId) {
    try {
      // Get all achievements for the user with their progress
      return this.models.UserAchievement.findAll({
        where: { userId },
        include: [
          {
            model: this.models.Achievement,
            as: 'achievement',
          },
        ],
      });
    } catch (error) {
      console.error('Error fetching user achievements:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Format milestones with their progress
   */
  async formatMilestonesWithProgress(milestones, userMetrics, userId) {
    // Get all user milestones to check isCurrent status
    const userMilestones = await this.models.UserMilestone.findAll({
      where: { userId },
      attributes: ['milestoneId', 'isCurrent'],
    });

    return Promise.all(
      milestones.map(async (milestone) => {
        // Get user milestone status
        const userMilestone = userMilestones.find(
          (um) => um.milestoneId === milestone.id
        );

        // Check if milestone is completed
        const isCompleted = await this.isMilestoneCompleted(
          milestone.id,
          userId
        );

        // Return formatted milestone
        return {
          id: milestone.id,
          name: milestone.name,
          isCompleted,
          isCurrent: userMilestone?.isCurrent || false,
        };
      })
    );
  }

  /**
   * Get achievements for a specific milestone
   */
  async getAchievementsForMilestone(milestoneId, userAchievements) {
    try {
      // First get all achievements for this milestone
      const milestoneAchievements = await this.models.Achievement.findAll({
        where: { milestoneId },
        attributes: ['id', 'name', 'targetValue'],
      });

      // Map through milestone achievements and check user progress
      return milestoneAchievements.map((achievement) => {
        // Find user's progress for this achievement
        const userAchievement = userAchievements.find(
          (ua) => ua.achievement.id === achievement.id
        );

        // If user has progress, use it, otherwise set defaults
        return {
          id: achievement.id,
          name: achievement.name,
          isCompleted: userAchievement ? userAchievement.isCompleted : false,
          currentValue: userAchievement ? userAchievement.currentValue : 0,
          targetValue: achievement.targetValue,
        };
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get achievements with progress for a milestone
   */
  async getAchievementsWithProgress(milestoneId, userId) {
    try {
      // Get all achievements for this milestone
      const achievements = await this.models.Achievement.findAll({
        where: { milestoneId },
        attributes: ['id', 'name', 'targetValue', 'achievementType'],
      });

      // Get user's progress for these achievements
      const userAchievements = await this.models.UserAchievement.findAll({
        where: {
          userId,
          achievementId: achievements.map((a) => a.id),
        },
        attributes: [
          'achievementId',
          'isCompleted',
          'currentValue',
          'completedAt',
        ],
      });

      // Map achievements with user progress
      return achievements.map((achievement) => {
        const userAchievement = userAchievements.find(
          (ua) => ua.achievementId === achievement.id
        );

        return {
          id: achievement.id,
          name: achievement.name,
          achievementType: achievement.achievementType,
          isCompleted: userAchievement?.isCompleted || false,
          currentValue: userAchievement?.currentValue || 0,
          targetValue: achievement.targetValue,
          completedAt: userAchievement?.completedAt || null,
        };
      });
    } catch (error) {
      console.error('Error fetching achievements with progress:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Check if milestone requires form submission
   */
  async hasMilestoneQuestions(milestoneId) {
    try {
      const count = await this.models.MilestoneQuestion.count({
        where: {
          milestoneId,
          isActive: true,
        },
      });
      return count > 0;
    } catch (error) {
      console.error('Error checking milestone questions:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get milestone questions with options
   */
  async getMilestoneQuestionsWithOptions(milestoneId) {
    try {
      // First check if milestone exists
      const milestone = await this.models.Milestone.findOne({
        where: {
          id: milestoneId,
          isActive: true,
        },
      });
      // If milestone does not exist, throw a not found error
      if (!milestone) {
        throw new ApiException(HttpStatus.NOT_FOUND, IMPACT.NOT_FOUND);
      }

      const questions = await this.models.MilestoneQuestion.findAll({
        where: {
          milestoneId,
          isActive: true,
        },
        include: [
          {
            model: this.models.MilestoneQuestionOption,
            as: 'options',
            required: false,
          },
        ],
        order: [
          ['order', 'ASC'],
          [
            { model: this.models.MilestoneQuestionOption, as: 'options' },
            'order',
            'ASC',
          ],
        ],
      });
      // Return empty array if no questions found
      return questions;
    } catch (error) {
      if (error instanceof ApiException) throw error;
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Check if a milestone is completed based on achievements
   * @param {string} milestoneId - ID of the milestone
   * @param {string} userId - ID of the user
   * @returns {Promise<boolean>} Whether the milestone is completed
   */
  async isMilestoneCompleted(milestoneId, userId) {
    // Get achievements for milestone
    const milestoneAchievements = await this.models.Achievement.findAll({
      where: {
        milestoneId,
        isActive: true,
      },
      attributes: ['id'],
    });

    // Get user's completed achievements for this milestone
    const userAchievements = await this.models.UserAchievement.findAll({
      where: {
        userId,
        achievementId: milestoneAchievements.map((a) => a.id),
      },
      attributes: ['achievementId', 'isCompleted'],
    });

    // Check if all achievements are completed
    return (
      milestoneAchievements.length > 0 &&
      userAchievements.filter((ua) => ua.isCompleted).length ===
        milestoneAchievements.length
    );
  }

  /**
   * Get current milestone with next milestone info
   * @param {string} userId - ID of the user
   * @param {Array} milestones - All milestones for the user type
   * @param {Array} achievements - User's achievements
   * @returns {Promise<Object>} Current and next milestone info
   */
  async getCurrentMilestoneInfo(userId, milestones, achievements) {
    // Get current milestone from user milestones
    const currentUserMilestone = await this.models.UserMilestone.findOne({
      where: {
        userId,
        isCurrent: true,
      },
      include: [
        {
          model: this.models.Milestone,
          as: 'milestone',
          required: true,
        },
      ],
    });

    if (!currentUserMilestone) {
      return {
        currentMilestone: null,
        nextMilestone: null,
      };
    }

    const current = currentUserMilestone.milestone;

    // Check if milestone is completed
    const isCompleted = await this.isMilestoneCompleted(current.id, userId);

    const currentMilestone = {
      id: current.id,
      icon: current.icon,
      name: current.name,
      quote: current.quote,
      quoteAuthor: current.quoteAuthor,
      isCurrent: true,
      isCompleted,
      congratulationsMessage: current.congratulationsMessage,
    };

    // Find the next milestone in sequence
    const currentIndex = milestones.findIndex((m) => m.id === current.id);
    let nextMilestone = null;

    if (currentIndex !== -1 && currentIndex + 1 < milestones.length) {
      const next = milestones[currentIndex + 1];
      // Get achievements for the next milestone
      const [nextMilestoneAchievements, requiresFormSubmission] =
        await Promise.all([
          this.getAchievementsForMilestone(next.id, achievements),
          this.hasMilestoneQuestions(next.id),
        ]);

      nextMilestone = {
        id: next.id,
        name: next.name,
        formSubmission: requiresFormSubmission,
        achievements: nextMilestoneAchievements,
      };
    }

    return {
      currentMilestone,
      nextMilestone,
    };
  }
}

/**
 * Helper class for Milestone Progress operations
 */
class MilestoneProgressHelper extends BaseRepository {
  /**
   * Get current milestone with next milestone info
   * @param {string} userId - ID of the user
   * @param {Array} milestones - All milestones for the user type
   * @param {Array} achievements - User's achievements
   * @returns {Promise<Object>} Current and next milestone info
   */
  async getCurrentMilestoneInfo(userId, milestones, achievements) {
    // Get current milestone from user milestones
    const currentUserMilestone = await this.models.UserMilestone.findOne({
      where: {
        userId,
        isCurrent: true,
      },
      include: [
        {
          model: this.models.Milestone,
          as: 'milestone',
          required: true,
        },
      ],
    });

    if (!currentUserMilestone) {
      return {
        currentMilestone: null,
        nextMilestone: null,
      };
    }

    const current = currentUserMilestone.milestone;

    // Check if milestone is completed
    const isCompleted = await this.isMilestoneCompleted(current.id, userId);

    const currentMilestone = {
      id: current.id,
      icon: current.icon,
      name: current.name,
      description: current.description,
      quote: current.quote,
      quoteAuthor: current.quoteAuthor,
      isCurrent: true,
      isCompleted,
      congratulationsMessage: current.congratulationsMessage,
    };

    // Find the next milestone in sequence
    const currentIndex = milestones.findIndex((m) => m.id === current.id);
    let nextMilestone = null;

    if (currentIndex !== -1 && currentIndex + 1 < milestones.length) {
      const next = milestones[currentIndex + 1];
      // Get achievements for the next milestone
      const [nextMilestoneAchievements, requiresFormSubmission] =
        await Promise.all([
          this.getAchievementsForMilestone(next.id, achievements),
          this.hasMilestoneQuestions(next.id),
        ]);

      nextMilestone = {
        id: next.id,
        name: next.name,
        description: next.description,
        formSubmission: requiresFormSubmission,
        achievements: nextMilestoneAchievements,
      };
    }

    return {
      currentMilestone,
      nextMilestone,
    };
  }

  /**
   * Format milestones with their progress
   */
  async formatMilestonesWithProgress(milestones, userMetrics, userId) {
    // Get all user milestones to check isCurrent status
    const userMilestones = await this.models.UserMilestone.findAll({
      where: { userId },
      attributes: ['milestoneId', 'isCurrent'],
    });

    return Promise.all(
      milestones.map(async (milestone) => {
        // Get user milestone status
        const userMilestone = userMilestones.find(
          (um) => um.milestoneId === milestone.id
        );

        // Check if milestone is completed
        const isCompleted = await this.isMilestoneCompleted(
          milestone.id,
          userId
        );

        // Return formatted milestone
        return {
          id: milestone.id,
          name: milestone.name,
          isCompleted,
          isCurrent: userMilestone?.isCurrent || false,
        };
      })
    );
  }

  /**
   * Get achievements for a specific milestone
   */
  async getAchievementsForMilestone(milestoneId, userAchievements) {
    try {
      // First get all achievements for this milestone
      const milestoneAchievements = await this.models.Achievement.findAll({
        where: { milestoneId },
        attributes: ['id', 'name', 'targetValue'],
      });

      // Map through milestone achievements and check user progress
      return milestoneAchievements.map((achievement) => {
        // Find user's progress for this achievement
        const userAchievement = userAchievements.find(
          (ua) => ua.achievement.id === achievement.id
        );

        // If user has progress, use it, otherwise set defaults
        return {
          id: achievement.id,
          name: achievement.name,
          isCompleted: userAchievement ? userAchievement.isCompleted : false,
          currentValue: userAchievement ? userAchievement.currentValue : 0,
          targetValue: achievement.targetValue,
        };
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Check if milestone requires form submission
   */
  async hasMilestoneQuestions(milestoneId) {
    try {
      const count = await this.models.MilestoneQuestion.count({
        where: {
          milestoneId,
          isActive: true,
          isRequired: true,
        },
      });
      return count > 0;
    } catch (error) {
      console.error('Error checking milestone questions:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Check if a milestone is completed based on achievements
   * @param {string} milestoneId - ID of the milestone
   * @param {string} userId - ID of the user
   * @returns {Promise<boolean>} Whether the milestone is completed
   */
  async isMilestoneCompleted(milestoneId, userId) {
    // Get achievements for milestone
    const milestoneAchievements = await this.models.Achievement.findAll({
      where: {
        milestoneId,
        isActive: true,
      },
      attributes: ['id'],
    });

    // Get user's completed achievements for this milestone
    const userAchievements = await this.models.UserAchievement.findAll({
      where: {
        userId,
        achievementId: milestoneAchievements.map((a) => a.id),
      },
      attributes: ['achievementId', 'isCompleted'],
    });

    // Check if all achievements are completed
    return (
      milestoneAchievements.length > 0 &&
      userAchievements.filter((ua) => ua.isCompleted).length ===
        milestoneAchievements.length
    );
  }
}

/**
 * Main Impact Repository class
 */
class ImpactRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new ImpactHelper();
    this.milestoneProgressHelper = new MilestoneProgressHelper();
  }

  /**
   * Get user activity metrics
   */
  async getUserActivityMetrics(user) {
    return this.helper.getUserActivityMetrics(user.id);
  }

  /**
   * Get milestones with progress
   * @param {Object} user - User object from request
   */
  async getMilestonesWithProgress(user) {
    try {
      // Fetch user activity metrics, milestones for the user's type, and user's achievements all at once
      const [metrics, milestones, achievements] = await Promise.all([
        this.helper.getUserActivityMetrics(user.id),
        this.helper.getUserMilestones(user.userType),
        this.helper.getUserAchievements(user.id),
      ]);

      // If no milestones found for user type, throw a not found error
      if (!milestones.length) {
        throw new ApiException(HttpStatus.NOT_FOUND, IMPACT.NOT_FOUND);
      }

      // Combine milestones with progress info based on user activity metrics
      const allMilestones =
        await this.milestoneProgressHelper.formatMilestonesWithProgress(
          milestones,
          metrics,
          user.id
        );

      // Get current and next milestone info
      const { currentMilestone, nextMilestone } =
        await this.milestoneProgressHelper.getCurrentMilestoneInfo(
          user.id,
          milestones,
          achievements
        );

      // Add formSubmission key to currentMilestone if it exists
      let currentMilestoneWithForm = currentMilestone;
      if (currentMilestone) {
        const formSubmission = await this.helper.hasMilestoneQuestions(
          currentMilestone.id
        );
        currentMilestoneWithForm = { ...currentMilestone, formSubmission };
      }

      // Return the collected and formatted impact data to the caller
      return {
        activityMetrics: metrics,
        currentMilestone: currentMilestoneWithForm,
        nextMilestone,
        completedMilestones: allMilestones.filter((m) => m.isCompleted).length,
        totalMilestones: allMilestones.length,
        milestones: allMilestones,
      };
    } catch (error) {
      // Re-throw known API exceptions to be handled upstream
      if (error instanceof ApiException) throw error;

      // Throw a generic internal server error for unexpected issues
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get achievements with progress for current milestone
   * @param {Object} user - User object from request
   * @returns {Promise<Object>} Achievements data with progress
   */
  async getAchievementsWithProgress(user) {
    try {
      // Get user's activity metrics
      const activityMetrics = await this.helper.getUserActivityMetrics(user.id);

      // Get user's milestones with progress
      const milestones = await this.getMilestonesWithProgress(user);

      // Get current milestone
      const currentMilestone = milestones.currentMilestone;
      if (!currentMilestone) {
        throw new ApiException(HttpStatus.NOT_FOUND, IMPACT.NOT_FOUND);
      }

      // Get achievements for current milestone
      const achievements = await this.helper.getAchievementsWithProgress(
        currentMilestone.id,
        user.id
      );

      // Calculate completed and total achievements
      const completedAchievements = achievements.filter(
        (a) => a.isCompleted
      ).length;
      const totalAchievements = achievements.length;

      return {
        userType: user.userType,
        activityMetrics,
        completedAchievements,
        totalAchievements,
        achievements,
      };
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get milestone questions with options
   * @param {string} milestoneId - ID of the milestone
   */
  async getMilestoneQuestions(milestoneId) {
    try {
      return await this.helper.getMilestoneQuestionsWithOptions(milestoneId);
    } catch (error) {
      if (error instanceof ApiException) throw error;
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Move to next milestone
   * @param {string} userId - ID of the user
   * @param {string} milestoneId - ID of the current milestone
   */
  async moveToNextMilestone(userId, milestoneId) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Educator Free plan milestone restriction
      const activeSubscription =
        await subscriptionService.getActiveSubscription(userId);

      if (isEducatorFree(activeSubscription)) {
        // Count completed milestones for the user
        const completedMilestones = await this.models.UserMilestone.count({
          where: {
            userId,
          },
        });
        if (completedMilestones >= EDUCATOR_FREE_MILESTONE_LIMIT) {
          throw new ApiException(
            HttpStatus.FORBIDDEN,
            IMPACT.EDUCATOR_FREE_MILESTONE_LIMIT
          );
        }
      }

      // Get current milestone
      const currentMilestone = await this.models.Milestone.findOne({
        where: {
          id: milestoneId,
          isActive: true,
        },
      });

      if (!currentMilestone) {
        throw new ApiException(HttpStatus.NOT_FOUND, IMPACT.NOT_FOUND);
      }

      // Get next milestone
      const nextMilestone = await this.models.Milestone.findOne({
        where: {
          userType: currentMilestone.userType,
          order: currentMilestone.order + 1,
          isActive: true,
        },
      });

      if (!nextMilestone) {
        throw new ApiException(HttpStatus.NOT_FOUND, IMPACT.NO_NEXT_MILESTONE);
      }

      // Update current milestone
      await this.models.UserMilestone.update(
        {
          isCurrent: false,
        },
        {
          where: {
            userId,
            milestoneId,
          },
          transaction,
        }
      );

      // Create or update next milestone progress
      await this.models.UserMilestone.upsert(
        {
          userId,
          milestoneId: nextMilestone.id,
          isCurrent: true,
          startedAt: new Date(),
        },
        { transaction }
      );

      await transaction.commit();

      return {
        message: `Successfully moved to ${nextMilestone.name}`,
        nextMilestone: {
          id: nextMilestone.id,
          name: nextMilestone.name,
          formSubmission: await this.helper.hasMilestoneQuestions(
            nextMilestone.id
          ),
        },
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Submit milestone questions
   * @param {string} userId - ID of the user
   * @param {string} milestoneId - ID of the milestone
   * @param {Array<Object>} answers - Array of question answers
   * @param {string} answers[].questionId - ID of the question
   * @param {string} answers[].answer - User's answer (text or option UUID)
   */
  async submitMilestoneQuestions(userId, milestoneId, answers) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Validate input parameters
      if (!userId || !milestoneId || !Array.isArray(answers)) {
        throw new ApiException(HttpStatus.BAD_REQUEST, VALIDATION.REQUIRED);
      }

      // Get milestone questions with their options
      const questions =
        await this.helper.getMilestoneQuestionsWithOptions(milestoneId);

      if (!questions.length) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          IMPACT.QUESTIONS_NOT_FOUND
        );
      }

      // Check if all questions are already answered
      const existingResponses = await this.models.UserMilestoneResponse.findAll(
        {
          where: {
            userId,
            questionId: questions.map((q) => q.id),
          },
        }
      );

      // If there are existing responses, delete them before inserting new ones
      if (existingResponses.length > 0) {
        await this.models.UserMilestoneResponse.destroy({
          where: {
            userId,
            questionId: questions.map((q) => q.id),
          },
          transaction,
        });
      }

      // Validate all required questions are answered
      const requiredQuestions = questions.filter((q) => q.isRequired);
      const answeredQuestionIds = answers.map((a) => a.questionId);

      const missingQuestions = requiredQuestions.filter(
        (q) => !answeredQuestionIds.includes(q.id)
      );

      if (missingQuestions.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          IMPACT.QUESTIONS_REQUIRED
        );
      }

      // Validate answers match question types
      for (const answer of answers) {
        const question = questions.find((q) => q.id === answer.questionId);
        if (!question) {
          throw new ApiException(
            HttpStatus.BAD_REQUEST,
            IMPACT.QUESTION_NOT_FOUND
          );
        }

        // For option-based questions, validate the option exists
        if (question.options?.length > 0) {
          const optionExists = question.options.some(
            (opt) => opt.id === answer.answer
          );
          if (!optionExists) {
            throw new ApiException(
              HttpStatus.BAD_REQUEST,
              IMPACT.OPTION_NOT_FOUND
            );
          }
        }
      }

      // Save answers
      await this.models.UserMilestone.update(
        {
          questionsSubmitted: true,
          questionsSubmittedAt: new Date(),
        },
        {
          where: {
            userId,
            milestoneId,
          },
          transaction,
        }
      );

      // Save individual answers
      for (const answer of answers) {
        await this.models.UserMilestoneResponse.create(
          {
            userId,
            questionId: answer.questionId,
            response: answer.answer,
            selectedOptionId:
              questions.find((q) => q.id === answer.questionId).options
                ?.length > 0
                ? answer.answer
                : null,
          },
          { transaction }
        );
      }

      await transaction.commit();

      return {
        message: IMPACT.QUESTIONS_SUBMITTED,
        milestoneId,
      };
    } catch (error) {
      await transaction.rollback();
      if (error instanceof ApiException) {
        throw error;
      }
      console.error('Error in submitMilestoneQuestions:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }
}

module.exports = new ImpactRepository();
