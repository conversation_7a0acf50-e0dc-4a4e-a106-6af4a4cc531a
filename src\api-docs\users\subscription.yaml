openapi: 3.0.0
info:
  title: WTD Platform User Subscriptions API
  version: 1.0.0
  description: API endpoints for managing user subscriptions

paths:
  /user/subscription:
    post:
      tags:
        - User Subscriptions
      summary: Create a new subscription
      description: |
        Create a new subscription for a user. For free plans (price = 0), no payment method is required.
        For paid plans, a valid payment method ID must be provided.
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionCreateInput'
      responses:
        '201':
          $ref: '#/components/responses/SubscriptionCreated'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      tags:
        - User Subscriptions
      summary: Get all user subscriptions
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription/active:
    get:
      tags:
        - User Subscriptions
      summary: Get active subscription
      security:
        - BearerAuth: []
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionDetail'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription/{subscriptionId}/pause:
    patch:
      tags:
        - User Subscriptions
      summary: Pause a subscription
      description: Pause a paid subscription. Free subscriptions cannot be paused.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/SubscriptionId'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPaused'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription/{subscriptionId}/resume:
    patch:
      tags:
        - User Subscriptions
      summary: Resume a paused subscription
      description: Resume a paused paid subscription. Free subscriptions cannot be paused/resumed.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/SubscriptionId'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionResumed'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription/{subscriptionId}/cancel:
    post:
      tags:
        - User Subscriptions
      summary: Cancel a subscription
      description: Cancel a subscription. For free subscriptions, this will mark them as inactive.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/SubscriptionId'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionCancelled'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription/{subscriptionId}/payment-method:
    put:
      tags:
        - User Subscriptions
      summary: Update payment method
      description: Update payment method for a paid subscription. Not applicable for free subscriptions.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/SubscriptionId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentMethodUpdateInput'
      responses:
        '200':
          $ref: '#/components/responses/PaymentMethodUpdated'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription/webhook:
    post:
      tags:
        - User Subscriptions
      summary: Handle Stripe webhook events
      description: Handle Stripe webhook events for paid subscriptions. Free subscriptions do not generate webhook events.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          $ref: '#/components/responses/WebhookReceived'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription/{subscriptionId}/upgrade:
    post:
      tags:
        - User Subscriptions
      summary: Upgrade a subscription plan
      description: >
        Upgrade the plan of an existing subscription. Only upgrades to a higher-priced plan are allowed. Downgrades and switching to the same plan are not permitted.
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/SubscriptionId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - planId
              properties:
                planId:
                  type: string
                  format: uuid
                  description: ID of the new subscription plan
                paymentMethodId:
                  type: string
                  description: Payment method ID (optional, required if changing payment method)
      responses:
        '200':
          description: Subscription upgraded successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Subscription'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    SubscriptionId:
      name: subscriptionId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the subscription
    Page:
      name: page
      in: query
      description: Page number for pagination
      schema:
        type: integer
        minimum: 1
        default: 1
    Limit:
      name: limit
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        default: 10
  schemas:
    Subscription:
      type: object
      properties:
        id:
          type: string
          format: uuid
        planId:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        status:
          type: string
          enum: [active, paused, canceled, past_due]
        stripeSubscriptionId:
          type: string
          nullable: true
          description: Stripe subscription ID (null for free subscriptions)
        stripeSubscriptionDetails:
          type: object
          nullable: true
          description: Stripe subscription details (null for free subscriptions)
        currentPeriodStart:
          type: string
          format: date-time
        currentPeriodEnd:
          type: string
          format: date-time
          nullable: true
          description: End date of current period (null for free subscriptions)
        cancelAtPeriodEnd:
          type: boolean
          nullable: true
          description: Whether subscription will be canceled at period end (null for free subscriptions)
        canceledAt:
          type: string
          format: date-time
          nullable: true
          description: When the subscription was canceled (null for free subscriptions)
        trialStart:
          type: string
          format: date-time
          nullable: true
          description: Start of trial period (null for free subscriptions)
        trialEnd:
          type: string
          format: date-time
          nullable: true
          description: End of trial period (null for free subscriptions)
        paymentMethodId:
          type: string
          format: uuid
          nullable: true
          description: Payment method ID (null for free subscriptions)
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    SubscriptionCreateInput:
      type: object
      required:
        - planId
      properties:
        planId:
          type: string
          format: uuid
          description: ID of the subscription plan
        paymentMethodId:
          type: string
          description: Payment method ID (required only for paid plans)
        currentStep:
          type: integer
          description: Current step in the signup process (optional)
    PaymentMethodUpdateInput:
      type: object
      required:
        - paymentMethodId
      properties:
        paymentMethodId:
          type: string
          description: New payment method ID
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string
  responses:
    SubscriptionCreated:
      description: Subscription created successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
    SubscriptionList:
      description: List of user subscriptions
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Subscription'
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                      limit:
                        type: integer
                      total:
                        type: integer
    SubscriptionDetail:
      description: Subscription details
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
    SubscriptionPaused:
      description: Subscription paused successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
    SubscriptionResumed:
      description: Subscription resumed successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
    SubscriptionCancelled:
      description: Subscription cancelled successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
    PaymentMethodUpdated:
      description: Payment method updated successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
    WebhookReceived:
      description: Webhook event received successfully
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SuccessResponse'
    BadRequestError:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    UnauthorizedError:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'