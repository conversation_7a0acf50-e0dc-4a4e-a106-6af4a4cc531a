/**
 * UserMilestone Model
 * Represents user's progress and completion status for milestones
 */
const { Model, DataTypes } = require('sequelize');

class UserMilestone extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        milestoneId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Milestone',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        progress: {
          type: DataTypes.FLOAT,
          defaultValue: 0.0,
          validate: {
            min: 0.0,
            max: 100.0,
          },
        },
        isCurrent: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize,
        modelName: 'UserMilestone',
        tableName: 'UserMilestone',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'milestoneId'],
            name: 'user_milestone_unique_idx',
          },
          {
            fields: ['userId'],
            name: 'user_milestone_user_id_idx',
          },
          {
            fields: ['milestoneId'],
            name: 'user_milestone_milestone_id_idx',
          },
          {
            fields: ['isCurrent'],
            name: 'user_milestone_is_current_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to user
    UserMilestone.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    // Belongs to milestone
    UserMilestone.belongsTo(models.Milestone, {
      foreignKey: 'milestoneId',
      as: 'milestone',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = UserMilestone;
