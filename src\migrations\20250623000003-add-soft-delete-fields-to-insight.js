'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add soft delete fields to Insight table
    await queryInterface.addColumn('Insight', 'isRemoved', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });

    await queryInterface.addColumn('Insight', 'removedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    // Add index for better query performance
    await queryInterface.addIndex('Insight', ['isRemoved'], {
      name: 'insight_is_removed_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove index
    await queryInterface.removeIndex('Insight', 'insight_is_removed_idx');

    // Remove columns from Insight table
    await queryInterface.removeColumn('Insight', 'removedAt');
    await queryInterface.removeColumn('Insight', 'isRemoved');
  },
};
