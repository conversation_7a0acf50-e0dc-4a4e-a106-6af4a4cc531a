/**
 * UserMilestoneResponse Model
 * Tracks user responses to milestone questions
 */
const { Model, DataTypes } = require('sequelize');

class UserMilestoneResponse extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        questionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'MilestoneQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        response: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        selectedOptionId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'MilestoneQuestionOption',
            key: 'id',
          },
          onDelete: 'SET NULL',
        },
      },
      {
        sequelize,
        modelName: 'UserMilestoneResponse',
        tableName: 'UserMilestoneResponse',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'questionId'],
            name: 'user_milestone_response_unique_idx',
          },
          {
            fields: ['userId'],
            name: 'user_milestone_response_user_id_idx',
          },
          {
            fields: ['questionId'],
            name: 'user_milestone_response_question_id_idx',
          },
          {
            fields: ['selectedOptionId'],
            name: 'user_milestone_response_option_id_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to user
    UserMilestoneResponse.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    // Belongs to milestone question
    UserMilestoneResponse.belongsTo(models.MilestoneQuestion, {
      foreignKey: 'questionId',
      as: 'question',
      onDelete: 'CASCADE',
    });

    // Belongs to selected option (if any)
    UserMilestoneResponse.belongsTo(models.MilestoneQuestionOption, {
      foreignKey: 'selectedOptionId',
      as: 'selectedOption',
      onDelete: 'SET NULL',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = UserMilestoneResponse;
