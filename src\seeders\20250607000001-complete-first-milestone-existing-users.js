'use strict';

const { UserType } = require('../utils/enums.utils');
const { v4: uuidv4 } = require('uuid');

/**
 * Seeder to complete the first milestone for existing users
 * This seeder is idempotent and will only process users who don't have the first milestone completed
 */
module.exports = {
  async up(queryInterface) {
    // Get all users without the first milestone
    const users = await queryInterface.sequelize.query(
      `SELECT u.id, u."userType"
       FROM "User" u
       LEFT JOIN "UserMilestone" um ON u.id = um."userId"
       LEFT JOIN "Milestone" m ON um."milestoneId" = m.id
       WHERE um.id IS NULL
       AND u."userType" IN (:types)
      `,
      {
        replacements: {
          types: [
            UserType.EDUCATOR,
            UserType.EDUCATOR_PLUS,
            UserType.PROVIDER,
            UserType.PROVIDER_PLUS,
          ],
        },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      }
    );

    console.log(`Found ${users.length} users without first milestone`);

    // Process each user
    for (const user of users) {
      // Get the first milestone for this user type
      const milestone = await queryInterface.sequelize.query(
        `SELECT id FROM "Milestone" 
         WHERE "userType" = :userType 
         AND "milestoneIdentifier" = :milestoneIdentifier
         AND "isActive" = true
         ORDER BY "order" ASC
         LIMIT 1`,
        {
          replacements: {
            userType: user.userType,
            milestoneIdentifier: `${user.userType}_WTD_BEGINNER`,
          },
          type: queryInterface.sequelize.QueryTypes.SELECT,
        }
      );

      if (milestone.length === 0) {
        console.log(
          `No first milestone found for user type ${user.userType}, skipping user ${user.id}`
        );
        continue;
      }

      const milestoneId = milestone[0].id;

      // Check if user already has this milestone
      const existingMilestone = await queryInterface.sequelize.query(
        `SELECT id FROM "UserMilestone" 
         WHERE "userId" = :userId 
         AND "milestoneId" = :milestoneId`,
        {
          replacements: {
            userId: user.id,
            milestoneId: milestoneId,
          },
          type: queryInterface.sequelize.QueryTypes.SELECT,
        }
      );

      if (existingMilestone.length > 0) {
        console.log(
          `User ${user.id} already has milestone ${milestoneId}, skipping`
        );
        continue;
      }

      // Create UserMilestone entry
      const userMilestoneId = uuidv4();
      await queryInterface.sequelize.query(
        `INSERT INTO "UserMilestone" 
         ("id", "userId", "milestoneId", "createdAt", "updatedAt", "progress", "isCurrent")
         VALUES 
         (:id, :userId, :milestoneId, NOW(), NOW(), 100, true)`,
        {
          replacements: {
            id: userMilestoneId,
            userId: user.id,
            milestoneId: milestoneId,
          },
        }
      );

      // Find the CREATE_ACCOUNT achievement for this milestone
      const achievement = await queryInterface.sequelize.query(
        `SELECT id, "targetValue" FROM "Achievement"
         WHERE "milestoneId" = :milestoneId
         AND "achievementType" = 'CREATE_ACCOUNT'
         AND "isActive" = true
         LIMIT 1`,
        {
          replacements: { milestoneId },
          type: queryInterface.sequelize.QueryTypes.SELECT,
        }
      );

      if (achievement.length > 0) {
        const userAchievementId = uuidv4();
        await queryInterface.sequelize.query(
          `INSERT INTO "UserAchievement" 
           ("id", "userId", "achievementId", "createdAt", "updatedAt", "currentValue", "isCompleted")
           VALUES 
           (:id, :userId, :achievementId, NOW(), NOW(), :targetValue, true)`,
          {
            replacements: {
              id: userAchievementId,
              userId: user.id,
              achievementId: achievement[0].id,
              targetValue: achievement[0].targetValue,
            },
          }
        );
      }

      console.log(
        `Completed first milestone for user ${user.id} (${user.userType})`
      );
    }
  },

  async down(queryInterface) {
    // Delete all first milestone completions
    await queryInterface.sequelize.query(
      `DELETE FROM "UserMilestone" 
       WHERE "milestoneId" IN (
         SELECT id FROM "Milestone" 
         WHERE "milestoneIdentifier" IN (
           'EDUCATOR_WTD_BEGINNER',
           'EDUCATOR_PLUS_WTD_BEGINNER',
           'PROVIDER_WTD_BEGINNER',
           'PROVIDER_PLUS_WTD_BEGINNER'
         )
       )`
    );

    // Delete corresponding achievements
    await queryInterface.sequelize.query(
      `DELETE FROM "UserAchievement" 
       WHERE "achievementId" IN (
         SELECT id FROM "Milestone" 
         WHERE "milestoneIdentifier" IN (
           'EDUCATOR_WTD_BEGINNER',
           'EDUCATOR_PLUS_WTD_BEGINNER',
           'PROVIDER_WTD_BEGINNER',
           'PROVIDER_PLUS_WTD_BEGINNER'
         )
       )`
    );
  },
};
