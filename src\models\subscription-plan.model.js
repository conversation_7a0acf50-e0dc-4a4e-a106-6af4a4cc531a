/**
 * SubscriptionPlan Model
 * Represents available subscription plans on the platform
 */
const { Model, DataTypes } = require('sequelize');

class SubscriptionPlan extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        slug: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          validate: {
            notEmpty: true,
          },
        },
        targetUserType: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        stripeProductId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        stripePriceId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        price: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: false,
          defaultValue: 0.0,
          validate: {
            min: 0,
          },
        },
        priceCents: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
        billingInterval: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        trialDays: {
          type: DataTypes.INTEGER,
          allowNull: true,
          validate: {
            min: 0,
          },
        },
        canRegisterExperiences: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        insightsLimit: {
          type: DataTypes.INTEGER,
          allowNull: true, // null = unlimited
        },
        insightsLimitPeriod: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
      },
      {
        sequelize,
        modelName: 'SubscriptionPlan',
        tableName: 'SubscriptionPlan',
        timestamps: true,
        indexes: [
          {
            fields: ['targetUserType'],
            name: 'subscription_plan_target_user_type_idx',
          },
          {
            fields: ['isActive'],
            name: 'subscription_plan_is_active_idx',
          },
          {
            fields: ['billingInterval'],
            name: 'subscription_plan_billing_interval_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    SubscriptionPlan.hasMany(models.UserSubscription, {
      foreignKey: 'subscriptionPlanId',
      as: 'subscriptions',
      onDelete: 'CASCADE',
    });
  }

  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = SubscriptionPlan;
