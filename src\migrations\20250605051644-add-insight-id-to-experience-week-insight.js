'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add insightId column to ExperienceWeekInsight table
    await queryInterface.addColumn('ExperienceWeekInsight', 'insightId', {
      type: Sequelize.UUID,
      allowNull: true, // Initially allow null for data migration
      references: {
        model: 'Insight',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });

    // Add index for insightId
    await queryInterface.addIndex('ExperienceWeekInsight', ['insightId'], {
      name: 'experience_week_insight_insight_id_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove index
    await queryInterface.removeIndex(
      'ExperienceWeekInsight',
      'experience_week_insight_insight_id_idx'
    );

    // Remove insightId column
    await queryInterface.removeColumn('ExperienceWeekInsight', 'insightId');
  },
};
