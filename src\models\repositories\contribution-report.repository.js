/**
 * ContributionReport Repository
 * Handles database operations for contribution reports
 */
const databaseService = require('@config/database.config');

class ContributionReportRepository {
  /**
   * Find contribution report by ID
   * @param {string} id - Report ID
   * @returns {Promise<Object|null>} Report or null
   */
  async findById(id) {
    try {
      const ContributionReport = databaseService.getContributionReportModel();
      const User = databaseService.getUserModel();
      const Contribution = databaseService.getContributionModel();
      const Insight = databaseService.getInsightModel();

      return await ContributionReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'reporter',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            model: Contribution,
            as: 'contribution',
            attributes: ['id', 'content', 'insightId', 'isRemoved'],
            include: [
              {
                model: Insight,
                as: 'insight',
                attributes: ['id', 'insightText'],
              },
              {
                model: User,
                as: 'contributor',
                attributes: ['id', 'firstName', 'lastName', 'email'],
              },
            ],
          },
        ],
      });
    } catch (error) {
      throw new Error(`Error finding contribution report: ${error.message}`);
    }
  }

  /**
   * Create a new contribution report
   * @param {Object} reportData - Report data
   * @returns {Promise<Object>} Created report
   */
  async create(reportData) {
    try {
      const ContributionReport = databaseService.getContributionReportModel();
      const report = await ContributionReport.create(reportData);
      return await this.findById(report.id);
    } catch (error) {
      throw new Error(`Error creating contribution report: ${error.message}`);
    }
  }

  /**
   * Find all contribution reports with pagination and filtering
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Reports and pagination info
   */
  async findAll({
    page = 1,
    limit = 10,
    search,
    sortBy = 'createdAt',
    sortOrder = 'DESC',
  }) {
    try {
      const ContributionReport = databaseService.getContributionReportModel();
      const User = databaseService.getUserModel();
      const Contribution = databaseService.getContributionModel();
      const Insight = databaseService.getInsightModel();

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Build include options with search
      const includeOptions = [
        {
          model: User,
          as: 'reporter',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          where: search
            ? {
                [databaseService.getSequelize().Op.or]: [
                  {
                    firstName: {
                      [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                    },
                  },
                  {
                    lastName: {
                      [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                    },
                  },
                  {
                    email: {
                      [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                    },
                  },
                ],
              }
            : undefined,
          required: search ? true : false,
        },

        {
          model: Contribution,
          as: 'contribution',
          attributes: ['id', 'content', 'insightId', 'isRemoved'],
          where: search
            ? {
                content: {
                  [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                },
              }
            : undefined,
          required: false,
          include: [
            {
              model: Insight,
              as: 'insight',
              attributes: ['id', 'insightText'],
            },
            {
              model: User,
              as: 'contributor',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ];

      const { count, rows } = await ContributionReport.findAndCountAll({
        where: whereClause,
        include: includeOptions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true,
      });

      return {
        reports: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      };
    } catch (error) {
      throw new Error(`Error finding contribution reports: ${error.message}`);
    }
  }

  /**
   * Check if user has already reported this contribution
   * @param {string} contributionId - Contribution ID
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} True if already reported
   */
  async hasUserReported(contributionId, userId) {
    try {
      const ContributionReport = databaseService.getContributionReportModel();
      const report = await ContributionReport.findOne({
        where: {
          contributionId,
          reportedBy: userId,
        },
      });
      return !!report;
    } catch (error) {
      throw new Error(`Error checking user report: ${error.message}`);
    }
  }
}

module.exports = new ContributionReportRepository();
