'use strict';
const { UserType, QuestionType } = require('../utils/enums.utils');
const { v4: uuidv4 } = require('uuid');

// Model definitions are accessed via queryInterface.sequelize.models within up/down methods.

// Questions for Educator & Educator Plus User Types
const milestoneFormsData = {
  'WTD Contributor': [
    {
      text: 'How has your WTD experience been? We’d love any feedback or thoughts you’d like to share. Thank you! – The WTD Team',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: false,
    },
  ],
  'WTD Encourager': [
    {
      text: 'How has your WTD experience been? We’d love any feedback or thoughts you’d like to share. Thank you! – The WTD Team',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: false,
    },
  ],
  'WTD Catalyst': [
    {
      text: 'How has your WTD experience been? We’d love any feedback or thoughts you’d like to share. Thank you! – The WTD Team',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: false,
    },
  ],
  // 'WTD Reflector': [
  //   {
  //     text: 'Which insight have you loved lately? (Shout out the provider who inspired this!)',
  //     questionType: QuestionType.TEXT,
  //     order: 1,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What inspired you to implement this insight, and what were you hoping to achieve?',
  //     questionType: QuestionType.TEXT,
  //     order: 2,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'How did the insight impact you or change your way of thinking?',
  //     questionType: QuestionType.TEXT,
  //     order: 3,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What adjustments or modifications did you make (if any)?',
  //     questionType: QuestionType.TEXT,
  //     order: 4,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'How do you plan to continue using this?',
  //     questionType: QuestionType.TEXT,
  //     order: 5,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'Do we have your permission to share these insights on social media?',
  //     questionType: QuestionType.MULTIPLE_CHOICE,
  //     order: 6,
  //     isRequired: true,
  //     options: [
  //       { id: uuidv4(), label: 'Yes', value: 'true', order: 1 },
  //       { id: uuidv4(), label: 'No', value: 'false', order: 2 },
  //     ],
  //   },
  // ],
  // 'WTD Visionary': [
  //   {
  //     text: 'What is a change you are envisioning for your classroom?',
  //     questionType: QuestionType.TEXT,
  //     order: 1,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'Why is this change important?',
  //     questionType: QuestionType.TEXT,
  //     order: 2,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What is your first step?',
  //     questionType: QuestionType.TEXT,
  //     order: 3,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What challenge do you foresee?',
  //     questionType: QuestionType.TEXT,
  //     order: 4,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What outcome do you hope for?',
  //     questionType: QuestionType.TEXT,
  //     order: 5,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'Do we have your permission to share these insights on social media?',
  //     questionType: QuestionType.MULTIPLE_CHOICE,
  //     order: 6,
  //     isRequired: true,
  //     options: [
  //       { label: 'Yes', value: 'true', order: 1 },
  //       { label: 'No', value: 'false', order: 2 },
  //     ],
  //   },
  // ],
  // 'WTD Innovator': [
  //   {
  //     text: 'How did your Visionary idea go in practice?',
  //     questionType: QuestionType.TEXT,
  //     order: 1,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'Was the impact as expected? Why or why not?',
  //     questionType: QuestionType.TEXT,
  //     order: 2,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What adjustments did you make based on feedback?',
  //     questionType: QuestionType.TEXT,
  //     order: 3,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What new insights or challenges arose?',
  //     questionType: QuestionType.TEXT,
  //     order: 4,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What’s next?',
  //     questionType: QuestionType.TEXT,
  //     order: 5,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'Do we have your permission to share these insights on social media?',
  //     questionType: QuestionType.MULTIPLE_CHOICE,
  //     order: 6,
  //     isRequired: true,
  //     options: [
  //       { label: 'Yes', value: 'true', order: 1 },
  //       { label: 'No', value: 'false', order: 2 },
  //     ],
  //   },
  // ],
  // 'WTD Trailblazer': [
  //   {
  //     text: 'Describe an initiative you want to implement to improve your school.',
  //     questionType: QuestionType.TEXT,
  //     order: 1,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What inspired it?',
  //     questionType: QuestionType.TEXT,
  //     order: 2,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What impact will it have?',
  //     questionType: QuestionType.TEXT,
  //     order: 3,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'How will you involve others?',
  //     questionType: QuestionType.TEXT,
  //     order: 4,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What challenges do you anticipate?',
  //     questionType: QuestionType.TEXT,
  //     order: 5,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'How will you sustain the change?',
  //     questionType: QuestionType.TEXT,
  //     order: 6,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'When will you start?',
  //     questionType: QuestionType.TEXT,
  //     order: 7,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'Do we have your permission to share these insights on social media?',
  //     questionType: QuestionType.MULTIPLE_CHOICE,
  //     order: 8,
  //     isRequired: true,
  //     options: [
  //       { label: 'Yes', value: 'true', order: 1 },
  //       { label: 'No', value: 'false', order: 2 },
  //     ],
  //   },
  // ],
};

// Questions for Provider & Provider Plus User Types
const providerMilestoneFormsData = {
  'WTD Contributor': [
    {
      text: 'How has your WTD experience been? We’d love any feedback or thoughts you’d like to share. Thank you! – The WTD Team',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: false,
    },
  ],
  'WTD Encourager': [
    {
      text: 'How has your WTD experience been? We’d love any feedback or thoughts you’d like to share. Thank you! – The WTD Team',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: false,
    },
  ],
  'WTD Catalyst': [
    {
      text: 'How has your WTD experience been? We’d love any feedback or thoughts you’d like to share. Thank you! – The WTD Team',
      questionType: QuestionType.TEXT,
      order: 1,
      isRequired: false,
    },
  ],
  // 'WTD Trailblazer': [
  //   {
  //     text: 'What change have you made in your life/company inspired by the WTD community?',
  //     questionType: QuestionType.TEXT,
  //     order: 1,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What impact did it have?',
  //     questionType: QuestionType.TEXT,
  //     order: 2,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'What do you believe the future of education looks like?',
  //     questionType: QuestionType.TEXT,
  //     order: 3,
  //     isRequired: true,
  //   },
  //   {
  //     text: 'How will you continue advocating for that future?',
  //     questionType: QuestionType.TEXT,
  //     order: 4,
  //     isRequired: true,
  //   },
  // ],
};

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Get existing questions to avoid duplicates
      const existingQuestions = await queryInterface.sequelize.query(
        `SELECT "question", "milestoneId" FROM "MilestoneQuestion"`,
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        }
      );

      const existingMap = new Map(
        existingQuestions.map((q) => [`${q.question}-${q.milestoneId}`, true])
      );

      // Get milestones
      const milestones = await queryInterface.sequelize.query(
        `SELECT "id", "name", "userType" FROM "Milestone" WHERE "name" IN (:names) AND "userType" IN (:types)`,
        {
          replacements: {
            names: [
              ...Object.keys(milestoneFormsData),
              ...Object.keys(providerMilestoneFormsData),
            ],
            types: [
              UserType.EDUCATOR,
              UserType.EDUCATOR_PLUS,
              UserType.PROVIDER,
              UserType.PROVIDER_PLUS,
            ],
          },
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        }
      );

      // Process each milestone
      for (const milestone of milestones) {
        // Get questions based on user type
        const questions = milestone.userType.includes('PROVIDER')
          ? providerMilestoneFormsData[milestone.name] || []
          : milestoneFormsData[milestone.name] || [];

        // Process each question
        for (const question of questions) {
          if (existingMap.has(`${question.text}-${milestone.id}`)) continue;

          // Generate UUID for the question
          const questionId = uuidv4();

          // Create the question record
          await queryInterface.bulkInsert(
            'MilestoneQuestion',
            [
              {
                id: questionId,
                milestoneId: milestone.id,
                question: question.text,
                questionType: question.questionType,
                order: question.order,
                isRequired: question.isRequired,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            ],
            { transaction }
          );

          // If it's a multiple choice question, create the options
          if (
            question.questionType === QuestionType.MULTIPLE_CHOICE &&
            question.options
          ) {
            const options = question.options.map((option, index) => ({
              id: uuidv4(),
              questionId,
              label: option.label,
              value: option.value,
              order: option.order || index + 1,
              createdAt: new Date(),
              updatedAt: new Date(),
            }));

            await queryInterface.bulkInsert(
              'MilestoneQuestionOption',
              options,
              {
                transaction,
              }
            );
          }
        }
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Get all questions for the milestones we created
      const milestoneNames = [
        ...Object.keys(milestoneFormsData),
        ...Object.keys(providerMilestoneFormsData),
      ];

      await queryInterface.sequelize.query(
        `DELETE FROM "MilestoneQuestionOption" WHERE "questionId" IN 
        (SELECT "id" FROM "MilestoneQuestion" WHERE "milestoneId" IN 
          (SELECT "id" FROM "Milestone" WHERE "name" IN (:names)))`,
        {
          replacements: { names: milestoneNames },
          transaction,
        }
      );

      await queryInterface.sequelize.query(
        `DELETE FROM "MilestoneQuestion" WHERE "milestoneId" IN 
        (SELECT "id" FROM "Milestone" WHERE "name" IN (:names))`,
        {
          replacements: { names: milestoneNames },
          transaction,
        }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
