/**
 * User EducatorQuestion Module
 *
 * This module handles educator-question-related functionality for users
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const educatorQuestionController = require('./educator-question.controller');
const educatorQuestionValidation = require('./educator-question.validation');

// Create a new educator question
router.post(
  '/',
  authenticate,
  validate(educatorQuestionValidation.create),
  educatorQuestionController.createEducatorQuestion
);

// Get all educator questions
router.get(
  '/',
  authenticate,
  validate(educatorQuestionValidation.getAll),
  educatorQuestionController.getAllEducatorQuestions
);

// Get educator question by ID
router.get(
  '/:questionId',
  authenticate,
  validate(educatorQuestionValidation.getById),
  educatorQuestionController.getEducatorQuestionById
);

// Toggle like status for an educator question
router.post(
  '/:questionId/like',
  authenticate,
  educatorQuestionController.toggleLike
);

// Toggle bookmark status for an educator question
router.post(
  '/:questionId/bookmark',
  authenticate,
  educatorQuestionController.toggleBookmark
);

// Toggle implement status for an educator question
router.post(
  '/:questionId/implement',
  authenticate,
  educatorQuestionController.toggleImplement
);

module.exports = router;
