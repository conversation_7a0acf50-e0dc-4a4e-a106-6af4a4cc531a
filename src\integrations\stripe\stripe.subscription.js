/* eslint-disable camelcase */
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripeSubscription {
  /**
   * Create a subscription in Stripe
   * @param {Object} options - Subscription creation options
   * @param {string} options.customerId - Stripe customer ID
   * @param {string} options.priceId - Stripe price ID
   * @param {string} options.paymentMethodId - Stripe payment method ID
   * @param {number} [options.trialDays] - Optional trial period in days
   * @returns {Promise<Object>} Created Stripe subscription
   */
  async create(options) {
    try {
      const {
        customerId,
        priceId,
        paymentMethodId,
        trialDays, // directly passed
      } = options;

      const subscriptionParams = {
        customer: customerId,
        items: [{ price: priceId }],
        default_payment_method: paymentMethodId,
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
      };

      // Add trial_period_days only if provided and valid
      if (typeof trialDays === 'number' && trialDays > 0) {
        subscriptionParams.trial_period_days = trialDays;
      }

      return await stripe.subscriptions.create(subscriptionParams);
    } catch (error) {
      console.error('[Stripe] Failed to create subscription:', error);
      throw error;
    }
  }

  /**
   * Update a subscription in Stripe
   * @param {string} subscriptionId - Stripe subscription ID
   * @param {Object} updateParams - Parameters to update
   * @returns {Promise<Object>} Updated Stripe subscription
   */
  async update(subscriptionId, updateParams) {
    try {
      return await stripe.subscriptions.update(subscriptionId, updateParams);
    } catch (error) {
      console.error('[Stripe] Failed to update subscription:', error);
      throw error;
    }
  }

  /**
   * Cancel a subscription in Stripe by setting cancel_at_period_end to true
   * @param {string} subscriptionId - Stripe subscription ID
   * @returns {Promise<Object>} Updated Stripe subscription
   */
  async cancel(subscriptionId) {
    try {
      return await this.update(subscriptionId, {
        cancel_at_period_end: true,
      });
    } catch (error) {
      console.error('[Stripe] Failed to cancel subscription:', error);
      throw error;
    }
  }

  /**
   * Immediately cancel a subscription in Stripe (delete it right away)
   * @param {string} subscriptionId - Stripe subscription ID
   * @returns {Promise<Object>} Deleted Stripe subscription
   */
  async cancelImmediately(subscriptionId) {
    try {
      return await stripe.subscriptions.cancel(subscriptionId);
    } catch (error) {
      console.error(
        '[Stripe] Failed to immediately cancel subscription:',
        error
      );
      throw error;
    }
  }

  /**
   * Update the payment method for a subscription
   * @param {string} subscriptionId - Stripe subscription ID
   * @param {string} paymentMethodId - Stripe payment method ID
   * @returns {Promise<Object>} Updated Stripe subscription
   */
  async updatePaymentMethod(subscriptionId, paymentMethodId) {
    try {
      return await this.update(subscriptionId, {
        default_payment_method: paymentMethodId,
      });
    } catch (error) {
      console.error(
        '[Stripe] Failed to update subscription payment method:',
        error
      );
      throw error;
    }
  }

  /**
   * Pause a subscription in Stripe
   * @param {string} subscriptionId - Stripe subscription ID
   * @returns {Promise<Object>} Updated Stripe subscription
   */
  async pause(subscriptionId) {
    try {
      return await this.update(subscriptionId, {
        pause_collection: {
          behavior: 'mark_uncollectible',
        },
      });
    } catch (error) {
      console.error('[Stripe] Failed to pause subscription:', error);
      throw error;
    }
  }

  /**
   * Resume a paused subscription in Stripe
   * @param {string} subscriptionId - Stripe subscription ID
   * @returns {Promise<Object>} Updated Stripe subscription
   */
  async resume(subscriptionId) {
    try {
      return await this.update(subscriptionId, {
        pause_collection: null,
      });
    } catch (error) {
      console.error('[Stripe] Failed to resume subscription:', error);
      throw error;
    }
  }
}

module.exports = new StripeSubscription();
