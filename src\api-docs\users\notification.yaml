openapi: 3.0.0
info:
  title: User Notification API
  version: 1.0.0
  description: API endpoints for user notifications

paths:
  /user/notification:
    get:
      tags:
        - User Notification
      summary: Get All Notifications
      description: Get a list of all notifications for the authenticated user with pagination
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          $ref: '#/components/responses/NotificationListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/notification/{id}/read:
    patch:
      tags:
        - User Notification
      summary: Mark Notification as Read
      description: Mark a specific notification as read for the authenticated user
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/NotificationIdParam'
      responses:
        '200':
          $ref: '#/components/responses/NotificationReadResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotificationNotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)
    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        default: 10
      description: Number of items per page
    NotificationIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Notification ID

  responses:
    NotificationListResponse:
      description: Successfully retrieved notifications
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 200
              message:
                type: string
                example: Notifications retrieved successfully
              data:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'
              pagination:
                $ref: '#/components/schemas/PaginationResponse'
    NotificationReadResponse:
      description: Notification marked as read
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 200
              message:
                type: string
                example: Notification marked as read
    NotificationNotFound:
      description: Notification not found
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 404
              error:
                type: string
                example: Not Found
              message:
                type: string
                example: Notification not found or not authorized
    Unauthorized:
      description: Unauthorized - User is not authenticated
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 401
              error:
                type: string
                example: Unauthorized
              message:
                type: string
                example: Unauthorized - User is not authenticated
    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 500
              error:
                type: string
                example: Internal Server Error
              message:
                type: string
                example: An unexpected error occurred

  schemas:
    Notification:
      type: object
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        receiverUserId:
          type: string
          format: uuid
        type:
          type: string
        title:
          type: string
        body:
          type: string
        data:
          type: object
        isRead:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    PaginationResponse:
      type: object
      properties:
        total:
          type: integer
          description: Total number of notifications
          example: 42
        page:
          type: integer
          description: Current page number (1-based)
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages available
          example: 5
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: true
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false 