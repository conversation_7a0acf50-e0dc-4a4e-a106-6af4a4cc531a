/**
 * Admin Subscription Plan Controller
 *
 * Handles subscription plan-related HTTP requests
 */
const { ApiResponse } = require('@utils/response.utils');
const { SUBSCRIPTION_PLAN } = require('@utils/messages.utils');
const subscriptionPlanService = require('./subscription-plan.service');

/**
 * Subscription Plan controller
 */
const subscriptionPlanController = {
  /**
   * Seed subscription plans
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  seedSubscriptionPlans: async (req, res, next) => {
    try {
      const result = await subscriptionPlanService.seedPlans();
      return ApiResponse.success(res, SUBSCRIPTION_PLAN.SEEDED, result);
    } catch (error) {
      next(error);
    }
  },

  /**se
   * Get all subscription plans
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllSubscriptionPlans: async (req, res, next) => {
    try {
      const { search, targetUserType } = req.query;
      const { page, limit } = req.pagination;

      const result = await subscriptionPlanService.getAllPlans({
        page,
        limit,
        search,
        targetUserType,
      });

      return ApiResponse.success(
        res,
        SUBSCRIPTION_PLAN.ALL_RETRIEVED,
        result.plans,
        { pagination: result.pagination }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get subscription plan by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getSubscriptionPlanById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const plan = await subscriptionPlanService.getPlanById(id);
      if (!plan) {
        return ApiResponse.error(res, SUBSCRIPTION_PLAN.NOT_FOUND, null, 404);
      }
      return ApiResponse.success(res, SUBSCRIPTION_PLAN.RETRIEVED, plan);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create subscription plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createSubscriptionPlan: async (req, res, next) => {
    try {
      const plan = await subscriptionPlanService.createPlan(req.body);
      return ApiResponse.created(res, SUBSCRIPTION_PLAN.CREATED, plan);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update subscription plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateSubscriptionPlan: async (req, res, next) => {
    try {
      const { id } = req.params;
      const plan = await subscriptionPlanService.updatePlan(id, req.body);
      if (!plan) {
        return ApiResponse.error(res, SUBSCRIPTION_PLAN.NOT_FOUND, null, 404);
      }
      return ApiResponse.success(res, SUBSCRIPTION_PLAN.UPDATED, plan);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete subscription plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  deleteSubscriptionPlan: async (req, res, next) => {
    try {
      const { id } = req.params;
      const deleted = await subscriptionPlanService.deletePlan(id);
      if (!deleted) {
        return ApiResponse.error(res, SUBSCRIPTION_PLAN.NOT_FOUND, null, 404);
      }
      return ApiResponse.success(res, SUBSCRIPTION_PLAN.DELETED);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = subscriptionPlanController;
