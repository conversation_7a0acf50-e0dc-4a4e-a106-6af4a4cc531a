/**
 * Experience Controller
 *
 * Handles experience-related HTTP requests
 */
const experienceService = require('./experience.service');
const { ApiResponse } = require('@utils/response.utils');
const {
  EXPERIENCE,
  DISCUSSION,
  EXPERIENCE_REVIEW,
} = require('@utils/messages.utils');

/**
 * Experience controller
 */
const experienceController = {
  /**
   * Get all experiences with pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllExperiences: async (req, res, next) => {
    try {
      const { userId: queryUserId } = req.query;
      const result = await experienceService.getAllExperiences({
        ...req.query,
        ...req.pagination,
        userId: queryUserId,
      });

      return ApiResponse.success(
        res,
        EXPERIENCE.ALL_RETRIEVED,
        result.experiences,
        { pagination: result.pagination }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get complete experience details
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getExperienceDetails: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const userId = req.user.id;

      const experience = await experienceService.getExperienceDetails(
        experienceId,
        userId
      );

      return ApiResponse.success(res, EXPERIENCE.RETRIEVED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update an experience
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateExperience: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const data = req.body;

      const experience = await experienceService.updateExperience(
        experienceId,
        data
      );

      return ApiResponse.success(res, EXPERIENCE.UPDATED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get week details including weeklyWhy, insights and engagement stats
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getWeekDetails: async (req, res, next) => {
    try {
      const { weekId } = req.params;
      const { page, limit } = req.pagination;

      const result = await experienceService.getWeekDetails(weekId, {
        page,
        limit,
      });

      return ApiResponse.success(
        res,
        result.message,
        result.data,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get discussions by experience ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getExperienceDiscussions: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { page, limit } = req.pagination;

      const result = await experienceService.getExperienceDiscussions(
        experienceId,
        page,
        limit
      );

      return ApiResponse.success(
        res,
        DISCUSSION.ALL_RETRIEVED,
        result.discussions,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get reviews by experience ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getExperienceReviews: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { page, limit } = req.pagination;

      const result = await experienceService.getExperienceReviews(
        experienceId,
        page,
        limit
      );

      return ApiResponse.success(
        res,
        EXPERIENCE_REVIEW.RETRIEVED,
        result.data,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update experience status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateExperienceStatus: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { status } = req.body;

      const experience = await experienceService.updateExperienceStatus(
        experienceId,
        status
      );

      return ApiResponse.success(res, EXPERIENCE.STATUS_UPDATED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete an experience
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  deleteExperience: async (req, res, next) => {
    try {
      const { experienceId } = req.params;

      const result = await experienceService.deleteExperience(experienceId);

      return ApiResponse.success(res, result.message);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = experienceController;
