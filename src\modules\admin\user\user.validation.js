const { body, param, query } = require('express-validator');
const { VALIDATION } = require('@utils/messages.utils');
const { UserType } = require('@utils/enums.utils');
const { CreatedVia } = require('@utils/enums.utils');
const { AccountStatus } = require('@utils/enums.utils');

const userValidation = {
  /**
   * Validation for creating a user (admin)
   */
  createUser: [
    body('firstName').notEmpty().withMessage(VALIDATION.FIRST_NAME_REQUIRED),
    body('lastName').notEmpty().withMessage(VALIDATION.LAST_NAME_REQUIRED),
    body('email')
      .isEmail()
      .withMessage(VALIDATION.EMAIL_INVALID)
      .normalizeEmail()
      .toLowerCase(),
    body('userType')
      .isIn(UserType.values)
      .withMessage(VALIDATION.INVALID_USER_TYPE),
    body('focus').isArray().withMessage(VALIDATION.FOCUS_MUST_BE_ARRAY),
    body('focus.*').isUUID().withMessage(VALIDATION.INVALID_FOCUS_ID),
    body('state').notEmpty().withMessage(VALIDATION.STATE_REQUIRED),
    body('country').notEmpty().withMessage(VALIDATION.COUNTRY_REQUIRED),
    body('position')
      .if(body('userType').isIn([UserType.EDUCATOR, UserType.EDUCATOR_PLUS]))
      .notEmpty()
      .withMessage(VALIDATION.POSITION_REQUIRED),
    body('companyName')
      .if(body('userType').equals(UserType.PROVIDER_PLUS))
      .notEmpty()
      .withMessage(VALIDATION.COMPANY_NAME_REQUIRED),
    body('subscriptionPlanId')
      .optional()
      .isUUID()
      .withMessage('Invalid subscription plan ID'),
    body('currentPeriodStart')
      .optional()
      .isISO8601()
      .withMessage('Invalid currentPeriodStart date'),
    body('currentPeriodEnd')
      .optional()
      .isISO8601()
      .withMessage('Invalid currentPeriodEnd date'),
  ],

  /**
   * Validation for updating a user (admin)
   */
  updateUser: [
    param('userId').isUUID().withMessage(VALIDATION.INVALID_FORMAT),
    body('firstName')
      .optional()
      .notEmpty()
      .withMessage(VALIDATION.FIRST_NAME_REQUIRED),
    body('lastName')
      .optional()
      .notEmpty()
      .withMessage(VALIDATION.LAST_NAME_REQUIRED),
    body('email').optional().isEmail().withMessage(VALIDATION.EMAIL_INVALID),
    body('userType')
      .optional()
      .isIn(UserType.values)
      .withMessage(VALIDATION.INVALID_USER_TYPE),
    body('focus')
      .optional()
      .isArray()
      .withMessage(VALIDATION.FOCUS_MUST_BE_ARRAY),
    body('focus.*')
      .optional()
      .isUUID()
      .withMessage(VALIDATION.INVALID_FOCUS_ID),
    body('state').optional().notEmpty().withMessage(VALIDATION.STATE_REQUIRED),
    body('country')
      .optional()
      .notEmpty()
      .withMessage(VALIDATION.COUNTRY_REQUIRED),
    body('position')
      .optional()
      .if(body('userType').isIn([UserType.EDUCATOR, UserType.EDUCATOR_PLUS]))
      .notEmpty()
      .withMessage(VALIDATION.POSITION_REQUIRED),
    body('companyName')
      .optional()
      .if(body('userType').equals(UserType.PROVIDER_PLUS))
      .notEmpty()
      .withMessage(VALIDATION.COMPANY_NAME_REQUIRED),
    body('subscriptionPlanId')
      .optional()
      .isUUID()
      .withMessage('Invalid subscription plan ID'),
    body('currentPeriodStart')
      .optional()
      .isISO8601()
      .withMessage('Invalid currentPeriodStart date'),
    body('currentPeriodEnd')
      .optional()
      .isISO8601()
      .withMessage('Invalid currentPeriodEnd date'),
  ],

  /**
   * Validation for listing users (admin)
   */
  listUsers: [
    // Pagination
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Limit must be a positive integer'),
    // Search
    query('search')
      .optional()
      .isString()
      .withMessage('Search query must be a string'),
    // userType filter
    query('userType')
      .optional()
      .isIn(UserType.values)
      .withMessage(VALIDATION.INVALID_USER_TYPE),
    // createdVia filter
    query('createdVia')
      .optional()
      .isString()
      .isIn(CreatedVia.values)
      .withMessage('createdVia must be either USER or ADMIN'),
    // accountStatus filter
    query('accountStatus')
      .optional()
      .isIn(AccountStatus.values)
      .withMessage('Invalid account status'),
  ],

  /**
   * Validation for deleting a user (admin)
   */
  deleteUser: [param('userId').isUUID().withMessage(VALIDATION.INVALID_FORMAT)],
};

module.exports = userValidation;
