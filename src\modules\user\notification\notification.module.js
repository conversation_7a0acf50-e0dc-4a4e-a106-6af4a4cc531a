const express = require('express');
const router = express.Router();
const notificationController = require('./notification.controller');
const notificationValidation = require('./notification.validation');
const { authenticate } = require('@middlewares/auth.middleware');
const { validate } = require('@middlewares/validation.middleware');
const paginationMiddleware = require('@middlewares/pagination.middleware');

function registerRoutes() {
  // List notifications
  router.get(
    '/',
    authenticate,
    paginationMiddleware,
    validate(notificationValidation.list),
    notificationController.listNotifications
  );

  // Mark notification as read
  router.patch(
    '/:id/read',
    authenticate,
    validate(notificationValidation.read),
    notificationController.readNotification
  );

  return router;
}

module.exports = registerRoutes();
