/**
 * Admin Model
 */
const { Model, DataTypes } = require('sequelize');
const bcrypt = require('bcrypt');

class Admin extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        email: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          validate: {
            isEmail: true,
          },
          set(value) {
            this.setDataValue('email', value.toLowerCase());
          },
        },
        password: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        tokenVersion: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
        },
      },
      {
        sequelize,
        modelName: 'Admin',
        tableName: 'Admin',
        timestamps: true,
        hooks: {
          beforeSave: async (admin) => {
            if (admin.changed('password')) {
              admin.password = await bcrypt.hash(admin.password, 10);
            }
          },
        },
      }
    );
  }

  // Instance Methods
  async verifyPassword(password) {
    return bcrypt.compare(password, this.password);
  }

  toJSON() {
    const values = { ...this.get() };
    delete values.password;
    return values;
  }
}

module.exports = Admin;
