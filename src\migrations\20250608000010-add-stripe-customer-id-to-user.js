'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('User', 'stripeCustomerId', {
      type: Sequelize.STRING,
      allowNull: true,
      unique: true,
    });

    // Add index for faster lookups
    await queryInterface.addIndex('User', ['stripeCustomerId'], {
      name: 'user_stripe_customer_id_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove the index first
    await queryInterface.removeIndex('User', 'user_stripe_customer_id_idx');

    // Then remove the column
    await queryInterface.removeColumn('User', 'stripeCustomerId');
  },
};
