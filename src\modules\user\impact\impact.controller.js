/**
 * User Impact Controller
 *
 * Handles HTTP requests related to user impact and progress tracking
 */
const impactService = require('./impact.service');
const { ApiException } = require('@utils/exception.utils');
const { ApiResponse } = require('@utils/response.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { IMPACT } = require('@utils/messages.utils');

/**
 * Impact controller
 */
const impactController = {
  /**
   * Get user's impact data
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getImpact: async (req, res, next) => {
    try {
      const user = req.user;
      const impact = await impactService.getImpact(user);
      return ApiResponse.success(res, IMPACT.RETRIEVED, impact);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get user's achievements
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getAchievements: async (req, res, next) => {
    try {
      const user = req.user;
      const achievements = await impactService.getAchievements(user);
      return ApiResponse.success(res, IMPACT.RETRIEVED, achievements);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get milestone questions with options
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getMilestoneQuestions: async (req, res, next) => {
    try {
      const { milestoneId } = req.params;
      if (!milestoneId) {
        throw new ApiException(HttpStatus.BAD_REQUEST, IMPACT.INVALID_REQUEST);
      }
      const questions = await impactService.getMilestoneQuestions(milestoneId);
      return ApiResponse.success(res, IMPACT.RETRIEVED, questions);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get achievement questions with options
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getAchievementQuestions: async (req, res, next) => {
    try {
      const { achievementId } = req.params;
      const questions =
        await impactService.getAchievementQuestions(achievementId);

      return ApiResponse.success(
        res,
        IMPACT.ACHIEVEMENT_QUESTIONS_RETRIEVED,
        questions
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Move to next milestone
   * @param {Request} req - Express request object
   * @param {Response} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  moveToNextMilestone: async (req, res, next) => {
    try {
      const result = await impactService.moveToNextMilestone(
        req.user.id,
        req.params.milestoneId
      );
      return ApiResponse.success(res, result.message, result.nextMilestone);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Submit milestone questions
   * @param {Request} req - Express request object
   * @param {Response} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  submitMilestoneQuestions: async (req, res, next) => {
    try {
      const result = await impactService.submitMilestoneQuestions(
        req.user.id,
        req.params.milestoneId,
        req.body.answers
      );
      return ApiResponse.success(res, result.message, {
        milestoneId: result.milestoneId,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Submit achievement questions
   * @param {Request} req - Express request object
   * @param {Response} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  submitAchievementQuestions: async (req, res, next) => {
    try {
      const result = await impactService.submitAchievementQuestions(
        req.user.id,
        req.params.achievementId,
        req.body.answers
      );
      return ApiResponse.success(res, result.message, {
        achievementId: result.achievementId,
      });
    } catch (error) {
      next(error);
    }
  },
};

module.exports = impactController;
