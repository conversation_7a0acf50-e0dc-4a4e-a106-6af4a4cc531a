'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('EducatorQuestion', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      text: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      sourceUrl: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      pdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'PdCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'PENDING',
      },
      reviewedBy: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'Admin',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      reviewedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      rejectionReason: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      isRemoved: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      removedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex('EducatorQuestion', ['pdCategoryId'], {
      name: 'educator_question_pd_category_id_idx',
    });
    await queryInterface.addIndex('EducatorQuestion', ['createdBy'], {
      name: 'educator_question_created_by_idx',
    });
    await queryInterface.addIndex('EducatorQuestion', ['status'], {
      name: 'educator_question_status_idx',
    });
    await queryInterface.addIndex('EducatorQuestion', ['reviewedBy'], {
      name: 'educator_question_reviewed_by_idx',
    });
    await queryInterface.addIndex('EducatorQuestion', ['isRemoved'], {
      name: 'educator_question_is_removed_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('EducatorQuestion');
  },
};
