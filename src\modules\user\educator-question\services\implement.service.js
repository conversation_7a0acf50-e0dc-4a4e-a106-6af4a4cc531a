/**
 * Implement Service for EducatorQuestion
 * Handles business logic for implementing educator questions
 */
const educatorQuestionRepository = require('@models/repositories/educator-question.repository');
const { ApiException } = require('@utils/exception.utils');
const { EDUCATOR_QUESTION } = require('@utils/messages.utils');

const implementService = {
  /**
   * Toggle implement status for an educator question
   * @param {Object} user - User object
   * @param {string} educatorQuestionId - EducatorQuestion ID
   * @returns {Promise<Object>} Object with isImplemented status
   */
  toggleImplement: async (user, educatorQuestionId) => {
    // 1. Check if the educator question exists
    const question = await educatorQuestionRepository.findById(
      educatorQuestionId,
      user.id
    );
    if (!question) {
      throw new ApiException(404, EDUCATOR_QUESTION.NOT_FOUND);
    }
    // 2. Toggle implement
    const result = await educatorQuestionRepository.toggleImplement(
      user.id,
      educatorQuestionId
    );
    // 3. (Optional) Add achievement/notification logic here
    return result;
  },
};

module.exports = implementService;
