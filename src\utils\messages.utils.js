/**
 * Application Messages
 *
 * Contains all application-wide messages for consistency
 * This includes error messages, success messages, and other static text
 */

/**
 * Common messages used across the application
 */
const COMMON = {
  // Success messages
  SUCCESS: 'Operation completed successfully',
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',

  // Error messages
  INTERNAL_ERROR: 'Internal server error',
  NOT_FOUND: 'Resource not found',
  INVALID_REQUEST: 'Invalid request',
  VALIDATION_ERROR: 'Validation error',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Forbidden access',
  CONFLICT: 'Resource already exists',
};

/**
 * Authentication related messages
 */
const AUTH = {
  // Success messages
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  REGISTER_SUCCESS: 'Registration successful',
  PASSWORD_RESET_SUCCESS: 'Password reset successful',
  PASSWORD_RESET_EMAIL_SENT: 'Password reset email sent',
  PASSWORD_CHANGE_SUCCESS: 'Password changed successfully',
  PROFILE_RETRIEVED: 'Profile retrieved successfully',
  FORGOT_PASSWORD_EMAIL_SENT:
    'Password reset instructions have been sent to your email',

  // Error messages
  INVALID_CREDENTIALS: 'Invalid credentials',
  ACCOUNT_LOCKED: 'Account is locked',
  ACCOUNT_DISABLED: 'Account is disabled',
  ACCOUNT_ON_HOLD:
    'Your account is on hold and pending deletion. Please contact support to restore it.',
  ACCOUNT_DELETED: 'This account has been deleted.',
  EMAIL_ALREADY_EXISTS: 'Email already registered',
  INVALID_TOKEN: 'Invalid token',
  TOKEN_EXPIRED: 'Token has expired',
  PASSWORD_MISMATCH: 'Passwords do not match',
  PASSWORD_SAME: 'New password must be different from old password',
  PASSWORD_WEAK: 'Password does not meet security requirements',
  EMAIL_NOT_FOUND: 'Email not registered',
  AUTHENTICATION_FAILED: 'Authentication failed',
  UNAUTHORIZED: 'You do not have the required role to access this resource',
  INVALID_USER_TYPE: 'Invalid user type',
  POSITION_REQUIRED: 'Position is required for educators',
  COMPANY_NAME_REQUIRED: 'Company name is required for provider plus users',
  REGISTRATION_FAILED: 'Registration failed',
  TOKEN_GENERATION_FAILED: 'Token generation failed',
  INVALID_CURRENT_PASSWORD: 'Current password is incorrect',
  FORGOT_PASSWORD_FAILED: 'Failed to process forgot password request',
  PASSWORD_RESET_FAILED: 'Failed to reset password',
  INVALID_RESET_TOKEN: 'Invalid or expired reset token',
  RESET_TOKEN_EXPIRED: 'Password reset token has expired',
};

/**
 * Admin related messages
 */
const ADMIN = {
  // Success messages
  LOGIN_SUCCESS: 'Admin login successful',
  PROFILE_RETRIEVED: 'Admin profile retrieved successfully',
  ADMIN_CREATED: 'Admin created successfully',
  ADMIN_UPDATED: 'Admin updated successfully',
  ADMIN_DELETED: 'Admin deleted successfully',

  // Error messages
  ADMIN_NOT_FOUND: 'Admin not found',
  ADMIN_EXISTS: 'Admin already exists',
};

/**
 * User related messages
 */
const USER = {
  // Success messages
  PROFILE_UPDATED: 'Profile updated successfully',
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',
  USERS_RETRIEVED: 'Users retrieved successfully',
  USER_RETRIEVED: 'User retrieved successfully',
  ACCOUNT_DELETION_REQUESTED:
    'Your account is now on hold and pending deletion review.',

  // Error messages
  USER_NOT_FOUND: 'User not found',
  USER_EXISTS: 'User already exists',
  ACCOUNT_ALREADY_ON_HOLD: 'Account is already on hold.',
  INVALID_ACCOUNT_STATUS: 'Invalid account status.',
};

/**
 * Validation related messages
 */
const VALIDATION = {
  REQUIRED: 'This field is required',
  EMAIL_INVALID: 'Valid email is required',
  PASSWORD_LENGTH: 'Password must be at least 6 characters long',
  NAME_REQUIRED: 'Name is required',
  FIRST_NAME_REQUIRED: 'First name is required',
  LAST_NAME_REQUIRED: 'Last name is required',
  INVALID_FORMAT: 'Invalid format',
  INVALID_DATE: 'Invalid date format',
  INVALID_PHONE: 'Invalid phone number',
  INVALID_URL: 'Invalid URL format',
  INVALID_USER_TYPE: 'Invalid user type',
  FOCUS_MUST_BE_ARRAY: 'Focus must be an array',
  INVALID_FOCUS_ID: 'Invalid focus ID',
  STATE_REQUIRED: 'State is required',
  COUNTRY_REQUIRED: 'Country is required',
  POSITION_REQUIRED: 'Position is required for educators',
  COMPANY_NAME_REQUIRED: 'Company name is required for provider plus users',
  INVALID_BOOLEAN: 'Value must be a boolean',
  CURRENT_PASSWORD_REQUIRED: 'Current password is required',
  NEW_PASSWORD_SAME_AS_CURRENT:
    'New password must be different from current password',
  TOKEN_REQUIRED: 'Reset token is required',
};

/**
 * File upload related messages
 */
const UPLOAD = {
  // Success messages
  UPLOAD_SUCCESS: 'File uploaded successfully',
  FILE_UPLOADED: 'File uploaded successfully.',
  FILE_RETRIEVED: 'File URL retrieved successfully.',

  // Error messages
  UPLOAD_FAILED: 'File upload failed',
  INVALID_FILE_TYPE: 'Invalid file type',
  FILE_TOO_LARGE: 'File size exceeds limit',
  FILE_NOT_FOUND: 'File not found with provided key.',
};

/**
 * PD Category related messages
 */
const PD_CATEGORY = {
  // Success messages
  CREATED: 'PD category created successfully',
  UPDATED: 'PD category updated successfully',
  DELETED: 'PD category deleted successfully',
  RETRIEVED: 'PD category retrieved successfully',
  ALL_RETRIEVED: 'All PD categories retrieved successfully',

  // Error messages
  NOT_FOUND: 'PD category not found',
  ALREADY_EXISTS: 'PD category with this name already exists',
};

/**
 * Focus related messages
 */
const FOCUS = {
  // Success messages
  CREATED: 'Focus created successfully',
  UPDATED: 'Focus updated successfully',
  DELETED: 'Focus deleted successfully',
  RETRIEVED: 'Focus retrieved successfully',
  ALL_RETRIEVED: 'All focus areas retrieved successfully',

  // Error messages
  NOT_FOUND: 'Focus not found',
  ALREADY_EXISTS: 'Focus with this name already exists',
};

/**
 * WTD Category related messages
 */
const WTD_CATEGORY = {
  // Success messages
  CREATED: 'WTD category created successfully',
  UPDATED: 'WTD category updated successfully',
  DELETED: 'WTD category deleted successfully',
  RETRIEVED: 'WTD category retrieved successfully',
  ALL_RETRIEVED: 'All WTD categories retrieved successfully',

  // Error messages
  NOT_FOUND: 'WTD category not found',
  ALREADY_EXISTS: 'WTD category with this name already exists',
};

/**
 * Insight related messages
 */
const INSIGHT = {
  // Success messages
  CREATED: 'Insight created successfully',
  UPDATED: 'Insight updated successfully',
  DELETED: 'Insight deleted successfully',
  RETRIEVED: 'Insight retrieved successfully',
  ALL_RETRIEVED: 'All insights retrieved successfully',
  TRENDING_RETRIEVED: 'Trending insights retrieved successfully',
  APPROVED: 'Insight approved successfully',
  REJECTED: 'Insight rejected successfully',
  PENDING: 'Insight is pending review',
  BOOKMARK_ADDED: 'Insight bookmarked successfully',
  BOOKMARK_REMOVED: 'Insight bookmark removed successfully',
  BOOKMARKS_RETRIEVED: 'Bookmarked insights retrieved successfully',
  LIKE_ADDED: 'Insight liked successfully',
  LIKE_REMOVED: 'Insight like removed successfully',
  LIKES_RETRIEVED: 'Liked insights retrieved successfully',
  IMPLEMENT_ADDED: 'Insight marked as implemented successfully',
  IMPLEMENT_REMOVED: 'Insight implementation removed successfully',
  IMPLEMENTS_RETRIEVED: 'Implemented insights retrieved successfully',
  SOFT_REMOVED: 'Insight removed (soft delete) successfully',

  // Error messages
  NOT_FOUND: 'Insight not found',
  UNAUTHORIZED: 'Only provider plus users can manage insights',
  ALREADY_REVIEWED: 'Insight has already been reviewed',
  INVALID_STATUS: 'Invalid status value',
  REJECTION_REASON_REQUIRED: 'Rejection reason is required',
  CANNOT_EDIT_APPROVED: 'Cannot edit an approved insight',
  CANNOT_EDIT_REJECTED: 'Cannot edit a rejected insight',
  CANNOT_DELETE_APPROVED: 'Cannot delete an approved insight',
  CANNOT_DELETE_REJECTED: 'Cannot delete a rejected insight',
  MONTHLY_LIMIT_EXCEEDED:
    'Monthly insight viewing limit exceeded. Please upgrade your plan to view more insights.',
};

/**
 * Experience related messages
 */
const EXPERIENCE = {
  // Success messages
  CREATED: 'Experience created successfully',
  UPDATED: 'Experience updated successfully',
  DELETED: 'Experience deleted successfully',
  RETRIEVED: 'Experience retrieved successfully',
  ALL_RETRIEVED: 'All experiences retrieved successfully',
  WEEK_ADDED: 'Week added to experience successfully',
  WEEK_UPDATED: 'Experience week updated successfully',
  WEEK_DELETED: 'Experience week deleted successfully',
  INSIGHT_ADDED: 'Insight added to week successfully',
  INSIGHT_UPDATED: 'Experience insight updated successfully',
  INSIGHT_DELETED: 'Experience insight deleted successfully',
  MEDIA_ADDED: 'Media added successfully',
  MEDIA_UPDATED: 'Media updated successfully',
  MEDIA_DELETED: 'Media deleted successfully',
  STATUS_UPDATED: 'Experience status updated successfully',
  ALREADY_REVIEWED: 'Experience has already been reviewed',
  INVALID_STATUS: 'Invalid experience status',
  REJECTION_REASON_REQUIRED:
    'Rejection reason is required when rejecting an experience',

  // Error messages
  NOT_FOUND: 'Experience not found',
  WEEK_NOT_FOUND: 'Experience week not found',
  INSIGHT_NOT_FOUND: 'Experience insight not found',
  MEDIA_NOT_FOUND: 'Media not found',
  UNAUTHORIZED: 'Only the creator can manage this experience',
  INVALID_WEEK_NUMBER: 'Invalid week number for this experience',
  MAX_INSIGHTS_REACHED: 'Maximum 5 insights allowed per week',
  WEEK_NUMBER_EXISTS: 'Week number already exists for this experience',
  INVALID_PD_CATEGORY_IDS: 'Invalid PD Category IDs: %s',
  INVALID_WTD_CATEGORY_IDS: 'Invalid WTD Category IDs: %s',
  INVALID_FOCUS_IDS: 'Invalid Focus IDs: %s',
  INVALID_PD_CATEGORY_IDS_INSIGHT: 'Invalid PD Category IDs in insight: %s',
  INVALID_WTD_CATEGORY_IDS_INSIGHT: 'Invalid WTD Category IDs in insight: %s',
};

/**
 * Report messages
 */
const REPORT = {
  // Success messages
  INSIGHT_REPORTED: 'Insight reported successfully',
  CONTRIBUTION_REPORTED: 'Contribution reported successfully',
  REPORT_UPDATED: 'Report status updated successfully',
  REPORTS_RETRIEVED: 'Reports retrieved successfully',
  REPORT_RETRIEVED: 'Report retrieved successfully',
  REPORT_STATISTICS_RETRIEVED: 'Report statistics retrieved successfully',
  CONTRIBUTION_SOFT_REMOVED: 'Contribution removed (soft delete) successfully',
  CONTRIBUTION_DELETED: 'Contribution deleted successfully',

  // Error messages
  REPORT_NOT_FOUND: 'Report not found',
  ALREADY_REPORTED: 'You have already reported this content',
  CANNOT_REPORT_OWN_CONTENT: 'You cannot report your own content',
  INSIGHT_NOT_FOUND: 'Insight not found',
  CONTRIBUTION_NOT_FOUND: 'Contribution not found',
};

/**
 * Experience enrollment related messages
 */
const EXPERIENCE_ENROLLMENT = {
  // Success messages
  CREATED: 'User enrolled successfully',
  STATUS_UPDATED: 'Enrollment status updated successfully',
  ALL_RETRIEVED: 'User enrollments retrieved successfully',
  WEEK_COMPLETED: 'Week completed successfully',
  PROGRESS_RETRIEVED: 'Enrollment progress retrieved successfully',

  // Error messages
  NOT_FOUND: 'Enrollment not found',
  ALREADY_ENROLLED: 'User is already enrolled in this experience',
  NOT_AUTHORIZED: 'You are not authorized to modify this enrollment',
  EXPERIENCE_NOT_FOUND: 'Experience not found',
  INVALID_START_DATE: 'Start date must be a Monday',
  INVALID_STATUS: 'Status must be either REGISTERED or COMPLETED',
  CANNOT_ENROLL_OWN_EXPERIENCE: 'You cannot enroll in your own experience',
  WEEK_NOT_ACCESSIBLE:
    'You must complete previous weeks before accessing this week',
  WEEK_PROGRESS_NOT_FOUND: 'Week progress not found',
  EXPERIENCE_NO_WEEKS: 'Experience has no weeks defined',
  EDUCATOR_FREE_EXPERIENCE_RESTRICTED:
    'Unable to register for experiences on the Educator Free plan.',
  ONE_AT_A_TIME_LIMIT:
    'You can only be registered for one experience at a time. Complete or cancel your current experience to register for a new one.',
};

/**
 * Experience Review related messages
 */
const EXPERIENCE_REVIEW = {
  // Success messages
  CREATED: 'Experience review submitted successfully',

  // Error messages
  NOT_FOUND: 'Experience review not found',
  ALREADY_REVIEWED: 'You have already reviewed this experience',
  EXPERIENCE_NOT_FOUND: 'Experience not found',
  CANNOT_REVIEW_OWN_EXPERIENCE: 'You cannot review your own experience',
  NOT_ENROLLED: 'You must be enrolled in the experience to review it',
  MUST_COMPLETE_EXPERIENCE:
    'You must complete the experience before reviewing it',
};

/**
 * Onboarding Config related messages
 */
const ONBOARDING_CONFIG = {
  // Success messages
  CREATED: 'Onboarding config created successfully',
  UPDATED: 'Onboarding config updated successfully',
  DELETED: 'Onboarding config deleted successfully',
  RETRIEVED: 'Onboarding config retrieved successfully',
  ALL_RETRIEVED: 'All onboarding configs retrieved successfully',

  // Error messages
  NOT_FOUND: 'Onboarding config not found',
  ALREADY_EXISTS: (userType) =>
    `Onboarding config already exists for user type: ${userType}`,
};

/**
 * Follow related messages
 */
const FOLLOW = {
  // Success messages
  FOLLOWED: 'User followed successfully',
  UNFOLLOWED: 'User unfollowed successfully',
  FOLLOWERS_RETRIEVED: 'Followers retrieved successfully',
  FOLLOWING_RETRIEVED: 'Following users retrieved successfully',

  // Error messages
  NOT_FOUND: 'User not found',
  ALREADY_FOLLOWING: 'You are already following this user',
  NOT_FOLLOWING: 'You are not following this user',
  CANNOT_FOLLOW_SELF: 'You cannot follow yourself',
};

/**
 * Experience Discussion related messages
 */
const DISCUSSION = {
  // Success messages
  CREATED: 'Discussion created successfully',
  UPDATED: 'Discussion updated successfully',
  DELETED: 'Discussion deleted successfully',
  RETRIEVED: 'Discussion retrieved successfully',
  ALL_RETRIEVED: 'All discussions retrieved successfully',
  LIKE_ADDED: 'Discussion liked successfully',
  LIKE_REMOVED: 'Discussion like removed successfully',

  // Error messages
  NOT_FOUND: 'Discussion not found',
  UNAUTHORIZED: 'Only the creator can manage this discussion',
};

const IMPACT = {
  RETRIEVED: 'Impact data retrieved successfully',
  NOT_FOUND: 'Impact data not found',
  RETRIEVAL_FAILED: 'Failed to retrieve impact data',
  NO_NEXT_MILESTONE: 'No next milestone available',
  QUESTIONS_REQUIRED:
    'Questions must be submitted before moving to next milestone',
  QUESTIONS_SUBMITTED: 'Questions submitted successfully',
  QUESTIONS_NOT_FOUND: 'Questions not found for this milestone',
  QUESTION_NOT_FOUND: 'Question not found',
  OPTION_NOT_FOUND: 'Selected option not found for this question',
  QUESTIONS_ALREADY_ANSWERED:
    'Questions for this milestone have already been answered',
  EDUCATOR_FREE_MILESTONE_LIMIT:
    'Educator Free plan users can only complete up to 4 milestones. Upgrade to unlock more.',
  ACHIEVEMENT_NOT_FOUND: 'Achievement not found',
  ACHIEVEMENT_QUESTIONS_NOT_FOUND: 'Achievement question(s) not found',
  MISSING_REQUIRED_ACHIEVEMENT_QUESTIONS: (ids) =>
    `Missing answers for required questions: ${ids.join(', ')}`,
  ACHIEVEMENT_QUESTIONS_RETRIEVED:
    'Achievement questions retrieved successfully',
};

/**
 * Subscription Plan related messages
 */
const SUBSCRIPTION_PLAN = {
  // Success messages
  CREATED: 'Subscription plan created successfully',
  UPDATED: 'Subscription plan updated successfully',
  DELETED: 'Subscription plan deleted successfully',
  RETRIEVED: 'Subscription plan retrieved successfully',
  ALL_RETRIEVED: 'All subscription plans retrieved successfully',
  SEEDED: 'Subscription plans seeded successfully',

  // Error messages
  NOT_FOUND: 'Subscription plan not found',
  ALREADY_EXISTS: 'Subscription plan already exists',
  SEEDING_FAILED: 'Failed to seed subscription plans',
  PLAN_EXISTS: (slug) =>
    `Plan with slug "${slug}" already exists. Seeding aborted to prevent duplicate plans.`,
  SEEDING_ERROR: (slug, error) => `Failed to seed plan "${slug}": ${error}`,
};

/**
 * Payment Method related messages
 */
const PAYMENT_METHOD = {
  // Success messages
  CREATED: 'Payment method created successfully',
  UPDATED: 'Payment method updated successfully',
  DELETED: 'Payment method deleted successfully',
  RETRIEVED: 'Payment method retrieved successfully',
  ALL_RETRIEVED: 'All payment methods retrieved successfully',
  SET_DEFAULT: 'Payment method set as default successfully',

  // Error messages
  NOT_FOUND: 'Payment method not found',
  CREATE_ERROR: 'Error creating payment method',
  UPDATE_ERROR: 'Error updating payment method',
  DELETE_ERROR: 'Error deleting payment method',
  FIND_ERROR: 'Error finding payment method',
  SET_DEFAULT_ERROR: 'Error setting payment method as default',
  INVALID_CARD: 'Invalid card details',
  NO_STRIPE_CUSTOMER: 'User does not have a Stripe customer ID',
  CARD_ALREADY_EXISTS: 'This card is already added to your account',
};

/**
 * Subscription related messages
 */
const SUBSCRIPTION = {
  // Success messages
  CREATED: 'Subscription created successfully',
  UPDATED: 'Subscription updated successfully',
  DELETED: 'Subscription deleted successfully',
  RETRIEVED: 'Subscription retrieved successfully',
  ALL_RETRIEVED: 'All subscriptions retrieved successfully',
  ACTIVE_RETRIEVED: 'Active subscription retrieved successfully',
  CANCELED: 'Subscription canceled successfully',
  PAUSED: 'Subscription paused successfully',
  RESUMED: 'Subscription resumed successfully',
  PAYMENT_METHOD_UPDATED: 'Payment method updated successfully',
  PAYMENT_INTENT_RETRIEVED: 'Payment intent retrieved successfully',
  WEBHOOK_RECEIVED: 'Webhook event received successfully',
  UPGRADED: 'Subscription upgraded successfully',

  // Error messages
  NOT_FOUND: 'Subscription not found',
  PLAN_NOT_FOUND: 'Subscription plan not found',
  PAYMENT_METHOD_NOT_FOUND: 'Payment method not found',
  NO_STRIPE_SUBSCRIPTION_ID:
    'Subscription does not have a Stripe subscription ID',
  NO_LATEST_INVOICE: 'No invoice found for subscription',
  NO_PAYMENT_INTENT: 'No payment intent found for subscription',
  ALREADY_CANCELED: 'Subscription is already canceled',
  ALREADY_PAUSED: 'Subscription is already paused',
  ALREADY_ACTIVE: 'Subscription is already active',
  CREATION_FAILED: 'Failed to create subscription',
  UPDATE_FAILED: 'Failed to update subscription',
  DELETION_FAILED: 'Failed to delete subscription',
  RETRIEVAL_FAILED: 'Failed to retrieve subscription',
  CANCELLATION_FAILED: 'Failed to cancel subscription',
  PAUSE_FAILED: 'Failed to pause subscription',
  RESUME_FAILED: 'Failed to resume subscription',
  PAYMENT_METHOD_UPDATE_FAILED: 'Failed to update payment method',
  PAYMENT_INTENT_RETRIEVAL_FAILED: 'Failed to retrieve payment intent',
  WEBHOOK_HANDLING_FAILED: 'Failed to handle webhook event',
  INVALID_STATUS: 'Invalid subscription status',
  INVALID_PAYMENT_METHOD: 'Invalid payment method',
  INVALID_PLAN: 'Invalid subscription plan',
  INVALID_AMOUNT: 'Invalid subscription amount',
  INVALID_CURRENCY: 'Invalid subscription currency',
  INVALID_PERIOD: 'Invalid subscription period',
  INVALID_DATE: 'Invalid subscription date',
  INVALID_USER: 'Invalid user for subscription',
  INVALID_STRIPE_DATA: 'Invalid Stripe subscription data',
  STRIPE_ERROR: 'Stripe operation failed',
  CANNOT_PAUSE_CANCELED: 'Cannot pause subscription: subscription is canceled',
  CANNOT_RESUME_CANCELED:
    'Cannot resume subscription: subscription is canceled',
  CANCELLATION_ALREADY_INITIATED:
    'Subscription cancellation has already been initiated',
  ALREADY_ON_THIS_PLAN: 'Already on this plan',
  INVALID_STRIPE_UPGRADE:
    'Cannot upgrade: subscription is not a valid paid Stripe subscription.',
};

/**
 * Notification related messages
 */
const NOTIFICATION = {
  NEW_CONTRIBUTION_TITLE: 'New Contribution',
  INSIGHT_IMPLEMENTED_TITLE: 'Insight Implemented',
  INSIGHT_APPROVED_TITLE: 'Insight Approved',
  EXPERIENCE_COMPLETED_TITLE: 'Experience Completed',
  EXPERIENCE_APPROVED_TITLE: 'Experience Approved',
  NEW_FOLLOWERS_TITLE: 'New Followers',
  NEW_FOLLOWERS_MONTHLY_TITLE: 'New Followers This Month',
  CONTRIBUTION_LIKED_TITLE: 'Contribution Liked',
  EXPERIENCE_START_REMINDER_TITLE: 'Experience Starting Soon',
  EXPERIENCE_STARTED_TITLE: 'Experience Started',
  MILESTONE_COMPLETED_TITLE: 'Milestone Completed',
  ALL_RETRIEVED: 'Notifications retrieved successfully',
  READ: 'Notification marked as read',
  NOTIFICATION_NOT_FOUND: 'Notification not found.',
};

/**
 * EducatorQuestion related messages
 */
const EDUCATOR_QUESTION = {
  CREATED: 'Educator question created successfully',
  UPDATED: 'Educator question updated successfully',
  DELETED: 'Educator question deleted successfully',
  RETRIEVED: 'Educator question retrieved successfully',
  ALL_RETRIEVED: 'All educator questions retrieved successfully',
  BOOKMARK_ADDED: 'Educator question bookmarked successfully',
  BOOKMARK_REMOVED: 'Educator question bookmark removed successfully',
  BOOKMARKS_RETRIEVED: 'Bookmarked educator questions retrieved successfully',
  LIKE_ADDED: 'Educator question liked successfully',
  LIKE_REMOVED: 'Educator question like removed successfully',
  LIKES_RETRIEVED: 'Liked educator questions retrieved successfully',
  IMPLEMENT_ADDED: 'Educator question marked as implemented successfully',
  IMPLEMENT_REMOVED: 'Educator question implementation removed successfully',
  IMPLEMENTS_RETRIEVED: 'Implemented educator questions retrieved successfully',

  // Error messages
  NOT_FOUND: 'Educator question not found',
};

/**
 * EducatorQuestion Notification related messages
 */
const EDUCATOR_QUESTION_NOTIFICATION = {
  QUESTION_APPROVED_TITLE: 'Educator Question Approved',
  // Add more notification titles as needed
};

module.exports = {
  COMMON,
  AUTH,
  ADMIN,
  USER,
  VALIDATION,
  UPLOAD,
  PD_CATEGORY,
  FOCUS,
  WTD_CATEGORY,
  INSIGHT,
  EXPERIENCE,
  EXPERIENCE_ENROLLMENT,
  REPORT,
  EXPERIENCE_REVIEW,
  ONBOARDING_CONFIG,
  FOLLOW,
  DISCUSSION,
  IMPACT,
  SUBSCRIPTION_PLAN,
  PAYMENT_METHOD,
  SUBSCRIPTION,
  NOTIFICATION,
  EDUCATOR_QUESTION,
  EDUCATOR_QUESTION_NOTIFICATION,
};
