/**
 * User Subscription Module
 *
 * This module handles user subscription-related functionality
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('@middlewares/auth.middleware');
const { validate } = require('@middlewares/validation.middleware');
const subscriptionController = require('./subscription.controller');
const subscriptionValidation = require('./subscription.validation');
const paginationMiddleware = require('@middlewares/pagination.middleware');

/**
 * Register routes
 */
function registerRoutes() {
  // Create a new subscription
  router.post(
    '/',
    authenticate,
    validate(subscriptionValidation.createSubscription),
    subscriptionController.createSubscription
  );

  // Get active subscription
  router.get(
    '/active',
    authenticate,
    subscriptionController.getActiveSubscription
  );

  // Get all user subscriptions
  router.get(
    '/',
    authenticate,
    paginationMiddleware,
    validate(subscriptionValidation.getAll),
    subscriptionController.getUserSubscriptions
  );

  // Update payment method
  router.put(
    '/:subscriptionId/payment-method',
    authenticate,
    validate(subscriptionValidation.updatePaymentMethod),
    subscriptionController.updatePaymentMethod
  );

  // Pause a subscription
  router.patch(
    '/:subscriptionId/pause',
    authenticate,
    validate(subscriptionValidation.pauseSubscription),
    subscriptionController.pauseSubscription
  );

  // Resume a subscription
  router.patch(
    '/:subscriptionId/resume',
    authenticate,
    validate(subscriptionValidation.resumeSubscription),
    subscriptionController.resumeSubscription
  );

  // Cancel a subscription
  router.post(
    '/:subscriptionId/cancel',
    authenticate,
    validate(subscriptionValidation.cancelSubscription),
    subscriptionController.cancelSubscription
  );

  // Upgrade a subscription's plan
  router.post(
    '/:subscriptionId/upgrade',
    authenticate,
    validate(subscriptionValidation.upgradeSubscription),
    subscriptionController.upgradeSubscription
  );

  // Handle Stripe webhook
  router.post('/webhook', subscriptionController.handleWebhook);

  return router;
}

module.exports = registerRoutes();
