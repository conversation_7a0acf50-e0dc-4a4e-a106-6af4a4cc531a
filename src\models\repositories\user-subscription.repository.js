/* eslint-disable camelcase */
/**
 * User Subscription Repository
 * Handles database operations for UserSubscription model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { HttpStatus, SubscriptionStatus } = require('@utils/enums.utils');
const { SUBSCRIPTION } = require('@utils/messages.utils');
const { mapStripeStatus } = require('@utils/subscription.utils');
const commonRepository = require('./common.repository');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      UserSubscription: databaseService.getUserSubscriptionModel(),
      SubscriptionPlan: databaseService.getSubscriptionPlanModel(),
      PaymentMethod: databaseService.getPaymentMethodModel(),
      User: databaseService.getUserModel(),
    };
  }

  /**
   * Find a record by primary key
   * @param {string} id - Record ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Found record
   */
  async findByPk(id, options = {}) {
    try {
      return await this.models.UserSubscription.findByPk(id, options);
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Find a record by conditions
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Found record
   */
  async findOne(options = {}) {
    try {
      return await this.models.UserSubscription.findOne(options);
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Find all records by conditions
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Found records
   */
  async findAll(options = {}) {
    try {
      return await this.models.UserSubscription.findAll(options);
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Create a new record
   * @param {Object} data - Record data
   * @param {Object} options - Create options
   * @returns {Promise<Object>} Created record
   */
  async create(data, options = {}) {
    try {
      const subscription = await this.models.UserSubscription.create(
        data,
        options
      );
      return subscription;
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.CREATION_FAILED
      );
    }
  }

  /**
   * Update a record
   * @param {Object} data - Update data
   * @param {Object} options - Update options
   * @returns {Promise<Array>} Update result
   */
  async update(data, options = {}) {
    try {
      return await this.models.UserSubscription.update(data, options);
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.UPDATE_FAILED
      );
    }
  }
}

/**
 * Subscription Helper class for subscription-specific utilities
 */
class SubscriptionHelper {
  constructor(models) {
    this.models = models;
  }

  /**
   * Get subscription attributes for queries
   * @returns {Object} Attributes configuration
   */
  getSubscriptionAttributes() {
    return {
      exclude: ['stripeSubscriptionDetails'],
    };
  }

  /**
   * Get subscription includes for queries
   * @returns {Array} Includes configuration
   */
  getSubscriptionIncludes() {
    return [
      {
        model: this.models.SubscriptionPlan,
        as: 'plan',
        attributes: [
          'id',
          'name',
          'description',
          'price',
          'billingInterval',
          'trialDays',
          'insightsLimit',
          'insightsLimitPeriod',
          'targetUserType',
          'canRegisterExperiences',
          'isActive',
          'slug',
        ],
      },
      {
        model: this.models.PaymentMethod,
        as: 'paymentMethod',
        attributes: ['id', 'type', 'last4', 'expMonth', 'expYear', 'brand'],
      },
      {
        model: this.models.User,
        as: 'user',
        attributes: ['id', 'email', 'firstName', 'lastName', 'userType'],
      },
    ];
  }

  /**
   * Convert Stripe timestamps to Date objects
   * @param {Object} stripeData - Stripe data object
   * @returns {Object} Processed data with Date objects
   */
  processStripeTimestamps(stripeData) {
    const processedData = {};

    // Get period timestamps from the first subscription item
    if (stripeData.items?.data?.[0]) {
      const subscriptionItem = stripeData.items.data[0];
      if (subscriptionItem.current_period_start) {
        processedData.currentPeriodStart = new Date(
          subscriptionItem.current_period_start * 1000
        );
      }
      if (subscriptionItem.current_period_end) {
        processedData.currentPeriodEnd = new Date(
          subscriptionItem.current_period_end * 1000
        );
      }
    }

    if (stripeData.canceled_at) {
      processedData.canceledAt = new Date(stripeData.canceled_at * 1000);
    }

    if (stripeData.trial_start) {
      processedData.trialStart = new Date(stripeData.trial_start * 1000);
    }

    if (stripeData.trial_end) {
      processedData.trialEnd = new Date(stripeData.trial_end * 1000);
    }

    return processedData;
  }
}

/**
 * User Subscription Repository class
 */
class UserSubscriptionRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new SubscriptionHelper(this.models);
  }

  /**
   * Create a new subscription
   * @param {Object} user - User object
   * @param {Object} subscriptionRequest - Subscription request data
   * @param {Object} stripeData - Stripe subscription data
   * @returns {Promise<Object>} Created subscription
   */
  async createSubscription(user, subscriptionRequest, stripeData) {
    try {
      const { planId, paymentMethodId, previousSubscriptionId } =
        subscriptionRequest;

      // Get the plan to check if it's free
      const plan = await this.getPlanById(planId);
      const isFreePlan = Number(plan.price) === 0;

      // Prepare subscription data
      const subscriptionData = {
        userId: user.id,
        subscriptionPlanId: planId,
        status: isFreePlan
          ? SubscriptionStatus.ACTIVE
          : mapStripeStatus(stripeData.status),
        currentPeriodStart: new Date(),
        previousSubscriptionId: previousSubscriptionId || null,
      };

      // Add Stripe-related data only for paid plans
      if (!isFreePlan) {
        subscriptionData.stripeSubscriptionId = stripeData.id;
        subscriptionData.stripeSubscriptionDetails = stripeData;
        subscriptionData.cancelAtPeriodEnd = stripeData.cancel_at_period_end;
        subscriptionData.paymentMethodId = paymentMethodId;
        subscriptionData.canceledAt =
          this.helper.processStripeTimestamps(stripeData).canceledAt;
        subscriptionData.trialStart =
          this.helper.processStripeTimestamps(stripeData).trialStart;
        subscriptionData.trialEnd =
          this.helper.processStripeTimestamps(stripeData).trialEnd;
      }

      // Create the subscription
      const subscription = await this.create(subscriptionData);

      // Return the created subscription with includes
      return await this.findByPk(subscription.id, {
        attributes: this.helper.getSubscriptionAttributes(),
        include: this.helper.getSubscriptionIncludes(),
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get subscription by ID
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise<Object>} Subscription details
   */
  async getSubscriptionById(subscriptionId) {
    try {
      const subscription = await this.findByPk(subscriptionId, {
        attributes: this.helper.getSubscriptionAttributes(),
        include: this.helper.getSubscriptionIncludes(),
      });
      if (!subscription) {
        throw new ApiException(HttpStatus.NOT_FOUND, SUBSCRIPTION.NOT_FOUND);
      }
      return subscription;
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get subscription by Stripe ID
   * @param {string} stripeSubscriptionId - Stripe subscription ID
   * @returns {Promise<Object>} Subscription details
   */
  async getSubscriptionByStripeId(stripeSubscriptionId) {
    try {
      return await this.findOne({
        where: { stripeSubscriptionId },
        attributes: this.helper.getSubscriptionAttributes(),
        include: this.helper.getSubscriptionIncludes(),
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get all subscriptions for a user with pagination
   * @param {string} userId - User ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Subscriptions with pagination info
   */
  async getSubscriptionByUserId(userId, page = 1, limit = 10) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } =
        await this.models.UserSubscription.findAndCountAll({
          where: { userId },
          attributes: this.helper.getSubscriptionAttributes(),
          include: this.helper.getSubscriptionIncludes(),
          order: [['createdAt', 'DESC']],
          limit,
          offset,
        });

      return {
        subscriptions: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Get active subscription for a user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Active subscription
   */
  async getActiveSubscription(userId) {
    try {
      return await this.findOne({
        where: {
          userId,
          status: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING],
        },
        attributes: this.helper.getSubscriptionAttributes(),
        include: this.helper.getSubscriptionIncludes(),
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get subscription plan by ID
   * @param {number} planId - Plan ID
   * @returns {Promise<Object>} Subscription plan
   */
  async getPlanById(planId) {
    try {
      const plan = await this.models.SubscriptionPlan.findByPk(planId);
      if (!plan) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          SUBSCRIPTION.PLAN_NOT_FOUND
        );
      }
      return plan;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get payment method by ID
   * @param {string} paymentMethodId - Payment method ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Payment method
   */
  async getPaymentMethodById(paymentMethodId, userId) {
    try {
      const paymentMethod = await this.models.PaymentMethod.findOne({
        where: {
          id: paymentMethodId,
          userId,
        },
      });
      if (!paymentMethod) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          SUBSCRIPTION.PAYMENT_METHOD_NOT_FOUND
        );
      }
      return paymentMethod;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update a subscription
   * @param {string} subscriptionId - Subscription ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated subscription
   */
  async updateSubscription(subscriptionId, updateData) {
    try {
      // First find the subscription to ensure it exists
      const subscription = await this.findByPk(subscriptionId);
      if (!subscription) {
        throw new ApiException(HttpStatus.NOT_FOUND, SUBSCRIPTION.NOT_FOUND);
      }

      // Update the subscription
      await subscription.update(updateData);

      // Return the updated subscription with includes
      return await this.getSubscriptionById(subscriptionId);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Upgrade a subscription's plan (full logic)
   * @param {Object} options - { user, subscriptionId, planId, paymentMethodId }
   * @returns {Promise<Object>} Upgraded subscription
   */
  async getSubscriptionWithStripeDetails(
    user,
    subscriptionId,
    planId,
    paymentMethodId
  ) {
    try {
      const subscription = await this.models.UserSubscription.findOne({
        where: {
          id: subscriptionId,
          userId: user.id,
        },
        include: this.helper.getSubscriptionIncludes(),
      });

      if (!subscription) {
        throw new ApiException(HttpStatus.NOT_FOUND, SUBSCRIPTION.NOT_FOUND);
      }

      // Fetch new plan
      const newPlan = await this.getPlanById(planId);

      // Block same plan
      if (subscription.subscriptionPlanId === planId) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          SUBSCRIPTION.ALREADY_ON_THIS_PLAN
        );
      }

      // Get payment method
      let paymentMethod = null;
      if (paymentMethodId) {
        paymentMethod = await this.getPaymentMethodById(
          paymentMethodId,
          user.id
        );
      } else if (subscription.paymentMethod) {
        paymentMethod = subscription.paymentMethod;
      }

      return { subscription, paymentMethod, newPlan };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update the currentStep field for a user
   * @param {string} userId - User ID
   * @param {number} currentStep - The new currentStep value
   * @returns {Promise<void>}
   */
  async updateUserCurrentStep(userId, currentStep) {
    try {
      await this.models.User.update({ currentStep }, { where: { id: userId } });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update the userType for a user
   * @param {string} userId - User ID
   * @param {string} userType - New userType
   * @returns {Promise<void>}
   */
  async updateUserType(userId, userType) {
    await this.models.User.update({ userType }, { where: { id: userId } });
  }
}

module.exports = new UserSubscriptionRepository();
