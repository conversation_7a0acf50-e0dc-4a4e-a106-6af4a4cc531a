/**
 * EducatorQuestionContributionLike Model
 * Represents the many-to-many relationship between User and EducatorQuestionContribution for likes
 */
const { Model, DataTypes } = require('sequelize');

class EducatorQuestionContributionLike extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        contributionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'EducatorQuestionContribution',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'EducatorQuestionContributionLike',
        tableName: 'EducatorQuestionContributionLike',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'contributionId'],
            name: 'educator_question_contribution_like_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // The associations are defined in the User and EducatorQuestionContribution models
  }
}

module.exports = EducatorQuestionContributionLike;
