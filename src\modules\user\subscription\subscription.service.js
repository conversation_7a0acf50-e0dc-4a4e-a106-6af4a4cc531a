/* eslint-disable camelcase */
/**
 * User Subscription Service
 *
 * Handles business logic for user subscriptions
 */
const userSubscriptionRepository = require('@models/repositories/user-subscription.repository');
const { ApiException } = require('@utils/exception.utils');
const {
  HttpStatus,
  SubscriptionStatus,
  UserType,
  SubscriptionPlanSlug,
  PauseBehavior,
} = require('@utils/enums.utils');
const { SUBSCRIPTION } = require('@utils/messages.utils');
const stripeSubscription = require('@integrations/stripe/stripe.subscription');
const { mapStripeStatus } = require('@utils/subscription.utils');

/**
 * Webhook Handler Class
 * Handles all webhook-related operations for subscriptions
 */
class WebhookHandler {
  constructor() {
    this.userSubscriptionRepository = userSubscriptionRepository;
  }

  /**
   * Handle subscription updated webhook event
   * @param {Object} subscription - Stripe subscription object
   * @returns {Promise<Object>} Updated subscription
   */
  async handleSubscriptionUpdated(subscription) {
    try {
      const userSubscription =
        await this.userSubscriptionRepository.getSubscriptionByStripeId(
          subscription.id
        );
      if (!userSubscription) {
        throw new ApiException(HttpStatus.NOT_FOUND, SUBSCRIPTION.NOT_FOUND);
      }

      // If subscription is already canceled, don't update it
      if (userSubscription.status === SubscriptionStatus.CANCELED) {
        return userSubscription;
      }

      // Map Stripe status to our status
      const status = mapStripeStatus(subscription.status);

      // Process timestamps and prepare update data
      const timestamps =
        this.userSubscriptionRepository.helper.processStripeTimestamps(
          subscription
        );

      const updateData = {
        status,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        currentPeriodStart: timestamps.currentPeriodStart,
        currentPeriodEnd: timestamps.currentPeriodEnd,
        canceledAt: timestamps.canceledAt,
        trialStart: timestamps.trialStart,
        trialEnd: timestamps.trialEnd,
        // Handle pause state
        pausedAt: subscription.pause_collection ? new Date() : null,
        pauseBehavior:
          subscription.pause_collection?.behavior?.toUpperCase() || null,
        resumedAt:
          !subscription.pause_collection && userSubscription.pausedAt
            ? new Date()
            : null,
        stripeSubscriptionDetails: subscription,
      };

      return await this.userSubscriptionRepository.updateSubscription(
        userSubscription.id,
        updateData
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * Handle subscription deleted webhook event
   * @param {Object} subscription - Stripe subscription object
   * @returns {Promise<Object>} Updated subscription
   */
  async handleSubscriptionDeleted(subscription) {
    try {
      const userSubscription =
        await this.userSubscriptionRepository.getSubscriptionByStripeId(
          subscription.id
        );
      if (!userSubscription) {
        throw new ApiException(HttpStatus.NOT_FOUND, SUBSCRIPTION.NOT_FOUND);
      }

      // If subscription is already canceled, don't update it
      if (userSubscription.status === SubscriptionStatus.CANCELED) {
        return userSubscription;
      }

      return await this.userSubscriptionRepository.updateSubscription(
        userSubscription.id,
        {
          status: SubscriptionStatus.CANCELED,
          canceledAt: new Date(),
        }
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * Handle payment succeeded event
   * @param {Object} invoice - Stripe invoice object
   * @returns {Promise<void>}
   */
  async handlePaymentSucceeded(invoice) {
    try {
      if (invoice.subscription) {
        const userSubscription =
          await this.userSubscriptionRepository.getSubscriptionByStripeId(
            invoice.subscription
          );
        if (userSubscription) {
          await this.userSubscriptionRepository.updateSubscription(
            userSubscription.id,
            {
              status: SubscriptionStatus.ACTIVE,
            }
          );
        }
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Handle payment failed event
   * @param {Object} invoice - Stripe invoice object
   * @returns {Promise<void>}
   */
  async handlePaymentFailed(invoice) {
    try {
      if (invoice.subscription) {
        const userSubscription =
          await this.userSubscriptionRepository.getSubscriptionByStripeId(
            invoice.subscription
          );
        if (userSubscription) {
          await this.userSubscriptionRepository.updateSubscription(
            userSubscription.id,
            {
              status: SubscriptionStatus.PAST_DUE,
            }
          );
        }
      }
    } catch (error) {
      throw error;
    }
  }
}

/**
 * User Subscription Service
 *
 * Handles business logic for user subscriptions
 */
const subscriptionService = {
  /**
   * Update user type based on subscription plan
   * @param {Object} user - User object
   * @param {Object} plan - Subscription plan object
   * @returns {Promise<void>}
   */
  updateUserType: async (user, plan) => {
    try {
      const plusAnnualSlug =
        SubscriptionPlanSlug.EDUCATOR_PLUS_ANNUAL.toLowerCase();
      const plus3MonthSlug =
        SubscriptionPlanSlug.EDUCATOR_PLUS_3_MONTH.toLowerCase();
      const plus3MonthPassSlug = 'educator_plus_3month_pass';
      const freeSlug = SubscriptionPlanSlug.EDUCATOR_FREE.toLowerCase();
      switch (true) {
        case user.userType === UserType.EDUCATOR &&
          (plan.slug === plusAnnualSlug ||
            plan.slug === plus3MonthSlug ||
            plan.slug === plus3MonthPassSlug):
          await userSubscriptionRepository.updateUserType(
            user.id,
            UserType.EDUCATOR_PLUS
          );
          break;
        case user.userType === UserType.EDUCATOR_PLUS && plan.slug === freeSlug:
          await userSubscriptionRepository.updateUserType(
            user.id,
            UserType.EDUCATOR
          );
          break;
        default:
        // No userType change needed
      }
    } catch (error) {
      console.error(
        '[Subscription] Error updating user type for user',
        user.id,
        ':',
        error
      );
      // Don't throw error as this is not critical for subscription upgrade
    }
  },

  /**
   * Create a new subscription
   * @param {Object} user - User object
   * @param {Object} body - Request body
   * @returns {Promise<Object>} Created subscription
   */
  createSubscription: async (user, body) => {
    try {
      const { planId, paymentMethodId, currentStep } = body;

      // If currentStep is provided, update the user
      if (typeof currentStep !== 'undefined') {
        await userSubscriptionRepository.updateUserCurrentStep(
          user.id,
          currentStep
        );
      }

      // Get and validate plan
      const plan = await userSubscriptionRepository.getPlanById(planId);

      // Check for existing active subscription
      const existingSubscription =
        await userSubscriptionRepository.getActiveSubscription(user.id);

      // If there's an existing active subscription, cancel it
      if (existingSubscription) {
        // Check if this is an offline subscription (no Stripe ID)
        const isOffline = !existingSubscription.stripeSubscriptionId;

        if (!isOffline && existingSubscription.stripeSubscriptionId) {
          await stripeSubscription.cancel(
            existingSubscription.stripeSubscriptionId
          );
        }

        // Update the subscription status in our database
        await userSubscriptionRepository.updateSubscription(
          existingSubscription.id,
          {
            status: SubscriptionStatus.CANCELED,
            canceledAt: new Date(),
          }
        );
      }

      const isFreePlan = Number(plan.price) === 0;

      // For free plans, create subscription directly
      if (isFreePlan) {
        const subscription =
          await userSubscriptionRepository.createSubscription(user, body, null);

        // Update user type based on plan
        await subscriptionService.updateUserType(user, plan);

        return subscription;
      } else {
        const paymentMethod =
          await userSubscriptionRepository.getPaymentMethodById(
            paymentMethodId,
            user.id
          );

        // Create Stripe subscription
        const trialDays =
          typeof plan.trialDays === 'number' && plan.trialDays > 0
            ? plan.trialDays
            : undefined;

        const stripeSubscriptionData = await stripeSubscription.create({
          customerId: user.stripeCustomerId,
          priceId: plan.stripePriceId,
          paymentMethodId: paymentMethod.stripePaymentMethodId,
          trialDays,
        });

        // Create subscription in our database
        const subscription =
          await userSubscriptionRepository.createSubscription(
            user,
            body,
            stripeSubscriptionData
          );

        // Update user type based on plan
        await subscriptionService.updateUserType(user, plan);

        // Add the hosted URL to the response
        return {
          ...subscription.toJSON(),
          hostedInvoiceUrl:
            stripeSubscriptionData.latest_invoice?.hosted_invoice_url,
          invoicePdf:
            stripeSubscriptionData.latest_invoice?.invoice_pdf || null,
        };
      }
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all subscriptions for a user
   * @param {string} userId - User ID
   * @param {Object} pagination - Pagination parameters
   * @param {number} pagination.page - Page number
   * @param {number} pagination.limit - Items per page
   * @returns {Promise<Object>} Subscriptions with pagination info
   */
  getUserSubscriptions: async (userId, pagination) => {
    try {
      const { page, limit } = pagination;
      return await userSubscriptionRepository.getSubscriptionByUserId(
        userId,
        page,
        limit
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get active subscription for a user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Active subscription
   */
  getActiveSubscription: async (userId) => {
    try {
      return await userSubscriptionRepository.getActiveSubscription(userId);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update payment method for a subscription
   * @param {string} subscriptionId - Subscription ID
   * @param {string} paymentMethodId - Payment method ID
   * @returns {Promise<Object>} Updated subscription
   */
  updatePaymentMethod: async (subscriptionId, paymentMethodId) => {
    try {
      const subscription =
        await userSubscriptionRepository.getSubscriptionById(subscriptionId);

      const paymentMethod =
        await userSubscriptionRepository.getPaymentMethodById(
          paymentMethodId,
          subscription.userId
        );

      // Check if this is an offline subscription (no Stripe ID)
      const isOffline = !subscription.stripeSubscriptionId;

      if (!isOffline && subscription.stripeSubscriptionId) {
        // Update payment method in Stripe
        await stripeSubscription.updatePaymentMethod(
          subscription.stripeSubscriptionId,
          paymentMethod.stripePaymentMethodId
        );
      }

      // Update payment method in our database
      return await userSubscriptionRepository.updateSubscription(
        subscriptionId,
        {
          paymentMethodId,
        }
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Pause a subscription
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise<Object>} Paused subscription
   */
  pauseSubscription: async (subscriptionId) => {
    try {
      const subscription =
        await userSubscriptionRepository.getSubscriptionById(subscriptionId);

      // Check if subscription is already paused
      if (subscription.pausedAt) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          SUBSCRIPTION.ALREADY_PAUSED
        );
      }

      // Check if subscription is canceled
      if (subscription.status === SubscriptionStatus.CANCELED) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          SUBSCRIPTION.CANNOT_PAUSE_CANCELED
        );
      }

      // Check if this is an offline subscription (no Stripe ID)
      const isOffline = !subscription.stripeSubscriptionId;

      if (isOffline) {
        // For offline subscriptions, just update the database
        return await userSubscriptionRepository.updateSubscription(
          subscriptionId,
          {
            status: SubscriptionStatus.PAUSED,
            pausedAt: new Date(),
            pauseBehavior: PauseBehavior.KEEP_AS_DRAFT,
          }
        );
      } else {
        // For online subscriptions, pause in Stripe
        await stripeSubscription.pause(subscription.stripeSubscriptionId);
        return subscription;
      }
    } catch (error) {
      throw error;
    }
  },

  /**
   * Resume a paused subscription
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise<Object>} Resumed subscription
   */
  resumeSubscription: async (subscriptionId) => {
    try {
      const subscription =
        await userSubscriptionRepository.getSubscriptionById(subscriptionId);

      // Check if subscription is not paused
      if (!subscription.pausedAt) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          SUBSCRIPTION.ALREADY_ACTIVE
        );
      }

      // Check if subscription is canceled
      if (subscription.status === SubscriptionStatus.CANCELED) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          SUBSCRIPTION.CANNOT_RESUME_CANCELED
        );
      }

      // Check if this is an offline subscription (no Stripe ID)
      const isOffline = !subscription.stripeSubscriptionId;

      if (isOffline) {
        // For offline subscriptions, just update the database
        return await userSubscriptionRepository.updateSubscription(
          subscriptionId,
          {
            status: SubscriptionStatus.ACTIVE,
            pausedAt: null,
            pauseBehavior: null,
            resumedAt: new Date(),
          }
        );
      } else {
        // For online subscriptions, resume in Stripe
        await stripeSubscription.resume(subscription.stripeSubscriptionId);
        return subscription;
      }
    } catch (error) {
      throw error;
    }
  },

  /**
   * Cancel a subscription
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise<Object>} Canceled subscription
   */
  cancelSubscription: async (subscriptionId) => {
    try {
      const subscription =
        await userSubscriptionRepository.getSubscriptionById(subscriptionId);

      // Check if subscription is already canceled
      if (subscription.cancelAtPeriodEnd === true) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          SUBSCRIPTION.CANCELLATION_ALREADY_INITIATED
        );
      }

      // Check if this is an offline subscription (no Stripe ID)
      const isOffline = !subscription.stripeSubscriptionId;

      if (isOffline) {
        // For offline subscriptions, just update the database
        return await userSubscriptionRepository.updateSubscription(
          subscriptionId,
          {
            status: SubscriptionStatus.CANCELED,
            canceledAt: new Date(),
          }
        );
      } else {
        // For online subscriptions, cancel in Stripe first
        await stripeSubscription.cancel(subscription.stripeSubscriptionId);
        return await userSubscriptionRepository.updateSubscription(
          subscriptionId,
          {
            status: SubscriptionStatus.CANCELED,
            canceledAt: new Date(),
          }
        );
      }
    } catch (error) {
      throw error;
    }
  },

  // Helper: Downgrade paid → free
  async handleDowngradeToFreePlan({ user, subscription, planId, newPlan }) {
    // Create new free subscription first
    const newSubscription = await userSubscriptionRepository.createSubscription(
      user,
      { planId },
      null
    );

    // Only after successful creation, cancel the old paid subscription
    // Check if this is an offline subscription (no Stripe ID)
    const isOffline = !subscription.stripeSubscriptionId;

    if (!isOffline && subscription.stripeSubscriptionId) {
      await stripeSubscription.cancelImmediately(
        subscription.stripeSubscriptionId
      );
    }

    await userSubscriptionRepository.updateSubscription(subscription.id, {
      status: SubscriptionStatus.CANCELED,
      canceledAt: new Date(),
    });

    // Update user type based on new plan
    await subscriptionService.updateUserType(user, newPlan);

    return {
      subscription: newSubscription.toJSON
        ? newSubscription.toJSON()
        : newSubscription,
      hostedInvoiceUrl: null,
    };
  },

  // Helper: Upgrade free → paid
  async handleUpgradeToPaidPlan({
    user,
    subscription,
    planId,
    paymentMethod,
    newPlan,
    paymentMethodId,
  }) {
    const paymentMethodObj =
      paymentMethod ||
      (await userSubscriptionRepository.getPaymentMethodById(
        paymentMethodId,
        user.id
      ));
    const trialDays =
      typeof newPlan.trialDays === 'number' && newPlan.trialDays > 0
        ? newPlan.trialDays
        : undefined;

    // Create new Stripe subscription first
    const stripeSubscriptionData = await stripeSubscription.create({
      customerId: user.stripeCustomerId,
      priceId: newPlan.stripePriceId,
      paymentMethodId: paymentMethodObj.stripePaymentMethodId,
      trialDays,
    });

    // Create new subscription in our database
    const newSubscription = await userSubscriptionRepository.createSubscription(
      user,
      {
        planId,
        paymentMethodId: paymentMethodObj.id,
        previousSubscriptionId: subscription.id,
      },
      stripeSubscriptionData
    );

    // If the new subscription is already active, cancel the old one now
    if (newSubscription.status === SubscriptionStatus.ACTIVE) {
      await userSubscriptionRepository.updateSubscription(subscription.id, {
        status: SubscriptionStatus.CANCELED,
        canceledAt: new Date(),
      });
    }

    // Update user type based on new plan
    await subscriptionService.updateUserType(user, newPlan);

    return {
      subscription: newSubscription.toJSON
        ? newSubscription.toJSON()
        : newSubscription,
      hostedInvoiceUrl:
        stripeSubscriptionData.latest_invoice?.hosted_invoice_url || null,
    };
  },

  // Helper: Paid → Paid (Stripe update)
  async handlePaidToPaidPlan({
    subscription,
    paymentMethod,
    newPlan,
    planId,
    subscriptionId,
    user,
  }) {
    // Check if this is an offline subscription (no Stripe ID)
    const isOffline = !subscription.stripeSubscriptionId;

    if (isOffline) {
      // For offline subscriptions, just update the database
      const updated = await userSubscriptionRepository.updateSubscription(
        subscriptionId,
        {
          subscriptionPlanId: planId,
          paymentMethodId: paymentMethod
            ? paymentMethod.id
            : subscription.paymentMethodId,
        }
      );

      // Update user type based on new plan
      await subscriptionService.updateUserType(user, newPlan);

      return {
        subscription: updated.toJSON ? updated.toJSON() : updated,
        hostedInvoiceUrl: null,
      };
    }

    const stripeDetails = subscription.stripeSubscriptionDetails;
    if (
      !stripeDetails ||
      !stripeDetails.items ||
      !Array.isArray(stripeDetails.items.data) ||
      !stripeDetails.items.data[0]
    ) {
      throw new ApiException(
        HttpStatus.BAD_REQUEST,
        SUBSCRIPTION.INVALID_STRIPE_UPGRADE
      );
    }

    const stripeUpdateParams = {
      items: [
        {
          id: stripeDetails.items.data[0].id,
          price: newPlan.stripePriceId,
        },
      ],
    };
    if (paymentMethod && paymentMethod.stripePaymentMethodId) {
      stripeUpdateParams.default_payment_method =
        paymentMethod.stripePaymentMethodId;
    }
    const updatedStripeSub = await stripeSubscription.update(
      subscription.stripeSubscriptionId,
      stripeUpdateParams
    );
    const updated = await userSubscriptionRepository.updateSubscription(
      subscriptionId,
      {
        subscriptionPlanId: planId,
        paymentMethodId: paymentMethod
          ? paymentMethod.id
          : subscription.paymentMethodId,
        stripeSubscriptionDetails: updatedStripeSub,
      }
    );

    // Update user type based on new plan
    await subscriptionService.updateUserType(user, newPlan);

    return {
      subscription: updated.toJSON ? updated.toJSON() : updated,
      hostedInvoiceUrl:
        updatedStripeSub.latest_invoice?.hosted_invoice_url || null,
    };
  },

  // Helper: Free → Free
  async handleFreeToFreePlan({ user, subscription, planId, newPlan }) {
    // Create new free subscription first
    const newSubscription = await userSubscriptionRepository.createSubscription(
      user,
      { planId },
      null
    );

    // Only after successful creation, cancel the old free subscription
    await userSubscriptionRepository.updateSubscription(subscription.id, {
      status: SubscriptionStatus.CANCELED,
      canceledAt: new Date(),
    });

    // Update user type based on new plan
    await subscriptionService.updateUserType(user, newPlan);

    return {
      subscription: newSubscription.toJSON
        ? newSubscription.toJSON()
        : newSubscription,
      hostedInvoiceUrl: null,
    };
  },

  /**
   * Upgrade a user's subscription plan
   *
   * @param {Object} routeParams - Route parameters containing subscriptionId
   * @param {Object} requestBody - Request body containing planId and (optionally) paymentMethodId
   * @param {Object} currentUser - The current user object
   * @returns {Promise<Object>} The updated subscription object
   */
  upgradeSubscription: async (routeParams, requestBody, currentUser) => {
    const { subscriptionId } = routeParams;
    const { planId, paymentMethodId } = requestBody;
    const user = currentUser;
    try {
      const { subscription, paymentMethod, newPlan } =
        await userSubscriptionRepository.getSubscriptionWithStripeDetails(
          user,
          subscriptionId,
          planId,
          paymentMethodId
        );

      const isCurrentFree = !subscription.stripeSubscriptionId;
      const isNewFree = Number(newPlan.price) === 0;
      const transition = `${isCurrentFree ? 'free' : 'paid'}->${isNewFree ? 'free' : 'paid'}`;

      switch (transition) {
        case 'paid->free':
          return await subscriptionService.handleDowngradeToFreePlan({
            user,
            subscription,
            planId,
            newPlan,
          });
        case 'free->paid':
          return await subscriptionService.handleUpgradeToPaidPlan({
            user,
            subscription,
            planId,
            paymentMethod,
            newPlan,
            paymentMethodId,
          });
        case 'paid->paid':
          return await subscriptionService.handlePaidToPaidPlan({
            subscription,
            paymentMethod,
            newPlan,
            planId,
            subscriptionId,
            user,
          });
        case 'free->free':
          return await subscriptionService.handleFreeToFreePlan({
            user,
            subscription,
            planId,
            newPlan,
          });
        default:
          throw new ApiException(
            HttpStatus.BAD_REQUEST,
            'Invalid subscription transition'
          );
      }
    } catch (error) {
      throw error;
    }
  },

  // Webhook Operations
  webhookHandler: new WebhookHandler(),

  /**
   * Handle webhook event
   * @param {Object} event - Stripe webhook event
   * @returns {Promise<void>}
   */
  handleWebhookEvent: async (event) => {
    try {
      switch (event.type) {
        case 'customer.subscription.updated':
          await subscriptionService.webhookHandler.handleSubscriptionUpdated(
            event.data.object
          );
          break;
        case 'customer.subscription.deleted':
          await subscriptionService.webhookHandler.handleSubscriptionDeleted(
            event.data.object
          );
          break;
        case 'invoice.payment_succeeded':
          await subscriptionService.webhookHandler.handlePaymentSucceeded(
            event.data.object
          );
          break;
        case 'invoice.payment_failed':
          await subscriptionService.webhookHandler.handlePaymentFailed(
            event.data.object
          );
          break;
        default:
          // Ignore other events
          break;
      }
    } catch (error) {
      throw error;
    }
  },
};

module.exports = subscriptionService;
