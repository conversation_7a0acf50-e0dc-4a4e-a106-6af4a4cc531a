/**
 * EducatorQuestionFocus Model
 * Represents the many-to-many relationship between EducatorQuestion and Focus
 */
const { Model, DataTypes } = require('sequelize');

class EducatorQuestionFocus extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        educatorQuestionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'EducatorQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        focusId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Focus',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'EducatorQuestionFocus',
        tableName: 'EducatorQuestionFocus',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['educatorQuestionId', 'focusId'],
            name: 'educator_question_focus_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
  }
}

module.exports = EducatorQuestionFocus;
