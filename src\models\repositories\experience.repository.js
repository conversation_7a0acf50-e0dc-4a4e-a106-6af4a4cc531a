/**
 * Experience Repository
 * Handles database operations for Experience model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');
const commonRepository = require('./common.repository');
const {
  HttpStatus,
  ExperienceEnrollmentStatus,
  UserType,
  InsightStatus,
  ExperienceStatus,
  ExperienceWeekProgressStatus,
} = require('@utils/enums.utils');
const { EXPERIENCE } = require('@utils/messages.utils');
const { Op } = require('sequelize');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      Experience: databaseService.getExperienceModel(),
      ExperienceMedia: databaseService.getExperienceMediaModel(),
      ExperiencePdCategory: databaseService.getExperiencePdCategoryModel(),
      ExperienceWtdCategory: databaseService.getExperienceWtdCategoryModel(),
      ExperienceWeek: databaseService.getExperienceWeekModel(),
      ExperienceWeekMedia: databaseService.getExperienceWeekMediaModel(),
      ExperienceWeekInsight: databaseService.getExperienceWeekInsightModel(),
      ExperienceEnrollment: databaseService.getExperienceEnrollmentModel(),
      ExperienceWeekProgress: databaseService.getExperienceWeekProgressModel(),
      ExperienceDiscussion: databaseService.getExperienceDiscussionModel(),
      User: databaseService.getUserModel(),
      PdCategory: databaseService.getPdCategoryModel(),
      WtdCategory: databaseService.getWtdCategoryModel(),
      Focus: databaseService.getFocusModel(),
      Insight: databaseService.getInsightModel(),
      InsightFocus: databaseService.getInsightFocusModel(),
      InsightWtdCategory: databaseService.getInsightWtdCategoryModel(),
      Contribution: databaseService.getContributionModel(),
    };
  }

  _getExperienceAttributes() {
    return {
      exclude: [
        'createdBy',
        'status',
        'reviewedBy',
        'reviewedAt',
        'rejectionReason',
      ],
      include: [
        [
          databaseService.getSequelize().literal(`(
            SELECT COALESCE(
              (
                SELECT CAST(AVG("providerRating") AS numeric(10,1))
                FROM "ExperienceReview" er
                INNER JOIN "Experience" e ON e.id = er."experienceId"
                WHERE e."createdBy" = "Experience"."createdBy"
                AND er."providerRating" IS NOT NULL
              ),
              0
            )
          )`),
          'providerRating',
        ],
        [
          databaseService.getSequelize().literal(
            `EXISTS (
              SELECT 1 FROM "ExperienceEnrollment" 
              WHERE "ExperienceEnrollment"."experienceId" = "Experience"."id"
              AND "ExperienceEnrollment"."userId" = :userId
            )`
          ),
          'isEnrolled',
        ],
      ],
    };
  }

  _getExperienceAttributesForAdmin() {
    return {
      exclude: ['createdBy', 'reviewedBy', 'reviewedAt', 'rejectionReason'],
      include: [
        [
          databaseService.getSequelize().literal(`(
            SELECT COALESCE(
              (
                SELECT CAST(AVG("providerRating") AS numeric(10,1))
                FROM "ExperienceReview" er
                INNER JOIN "Experience" e ON e.id = er."experienceId"
                WHERE e."createdBy" = "Experience"."createdBy"
                AND er."providerRating" IS NOT NULL
              ),
              0
            )
          )`),
          'providerRating',
        ],
      ],
    };
  }

  _getExperienceIncludes() {
    return [
      {
        model: this.models.User,
        as: 'creator',
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'profilePic',
          'userType',
        ],
      },
      {
        model: this.models.ExperienceMedia,
        as: 'media',
        required: false,
        attributes: ['id', 'type', 'url', 'title', 'order'],
      },
      {
        model: this.models.PdCategory,
        as: 'pdCategories',
        through: { attributes: [] },
        required: false,
        attributes: ['id', 'name'],
      },
      {
        model: this.models.WtdCategory,
        as: 'wtdCategories',
        through: { attributes: [] },
        required: false,
        attributes: ['id', 'name'],
      },
    ];
  }

  _getExperienceIncludesForAdmin() {
    return [
      {
        model: this.models.User,
        as: 'creator',
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'profilePic',
          'userType',
        ],
      },
      {
        model: this.models.ExperienceMedia,
        as: 'media',
        required: false,
        attributes: ['id', 'type', 'url', 'title', 'order'],
      },
      {
        model: this.models.PdCategory,
        as: 'pdCategories',
        through: { attributes: [] },
        required: false,
        attributes: ['id', 'name'],
      },
      {
        model: this.models.WtdCategory,
        as: 'wtdCategories',
        through: { attributes: [] },
        required: false,
        attributes: ['id', 'name'],
      },
    ];
  }

  _getExperienceByIdAttributes() {
    return {
      exclude: [
        'createdBy',
        'status',
        'reviewedBy',
        'reviewedAt',
        'rejectionReason',
      ],
      include: [
        [
          databaseService.getSequelize().literal(`(
            SELECT COALESCE(
              (
                SELECT CAST(AVG("providerRating") AS numeric(10,1))
                FROM "ExperienceReview"
                WHERE "experienceId" = "Experience"."id"
              ),
              0
            )
          )`),
          'providerRating',
        ],
        [
          databaseService.getSequelize().literal(
            `EXISTS (
              SELECT 1 FROM "ExperienceEnrollment" 
              WHERE "ExperienceEnrollment"."experienceId" = "Experience"."id"
              AND "ExperienceEnrollment"."userId" = :userId
            )`
          ),
          'isEnrolled',
        ],
        [
          databaseService.getSequelize().literal(`(
            SELECT "startDate" FROM "ExperienceEnrollment"
            WHERE "ExperienceEnrollment"."experienceId" = "Experience"."id"
            AND "ExperienceEnrollment"."userId" = :userId
            LIMIT 1
          )`),
          'startDate',
        ],
        [
          databaseService.getSequelize().literal(`(
            SELECT json_build_object(
              'growth', json_build_object(
                'insight', (
                  SELECT COUNT(*) 
                  FROM "ExperienceWeekInsight" ewi
                  INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                  WHERE ew."experienceId" = "Experience"."id"
                ),
                'developmentCategories', (
                  SELECT COUNT(*) FROM (
                    SELECT DISTINCT "pdCategoryId" FROM "ExperiencePdCategory" WHERE "experienceId" = "Experience"."id"
                    UNION
                    SELECT DISTINCT "wtdCategoryId" FROM "ExperienceWtdCategory" WHERE "experienceId" = "Experience"."id"
                  ) AS categories
                )
              ),
              'connection', json_build_object(
                'enrolledEducators', (
                  SELECT COUNT(DISTINCT ee."userId") 
                  FROM "ExperienceEnrollment" ee
                  INNER JOIN "User" u ON u.id = ee."userId"
                  WHERE ee."experienceId" = "Experience"."id"
                  AND u."userType" IN ('${UserType.EDUCATOR}', '${UserType.EDUCATOR_PLUS}')
                ),
                'statesRepresented', 0,
                'schoolRepresented', 0
              ),
              'inspiration', json_build_object(
                'educatorsCompleted', (
                  SELECT COUNT(DISTINCT ee."userId") 
                  FROM "ExperienceEnrollment" ee
                  INNER JOIN "User" u ON u.id = ee."userId"
                  WHERE ee."experienceId" = "Experience"."id" 
                  AND ee."status" = '${ExperienceEnrollmentStatus.COMPLETED}'
                  AND u."userType" IN ('${UserType.EDUCATOR}', '${UserType.EDUCATOR_PLUS}')
                )
              )
            )
          )`),
          'takeaways',
        ],
        [
          databaseService.getSequelize().literal(`(
            SELECT json_build_object(
              'completed', (
                SELECT COUNT(DISTINCT ee."userId") 
                FROM "ExperienceEnrollment" ee
                WHERE ee."experienceId" = "Experience"."id" 
                AND ee."status" = '${ExperienceEnrollmentStatus.COMPLETED}'
              ),
              'enrolled', (
                SELECT COUNT(DISTINCT ee."userId") 
                FROM "ExperienceEnrollment" ee
                WHERE ee."experienceId" = "Experience"."id"
              ),
              'peerInteractions', (
                SELECT COALESCE(SUM(peer_interactions), 0)
                FROM (
                  SELECT COUNT(DISTINCT c.id) as peer_interactions
                  FROM "Contribution" c
                  INNER JOIN "Insight" i ON i.id = c."insightId"
                  INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
                  INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                  WHERE ew."experienceId" = "Experience"."id"
                  GROUP BY c."contributedBy"
                  UNION ALL
                  SELECT COUNT(DISTINCT li.id) as peer_interactions
                  FROM "LikedInsight" li
                  INNER JOIN "Insight" i ON i.id = li."insightId"
                  INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
                  INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                  WHERE ew."experienceId" = "Experience"."id"
                  GROUP BY li."userId"
                ) as all_interactions
              )
            )
          )`),
          'engagementStats',
        ],
      ],
    };
  }

  _getExperienceByIdAttributesForAdmin() {
    return {
      exclude: ['createdBy', 'reviewedBy', 'reviewedAt', 'rejectionReason'],
      include: [
        [
          databaseService.getSequelize().literal(`(
            SELECT COALESCE(
              (
                SELECT CAST(AVG("providerRating") AS numeric(10,1))
                FROM "ExperienceReview"
                WHERE "experienceId" = "Experience"."id"
              ),
              0
            )
          )`),
          'providerRating',
        ],
        [
          databaseService.getSequelize().literal(`(
            SELECT json_build_object(
              'growth', json_build_object(
                'insight', (
                  SELECT COUNT(*) 
                  FROM "ExperienceWeekInsight" ewi
                  INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                  WHERE ew."experienceId" = "Experience"."id"
                ),
                'developmentCategories', (
                  SELECT COUNT(*) FROM (
                    SELECT DISTINCT "pdCategoryId" FROM "ExperiencePdCategory" WHERE "experienceId" = "Experience"."id"
                    UNION
                    SELECT DISTINCT "wtdCategoryId" FROM "ExperienceWtdCategory" WHERE "experienceId" = "Experience"."id"
                  ) AS categories
                )
              ),
              'connection', json_build_object(
                'enrolledEducators', (
                  SELECT COUNT(DISTINCT ee."userId") 
                  FROM "ExperienceEnrollment" ee
                  INNER JOIN "User" u ON u.id = ee."userId"
                  WHERE ee."experienceId" = "Experience"."id"
                  AND u."userType" IN ('${UserType.EDUCATOR}', '${UserType.EDUCATOR_PLUS}')
                ),
                'statesRepresented', 0,
                'schoolRepresented', 0
              ),
              'inspiration', json_build_object(
                'educatorsCompleted', (
                  SELECT COUNT(DISTINCT ee."userId") 
                  FROM "ExperienceEnrollment" ee
                  INNER JOIN "User" u ON u.id = ee."userId"
                  WHERE ee."experienceId" = "Experience"."id" 
                  AND ee."status" = '${ExperienceEnrollmentStatus.COMPLETED}'
                  AND u."userType" IN ('${UserType.EDUCATOR}', '${UserType.EDUCATOR_PLUS}')
                )
              )
            )
          )`),
          'takeaways',
        ],
        [
          databaseService.getSequelize().literal(`(
            SELECT json_build_object(
              'completed', (
                SELECT COUNT(DISTINCT ee."userId") 
                FROM "ExperienceEnrollment" ee
                WHERE ee."experienceId" = "Experience"."id" 
                AND ee."status" = '${ExperienceEnrollmentStatus.COMPLETED}'
              ),
              'enrolled', (
                SELECT COUNT(DISTINCT ee."userId") 
                FROM "ExperienceEnrollment" ee
                WHERE ee."experienceId" = "Experience"."id"
              ),
              'peerInteractions', (
                SELECT COALESCE(SUM(peer_interactions), 0)
                FROM (
                  SELECT COUNT(DISTINCT c.id) as peer_interactions
                  FROM "Contribution" c
                  INNER JOIN "Insight" i ON i.id = c."insightId"
                  INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
                  INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                  WHERE ew."experienceId" = "Experience"."id"
                  GROUP BY c."contributedBy"
                  UNION ALL
                  SELECT COUNT(DISTINCT li.id) as peer_interactions
                  FROM "LikedInsight" li
                  INNER JOIN "Insight" i ON i.id = li."insightId"
                  INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
                  INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                  WHERE ew."experienceId" = "Experience"."id"
                  GROUP BY li."userId"
                ) as all_interactions
              )
            )
          )`),
          'engagementStats',
        ],
      ],
    };
  }

  /**
   * Get week attributes with status and isCurrentWeek fields
   * @param {string} userId - User ID for enrollment data (optional)
   * @returns {Array} Array of attributes for ExperienceWeek model
   */
  _getWeekAttributesWithProgress(userId = null) {
    const weekAttributes = ['id', 'weekNumber', 'title', 'weeklyWhy'];

    // Always add status and isCurrentWeek fields
    if (userId) {
      // If userId provided, get actual values from database with proper defaults
      weekAttributes.push([
        databaseService.getSequelize().literal(`
          COALESCE(
            (
              SELECT COALESCE(ewp.status, '${ExperienceWeekProgressStatus.PENDING}')
              FROM "ExperienceEnrollment" ee
              LEFT JOIN "ExperienceWeekProgress" ewp ON ewp."enrollmentId" = ee.id 
                AND ewp."experienceWeekId" = "weeks"."id"
              WHERE ee."experienceId" = "Experience"."id" 
                AND ee."userId" = '${userId}'
              LIMIT 1
            ),
            '${ExperienceWeekProgressStatus.PENDING}'
          )
        `),
        'status',
      ]);

      weekAttributes.push([
        databaseService.getSequelize().literal(`
          COALESCE(
            (
              SELECT CASE 
                WHEN ee."currentWeek" = "weeks"."weekNumber" THEN true 
                ELSE false 
              END
              FROM "ExperienceEnrollment" ee
              WHERE ee."experienceId" = "Experience"."id" 
                AND ee."userId" = '${userId}'
              LIMIT 1
            ),
            false
          )
        `),
        'isCurrentWeek',
      ]);
    } else {
      // If no userId, set default values
      weekAttributes.push([
        databaseService
          .getSequelize()
          .literal(`'${ExperienceWeekProgressStatus.PENDING}'`),
        'status',
      ]);

      weekAttributes.push([
        databaseService.getSequelize().literal('false'),
        'isCurrentWeek',
      ]);
    }

    return weekAttributes;
  }
}

/**
 * Experience Helper class for common operations
 */
class ExperienceHelper {
  constructor(models) {
    this.models = models;
  }

  async createMedia(experienceId, media, transaction) {
    if (!media.length) return;

    const mediaData = media.map((item, index) => ({
      id: uuidv4(),
      experienceId,
      type: item.type,
      url: item.url,
      title: item.title,
      order: index + 1,
    }));

    await this.models.ExperienceMedia.bulkCreate(mediaData, { transaction });
  }

  async createCategories(
    experienceId,
    pdCategoryIds,
    wtdCategoryIds,
    transaction
  ) {
    if (pdCategoryIds.length) {
      // Validate PD Category IDs
      const validPdCategories = await this.models.PdCategory.findAll({
        where: { id: pdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validPdIds = validPdCategories.map((cat) => cat.id);
      const invalidPdIds = pdCategoryIds.filter(
        (id) => !validPdIds.includes(id)
      );

      if (invalidPdIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_PD_CATEGORY_IDS.replace(
            '%s',
            invalidPdIds.join(', ')
          )
        );
      }

      const pdCategoryData = validPdIds.map((id) => ({
        id: uuidv4(),
        experienceId,
        pdCategoryId: id,
      }));
      await this.models.ExperiencePdCategory.bulkCreate(pdCategoryData, {
        transaction,
      });
    }

    if (wtdCategoryIds.length) {
      // Validate WTD Category IDs
      const validWtdCategories = await this.models.WtdCategory.findAll({
        where: { id: wtdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validWtdIds = validWtdCategories.map((cat) => cat.id);
      const invalidWtdIds = wtdCategoryIds.filter(
        (id) => !validWtdIds.includes(id)
      );

      if (invalidWtdIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_WTD_CATEGORY_IDS.replace(
            '%s',
            invalidWtdIds.join(', ')
          )
        );
      }

      const wtdCategoryData = validWtdIds.map((id) => ({
        id: uuidv4(),
        experienceId,
        wtdCategoryId: id,
      }));
      await this.models.ExperienceWtdCategory.bulkCreate(wtdCategoryData, {
        transaction,
      });
    }
  }

  async createWeeks(
    experienceId,
    weeks,
    transaction,
    userId,
    insightStatus = InsightStatus.PENDING
  ) {
    for (const week of weeks) {
      const experienceWeek = await this.models.ExperienceWeek.create(
        {
          id: uuidv4(),
          experienceId,
          weekNumber: week.weekNumber,
          title: week.title,
          weeklyWhy: week.weeklyWhy,
        },
        { transaction }
      );

      await this.createWeekMedia(experienceWeek.id, week.media, transaction);
      await this.createInsights(
        experienceWeek.id,
        week.insights,
        transaction,
        userId,
        insightStatus // pass status
      );
    }
  }

  async createWeekMedia(weekId, media, transaction) {
    if (!media?.length) return;

    const weekMediaData = media.map((item, index) => ({
      id: uuidv4(),
      experienceWeekId: weekId,
      type: item.type,
      url: item.url,
      title: item.title,
      order: index + 1,
    }));

    await this.models.ExperienceWeekMedia.bulkCreate(weekMediaData, {
      transaction,
    });
  }

  async createInsights(
    weekId,
    insights,
    transaction,
    userId,
    status = InsightStatus.PENDING
  ) {
    if (!insights?.length) return;

    for (let i = 0; i < insights.length; i++) {
      const data = insights[i];

      // Create main insight first
      const mainInsight = await this.models.Insight.create(
        {
          id: uuidv4(),
          insightText: data.text,
          sourceUrl: data.sourceUrl,
          pdCategoryId: data.pdCategoryIds?.[0],
          createdBy: userId,
          status, // use passed status
        },
        { transaction }
      );

      // Create insight associations
      await this.createInsightAssociations(mainInsight.id, data, transaction);

      // Link insight to experience week
      await this.models.ExperienceWeekInsight.create(
        {
          id: uuidv4(),
          experienceWeekId: weekId,
          insightId: mainInsight.id,
          order: i + 1,
        },
        { transaction }
      );
    }
  }

  async createInsightAssociations(insightId, data, transaction) {
    if (data.focusIds?.length) {
      // Validate Focus IDs
      const validFocuses = await this.models.Focus.findAll({
        where: { id: data.focusIds },
        attributes: ['id'],
        transaction,
      });

      const validFocusIds = validFocuses.map((focus) => focus.id);
      const invalidFocusIds = data.focusIds.filter(
        (id) => !validFocusIds.includes(id)
      );

      if (invalidFocusIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_FOCUS_IDS.replace('%s', invalidFocusIds.join(', '))
        );
      }

      const focusData = validFocusIds.map((id) => ({
        id: uuidv4(),
        insightId: insightId,
        focusId: id,
      }));
      await this.models.InsightFocus.bulkCreate(focusData, {
        transaction,
      });
    }

    if (data.wtdCategoryIds?.length) {
      // Validate WTD Category IDs
      const validWtdCategories = await this.models.WtdCategory.findAll({
        where: { id: data.wtdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validWtdIds = validWtdCategories.map((cat) => cat.id);
      const invalidWtdIds = data.wtdCategoryIds.filter(
        (id) => !validWtdIds.includes(id)
      );

      if (invalidWtdIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_WTD_CATEGORY_IDS_INSIGHT.replace(
            '%s',
            invalidWtdIds.join(', ')
          )
        );
      }

      const wtdData = validWtdIds.map((id) => ({
        id: uuidv4(),
        insightId: insightId,
        wtdCategoryId: id,
      }));
      await this.models.InsightWtdCategory.bulkCreate(wtdData, {
        transaction,
      });
    }
  }
}

/**
 * Main Experience Repository class
 */
class ExperienceRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new ExperienceHelper(this.models);
  }

  /**
   * Find experience by ID (simple lookup)
   * @param {string} id - Experience ID
   * @returns {Promise<Object|null>} Experience instance or null if not found
   */
  async findById(id) {
    try {
      const experience = await this.models.Experience.findByPk(id);
      return experience;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find creator ID of an experience
   * @param {string} id - Experience ID
   * @returns {Promise<string|null>} Creator ID or null if experience not found
   */
  async findCreatorId(id) {
    const experience = await this.models.Experience.findByPk(id, {
      attributes: ['createdBy'],
    });
    return experience?.createdBy || null;
  }

  /**
   * Create an experience
   * @param {Object} data - Experience data to create
   * @returns {Promise<Object>} Created experience
   */
  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      const experience = await this.models.Experience.create(
        {
          title: data.title,
          shortDescription: data.shortDescription,
          longDescription: data.longDescription,
          experienceLength: data.experienceLength,
          personalNote: data.personalNote,
          createdBy: data.createdBy,
          status: ExperienceStatus.PENDING,
        },
        { transaction }
      );

      await this.helper.createMedia(
        experience.id,
        data.media || [],
        transaction
      );
      await this.helper.createCategories(
        experience.id,
        data.pdCategoryIds || [],
        data.wtdCategoryIds || [],
        transaction
      );
      await this.helper.createWeeks(
        experience.id,
        data.weeks || [],
        transaction,
        data.createdBy,
        InsightStatus.PENDING // status for create
      );

      await transaction.commit();
      return await this.getExperienceDetails(
        experience.id,
        true,
        data.createdBy
      );
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update an experience
   * @param {string} id - Experience ID
   * @param {Object} data - Experience data to update
   * @returns {Promise<Object>} Updated experience
   */
  async update(id, data) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Check if experience exists
      const experience = await this.findById(id);
      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      // Update basic experience data
      await experience.update(data, { transaction });

      // Update media if provided
      if (data.media) {
        // Delete existing media
        await this.models.ExperienceMedia.destroy({
          where: { experienceId: id },
          transaction,
        });
        // Create new media
        await this.helper.createMedia(id, data.media, transaction);
      }

      // Update categories if provided
      if (data.pdCategoryIds || data.wtdCategoryIds) {
        // Delete existing categories
        await this.models.ExperiencePdCategory.destroy({
          where: { experienceId: id },
          transaction,
        });
        await this.models.ExperienceWtdCategory.destroy({
          where: { experienceId: id },
          transaction,
        });
        // Create new categories
        await this.helper.createCategories(
          id,
          data.pdCategoryIds || [],
          data.wtdCategoryIds || [],
          transaction
        );
      }

      // Update weeks if provided
      if (data.weeks) {
        // Delete existing weeks and their associated data
        const existingWeeks = await this.models.ExperienceWeek.findAll({
          where: { experienceId: id },
          attributes: ['id'],
        });
        const weekIds = existingWeeks.map((week) => week.id);

        // Get all insight IDs associated with these weeks
        const weekInsights = await this.models.ExperienceWeekInsight.findAll({
          where: { experienceWeekId: weekIds },
          attributes: ['insightId'],
        });
        const insightIds = weekInsights.map((wi) => wi.insightId);

        // Delete insight associations first
        await this.models.InsightFocus.destroy({
          where: { insightId: insightIds },
          transaction,
        });
        await this.models.InsightWtdCategory.destroy({
          where: { insightId: insightIds },
          transaction,
        });

        // Delete the insights themselves
        await this.models.Insight.destroy({
          where: { id: insightIds },
          transaction,
        });

        // Delete week media and week-insight associations
        await this.models.ExperienceWeekMedia.destroy({
          where: { experienceWeekId: weekIds },
          transaction,
        });
        await this.models.ExperienceWeekInsight.destroy({
          where: { experienceWeekId: weekIds },
          transaction,
        });

        // Finally delete the weeks
        await this.models.ExperienceWeek.destroy({
          where: { experienceId: id },
          transaction,
        });

        // Create new weeks
        await this.helper.createWeeks(
          id,
          data.weeks,
          transaction,
          experience.createdBy,
          InsightStatus.APPROVED // status for update
        );
      }

      await transaction.commit();
      return await this.getExperienceDetails(id, true, experience.createdBy);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async findAll({ page, limit, createdBy = '', userId } = {}) {
    try {
      const whereClause = {
        status: ExperienceStatus.APPROVED,
      };

      if (createdBy) whereClause.createdBy = createdBy;

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.Experience.findAndCountAll({
        where: whereClause,
        attributes: this._getExperienceAttributes(),
        include: this._getExperienceIncludes(),
        limit,
        offset,
        order: [['createdAt', 'DESC']],
        distinct: true,
        replacements: { userId },
      });

      return {
        experiences: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  async findAllForAdmin({ page, limit, createdBy = '' } = {}) {
    try {
      const whereClause = {};
      if (createdBy) whereClause.createdBy = createdBy;

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.Experience.findAndCountAll({
        where: whereClause,
        attributes: this._getExperienceAttributesForAdmin(),
        include: this._getExperienceIncludesForAdmin(),
        order: [['createdAt', 'DESC']],
        limit,
        offset,
        distinct: true,
      });

      return {
        experiences: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get complete experience details with all associations, statistics, and user-specific data
   * @param {string} id - Experience ID
   * @param {boolean} includeWeeks - Whether to include weeks data (default: true)
   * @param {string} userId - User ID for user-specific data (enrollment status, etc.)
   * @returns {Promise<Object>} Complete experience details with all relationships and statistics
   * @throws {ApiException} If experience not found or not approved
   */
  async getExperienceDetails(id, includeWeeks = true, userId) {
    try {
      const includeOptions = [...this._getExperienceIncludes()];

      if (includeWeeks) {
        includeOptions.push({
          model: this.models.ExperienceWeek,
          as: 'weeks',
          required: false,
          attributes: this._getWeekAttributesWithProgress(userId),
          include: [
            {
              model: this.models.ExperienceWeekMedia,
              as: 'media',
              required: false,
              attributes: [
                'id',
                'type',
                'url',
                'title',
                'order',
                [
                  databaseService.getSequelize().literal(`COALESCE(
                    (SELECT "watchDuration" FROM "ExperienceWeekMediaWatch" 
                    WHERE "experienceWeekMediaId" = "weeks->media"."id" 
                    AND "userId" = :userId 
                    LIMIT 1),
                    0
                  )`),
                  'watchDuration',
                ],
                [
                  databaseService.getSequelize().literal(`COALESCE(
                    (SELECT "isCompleted" FROM "ExperienceWeekMediaWatch" 
                    WHERE "experienceWeekMediaId" = "weeks->media"."id" 
                    AND "userId" = :userId 
                    LIMIT 1),
                    false
                  )`),
                  'isCompleted',
                ],
              ],
            },
          ],
        });
      }

      const experience = await this.models.Experience.findByPk(id, {
        attributes: this._getExperienceByIdAttributes(),
        include: includeOptions,
        where: {
          status: ExperienceStatus.APPROVED,
        },
        replacements: { userId },
      });

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      // // Add startDate if user is enrolled
      // if (userId) {
      //   const ExperienceEnrollment = this.models.ExperienceEnrollment;
      //   const enrollment = await ExperienceEnrollment.findOne({
      //     where: { experienceId: id, userId },
      //     attributes: ['startDate'],
      //   });
      //   if (enrollment && enrollment.startDate) {
      //     experience.setDataValue('startDate', enrollment.startDate);
      //   }
      // }

      return experience;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get complete experience details for admin with all associations and statistics
   * @param {string} id - Experience ID
   * @param {boolean} includeWeeks - Whether to include weeks data (default: true)
   * @returns {Promise<Object>} Complete experience details with all relationships and statistics
   * @throws {ApiException} If experience not found
   */
  async getExperienceDetailsForAdmin(id, includeWeeks = true) {
    try {
      const includeOptions = [...this._getExperienceIncludes()];

      if (includeWeeks) {
        includeOptions.push({
          model: this.models.ExperienceWeek,
          as: 'weeks',
          required: false,
          attributes: ['id', 'weekNumber', 'title', 'weeklyWhy'],
          include: [
            {
              model: this.models.ExperienceWeekMedia,
              as: 'media',
              required: false,
              attributes: ['id', 'type', 'url', 'title', 'order'],
            },
          ],
        });
      }

      const experience = await this.models.Experience.findByPk(id, {
        attributes: this._getExperienceByIdAttributesForAdmin(),
        include: includeOptions,
      });

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return experience;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find a week by its ID
   * @param {string} weekId - Week ID
   * @returns {Promise<Object>} Week details
   */
  async findWeekById(weekId) {
    try {
      const week = await this.models.ExperienceWeek.findOne({
        where: { id: weekId },
        attributes: ['id', 'weekNumber', 'title', 'weeklyWhy', 'experienceId'],
        include: [
          {
            model: this.models.ExperienceWeekMedia,
            as: 'media',
            required: false,
            attributes: ['id', 'type', 'url', 'title', 'order'],
          },
        ],
      });

      return week;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get engagement statistics for user experience participation (cumulative by week)
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {number} currentWeek - Current week number
   * @returns {Promise<Object>} Engagement statistics including cumulative data
   */
  async getWeekEngagementStats(experienceId, userId, currentWeek) {
    try {
      const sequelize = databaseService.getSequelize();

      const statsResult = await sequelize.query(
        `
        WITH total_weeks AS (
          SELECT "experienceLength" as total_weeks
          FROM "Experience"
          WHERE id = :experienceId
        ),
        week_stats AS (
          SELECT COUNT(DISTINCT c.id)::INTEGER as peer_interactions
          FROM "Contribution" c
          INNER JOIN "Insight" i ON i.id = c."insightId"
          INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
          INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
          WHERE ew."experienceId" = :experienceId
          AND ew."weekNumber" <= :currentWeek
          AND c."contributedBy" = :userId
        ),
        liked_insights AS (
          SELECT COUNT(DISTINCT li.id)::INTEGER as insight_liked
          FROM "LikedInsight" li
          INNER JOIN "Insight" i ON i.id = li."insightId"
          INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
          INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
          WHERE ew."experienceId" = :experienceId
          AND ew."weekNumber" <= :currentWeek
          AND li."userId" = :userId
        ),
        insight_stats AS (
          SELECT COUNT(DISTINCT c.id)::INTEGER as insight_contributions
          FROM "Contribution" c
          INNER JOIN "Insight" i ON i.id = c."insightId"
          INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
          INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
          WHERE ew."experienceId" = :experienceId
          AND ew."weekNumber" <= :currentWeek
          AND c."contributedBy" = :userId
        ),
        completion_stats AS (
          SELECT 
            ROUND(
              (COUNT(CASE WHEN ewp.status = :progressStatus THEN 1 END)::FLOAT / 
              (SELECT COUNT(*) FROM "ExperienceWeek" WHERE "experienceId" = :experienceId)::FLOAT * 100)::NUMERIC(10,2)
            ) as completion_rate
          FROM "ExperienceWeekProgress" ewp
          INNER JOIN "ExperienceEnrollment" ee ON ee.id = ewp."enrollmentId"
          INNER JOIN "ExperienceWeek" ew ON ew.id = ewp."experienceWeekId"
          WHERE ee."experienceId" = :experienceId
          AND ee."userId" = :userId
          AND ew."weekNumber" <= :currentWeek
        ),
        discussion_stats AS (
          SELECT COUNT(DISTINCT ed.id)::INTEGER as discussion_contributions
          FROM "ExperienceDiscussion" ed
          INNER JOIN "ExperienceEnrollment" ee ON ee."experienceId" = ed."experienceId"
          WHERE ed."experienceId" = :experienceId
          AND ed."createdBy" = :userId
          AND ee."userId" = :userId
          AND EXISTS (
            SELECT 1
            FROM "ExperienceWeekProgress" ewp
            INNER JOIN "ExperienceWeek" ew ON ew.id = ewp."experienceWeekId"
            WHERE ew."experienceId" = :experienceId
            AND ew."weekNumber" <= :currentWeek
          )
        ),
        video_stats AS (
          SELECT COUNT(DISTINCT ewmw.id)::INTEGER as videos_watched
          FROM "ExperienceWeekMediaWatch" ewmw
          INNER JOIN "ExperienceWeekMedia" ewm ON ewm.id = ewmw."experienceWeekMediaId"
          INNER JOIN "ExperienceWeek" ew ON ew.id = ewm."experienceWeekId"
          WHERE ew."experienceId" = :experienceId
          AND ewmw."userId" = :userId
          AND ewmw."isCompleted" = true
          AND ew."weekNumber" <= :currentWeek
        )
        SELECT 
          (COALESCE(ins.insight_contributions, 0) + COALESCE(lis.insight_liked, 0)) as peer_interactions,
          COALESCE(ins.insight_contributions, 0) as insight_contributions,
          COALESCE(cs.completion_rate, 0) as completion_rate,
          COALESCE(vs.videos_watched, 0) as videos_watched,
          COALESCE(ds.discussion_contributions, 0) as discussion_contributions
        FROM insight_stats ins
        CROSS JOIN liked_insights lis
        CROSS JOIN completion_stats cs
        CROSS JOIN video_stats vs
        CROSS JOIN discussion_stats ds
        `,
        {
          replacements: {
            experienceId,
            userId,
            currentWeek,
            insightStatus: InsightStatus.APPROVED,
            progressStatus: ExperienceWeekProgressStatus.COMPLETED,
          },
          type: sequelize.QueryTypes.SELECT,
        }
      );

      const stats = statsResult?.[0] || {};

      return {
        peerInteractions: parseInt(stats.peer_interactions) || 0,
        insightContributions: parseInt(stats.insight_contributions) || 0,
        completionRate: parseFloat(stats.completion_rate) || 0,
        videosWatched: parseInt(stats.videos_watched) || 0,
        discussionPostContributions:
          parseInt(stats.discussion_contributions) || 0,
      };
    } catch (error) {
      throw error;
    }
  }

  async findDiscussionsByExperienceIdForAdmin(experienceId, page, limit) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } =
        await this.models.ExperienceDiscussion.findAndCountAll({
          where: { experienceId },
          include: [
            {
              model: this.models.User,
              as: 'user',
              attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'profilePic',
                'userType',
              ],
            },
          ],
          order: [['createdAt', 'DESC']],
          limit,
          offset,
          distinct: true,
        });

      return {
        discussions: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  async findReviewsByExperienceIdForAdmin(experienceId, page, limit) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } =
        await this.models.ExperienceReview.findAndCountAll({
          where: { experienceId },
          include: [
            {
              model: this.models.User,
              as: 'user',
              attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'profilePic',
                'userType',
              ],
            },
          ],
          order: [['createdAt', 'DESC']],
          limit,
          offset,
          distinct: true,
        });

      return {
        reviews: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update experience status and all linked insights
   * @param {string} id - Experience ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated experience
   */
  async updateExperienceStatusWithInsights(id, status) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Get experience with weeks and insights
      const experience = await this.models.Experience.findByPk(id, {
        include: [
          {
            model: this.models.User,
            as: 'creator',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profilePic',
              'userType',
              'deviceToken',
            ],
          },
          {
            model: this.models.ExperienceWeek,
            as: 'weeks',
            include: [
              {
                model: this.models.ExperienceWeekInsight,
                as: 'insights',
                include: [
                  {
                    model: this.models.Insight,
                    as: 'insight',
                  },
                ],
              },
            ],
          },
        ],
        transaction,
      });

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      // Update experience status
      await experience.update({ status }, { transaction });

      // Update all linked insights
      for (const week of experience.weeks) {
        for (const weekInsight of week.insights) {
          await weekInsight.insight.update({ status }, { transaction });
        }
      }

      await transaction.commit();
      return experience;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Delete an experience and all its related data
   * @param {string} id - Experience ID
   * @returns {Promise<void>}
   */
  async deleteExperience(id) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Check if experience exists
      const experience = await this.models.Experience.findByPk(id, {
        transaction,
      });
      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      // Get all week IDs for this experience
      const weeks = await this.models.ExperienceWeek.findAll({
        where: { experienceId: id },
        attributes: ['id'],
        transaction,
      });
      const weekIds = weeks.map((week) => week.id);

      if (weekIds.length > 0) {
        // Get all insight IDs associated with these weeks
        const weekInsights = await this.models.ExperienceWeekInsight.findAll({
          where: {
            experienceWeekId: {
              [Op.in]: weekIds,
            },
          },
          attributes: ['insightId'],
          transaction,
        });
        const insightIds = weekInsights.map((wi) => wi.insightId);

        // Delete the insights themselves
        await this.models.Insight.destroy({
          where: { id: insightIds },
          transaction,
        });
      }

      await experience.destroy({ transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get provider-level engagement stats for an experience (same as experience details)
   * @param {string} experienceId
   * @returns {Promise<Object>} Provider engagement stats (takeaways, engagementStats)
   */
  async getProviderEngagementStats(experienceId) {
    try {
      const sequelize = databaseService.getSequelize();
      const statsResult = await sequelize.query(
        `SELECT 
          (SELECT json_build_object(
            'completed', (
              SELECT COUNT(DISTINCT ee."userId") 
              FROM "ExperienceEnrollment" ee
              WHERE ee."experienceId" = :experienceId 
              AND ee."status" = 'COMPLETED'
            ),
            'enrolled', (
              SELECT COUNT(DISTINCT ee."userId") 
              FROM "ExperienceEnrollment" ee
              WHERE ee."experienceId" = :experienceId
            ),
            'peerInteractions', (
              SELECT COALESCE(SUM(peer_interactions), 0)
              FROM (
                SELECT COUNT(DISTINCT c.id) as peer_interactions
                FROM "Contribution" c
                INNER JOIN "Insight" i ON i.id = c."insightId"
                INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
                INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                WHERE ew."experienceId" = :experienceId
                GROUP BY c."contributedBy"
                UNION ALL
                SELECT COUNT(DISTINCT li.id) as peer_interactions
                FROM "LikedInsight" li
                INNER JOIN "Insight" i ON i.id = li."insightId"
                INNER JOIN "ExperienceWeekInsight" ewi ON ewi."insightId" = i.id
                INNER JOIN "ExperienceWeek" ew ON ew.id = ewi."experienceWeekId"
                WHERE ew."experienceId" = :experienceId
                GROUP BY li."userId"
              ) as all_interactions
            )
          )) AS engagementStats
        FROM (SELECT 1) as dummy
        `,
        {
          replacements: { experienceId },
          type: sequelize.QueryTypes.SELECT,
        }
      );
      const stats = statsResult?.[0] || {};
      return stats;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ExperienceRepository();
