/**
 * Bookmark Service
 *
 * Handles business logic for bookmarking insights
 */
const bookmarkedInsightRepository = require('@models/repositories/bookmarked-insight.repository');
const insightRepository = require('@models/repositories/insight.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { AchievementType, UserType } = require('@utils/enums.utils');

/**
 * Bookmark Service
 */
const bookmarkService = {
  /**
   * Toggle bookmark status for an insight
   * @param {Object} user - User object containing user information
   * @param {string} user.id - User ID
   * @param {string} user.userType - Type of user (provider, provider_plus, etc.)
   * @param {string} insightId - Insight ID
   * @returns {Promise<Object>} Object with isBookmarked status
   */
  toggleBookmark: async (user, insightId) => {
    const userId = user.id;
    try {
      // Get insight data to check the creator
      const insight = await insightRepository.findById(insightId);

      const result = await bookmarkedInsightRepository.toggleBookmark(
        userId,
        insightId
      );

      const isBookmarked = result.isBookmarked;
      const progressChange = isBookmarked ? 1 : -1;

      switch (user.userType) {
        case UserType.PROVIDER:
        case UserType.PROVIDER_PLUS:
          if (insight.userId !== userId) {
            await achievementService.updateAchievementProgress(
              userId,
              AchievementType.INSIGHT_SAVED,
              progressChange
            );
          }
          break;
        case UserType.EDUCATOR:
        case UserType.EDUCATOR_PLUS:
          await achievementService.updateAchievementProgress(
            userId,
            AchievementType.INSIGHT_SAVED,
            progressChange
          );
          break;
        default:
          break;
      }

      return result;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all bookmarked insights for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Insights and pagination info
   */
  getBookmarkedInsights: async (userId, { page, limit }) => {
    try {
      return await bookmarkedInsightRepository.getBookmarkedInsights(userId, {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },
};

module.exports = bookmarkService;
