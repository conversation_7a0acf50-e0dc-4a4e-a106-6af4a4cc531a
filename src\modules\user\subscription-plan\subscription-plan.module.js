/**
 * Admin Subscription Plan Module
 *
 * This module handles subscription plan-related functionality for admins
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const paginationMiddleware = require('@middlewares/pagination.middleware');
const subscriptionPlanController = require('./subscription-plan.controller');
const subscriptionPlanValidation = require('./subscription-plan.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all subscription plans
  router.get(
    '/',
    paginationMiddleware,
    validate(subscriptionPlanValidation.getAll),
    subscriptionPlanController.getAllSubscriptionPlans
  );

  // Get subscription plan by ID
  router.get(
    '/:id',
    validate(subscriptionPlanValidation.getById),
    subscriptionPlanController.getSubscriptionPlanById
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
