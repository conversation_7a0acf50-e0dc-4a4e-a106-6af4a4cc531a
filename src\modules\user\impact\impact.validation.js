/**
 * Impact Validation
 *
 * Validation schemas for impact-related endpoints
 */
const { param, body } = require('express-validator');
const { VALIDATION } = require('@utils/messages.utils');

/**
 * Impact validation schemas
 */
const impactValidation = {
  /**
   * Get milestone questions validation schema
   */
  getMilestoneQuestions: [
    param('milestoneId').isUUID().withMessage(VALIDATION.INVALID_FORMAT),
  ],

  /**
   * Get achievement questions validation schema
   */
  getAchievementQuestions: [
    param('achievementId').isUUID().withMessage(VALIDATION.INVALID_FORMAT),
  ],

  /**
   * Move to next milestone validation schema
   */
  moveToNextMilestone: [
    param('milestoneId').isUUID().withMessage(VALIDATION.INVALID_FORMAT),
  ],

  /**
   * Submit milestone questions validation schema
   */
  submitMilestoneQuestions: [
    param('milestoneId').isUUID().withMessage(VALIDATION.INVALID_FORMAT),
    body('answers')
      .isArray()
      .withMessage('Answers must be an array')
      .notEmpty()
      .withMessage('At least one answer is required'),
    body('answers.*.questionId')
      .isUUID()
      .withMessage(VALIDATION.INVALID_FORMAT),
    body('answers.*.answer')
      .notEmpty()
      .withMessage('Answer cannot be empty')
      .isString()
      .withMessage('Answer must be a string')
      .trim()
      .notEmpty()
      .withMessage('Answer cannot be empty'),
  ],

  /**
   * Submit achievement questions validation schema
   */
  submitAchievementQuestions: [
    param('achievementId').isUUID().withMessage(VALIDATION.INVALID_FORMAT),
    body('answers')
      .isArray()
      .withMessage('Answers must be an array')
      .notEmpty()
      .withMessage('At least one answer is required'),
    body('answers.*.questionId')
      .isUUID()
      .withMessage(VALIDATION.INVALID_FORMAT),
    body('answers.*.response')
      .notEmpty()
      .withMessage('Answer cannot be empty')
      .isString()
      .withMessage('Answer must be a string')
      .trim()
      .notEmpty()
      .withMessage('Answer cannot be empty'),
    body('answers.*.selectedOptionId')
      .optional()
      .isUUID()
      .withMessage(VALIDATION.INVALID_FORMAT),
  ],
};

module.exports = impactValidation;
