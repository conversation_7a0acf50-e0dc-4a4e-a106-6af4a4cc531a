// Debug script to check controller exports
console.log('Debugging controller exports...');

// Set up path aliases like the main app
const moduleAlias = require('module-alias');
moduleAlias.addAliases({
  '@': __dirname + '/src',
  '@config': __dirname + '/src/config',
  '@models': __dirname + '/src/models',
  '@utils': __dirname + '/src/utils',
  '@middlewares': __dirname + '/src/middlewares',
  '@modules': __dirname + '/src/modules',
  '@user': __dirname + '/src/modules/user',
  '@admin': __dirname + '/src/modules/admin',
  '@repositories': __dirname + '/src/models/repositories'
});

try {
  console.log('1. Checking if controller file exists...');
  const fs = require('fs');
  const controllerPath = './src/modules/user/educator-question/educator-question.controller.js';
  if (fs.existsSync(controllerPath)) {
    console.log('✅ Controller file exists');
  } else {
    console.log('❌ Controller file does not exist');
    process.exit(1);
  }

  console.log('2. Trying to require the controller...');
  const controller = require(controllerPath);
  
  console.log('3. Controller type:', typeof controller);
  console.log('4. Controller keys:', Object.keys(controller));
  
  console.log('5. Checking specific methods:');
  console.log('   - getAllEducatorQuestions:', typeof controller.getAllEducatorQuestions);
  console.log('   - getEducatorQuestionById:', typeof controller.getEducatorQuestionById);
  console.log('   - getBookmarkedQuestions:', typeof controller.getBookmarkedQuestions);
  console.log('   - toggleLike:', typeof controller.toggleLike);
  console.log('   - toggleBookmark:', typeof controller.toggleBookmark);
  console.log('   - toggleImplement:', typeof controller.toggleImplement);

  if (controller.getBookmarkedQuestions) {
    console.log('✅ getBookmarkedQuestions method exists and is:', typeof controller.getBookmarkedQuestions);
  } else {
    console.log('❌ getBookmarkedQuestions method is missing!');
  }

} catch (error) {
  console.error('❌ Error requiring controller:', error.message);
  console.error('Stack trace:', error.stack);
}
