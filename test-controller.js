// Simple test to check controller methods
console.log('Testing controller...');

try {
  // Mock the required modules first
  const mockRepository = {
    findAll: () => Promise.resolve({ questions: [] }),
    findById: () => Promise.resolve({}),
    create: () => Promise.resolve({}),
    toggleLike: () => Promise.resolve({ isLiked: true }),
    toggleBookmark: () => Promise.resolve({ isBookmarked: true }),
    toggleImplement: () => Promise.resolve({ isImplemented: true }),
    getBookmarkedEducatorQuestions: () => Promise.resolve({ questions: [], pagination: {} })
  };

  const mockService = {
    getAllEducatorQuestions: () => Promise.resolve({ questions: [] }),
    getEducatorQuestionById: () => Promise.resolve({}),
    createEducatorQuestion: () => Promise.resolve({})
  };

  const mockBookmarkService = {
    toggleBookmark: () => Promise.resolve({ isBookmarked: true }),
    getBookmarkedEducatorQuestions: () => Promise.resolve({ questions: [], pagination: {} })
  };

  const mockLikeService = {
    toggleLike: () => Promise.resolve({ isLiked: true })
  };

  const mockImplementService = {
    toggleImplement: () => Promise.resolve({ isImplemented: true })
  };

  // Mock the modules
  require.cache[require.resolve('./src/modules/user/educator-question/services/educator-question.service.js')] = {
    exports: mockService
  };

  require.cache[require.resolve('./src/modules/user/educator-question/services/bookmark.service.js')] = {
    exports: mockBookmarkService
  };

  require.cache[require.resolve('./src/modules/user/educator-question/services/like.service.js')] = {
    exports: mockLikeService
  };

  require.cache[require.resolve('./src/modules/user/educator-question/services/implement.service.js')] = {
    exports: mockImplementService
  };

  require.cache[require.resolve('@models/repositories/educator-question.repository')] = {
    exports: mockRepository
  };

  require.cache[require.resolve('@utils/response.utils')] = {
    exports: { ApiResponse: { success: () => {}, created: () => {} } }
  };

  require.cache[require.resolve('@utils/messages.utils')] = {
    exports: { EDUCATOR_QUESTION: { CREATED: 'Created', ALL_RETRIEVED: 'Retrieved' } }
  };

  const controller = require('./src/modules/user/educator-question/educator-question.controller.js');
  
  console.log('Controller methods:', Object.keys(controller));
  console.log('getBookmarkedQuestions exists:', typeof controller.getBookmarkedQuestions);
  console.log('getBookmarkedQuestions is function:', typeof controller.getBookmarkedQuestions === 'function');
  
  if (controller.getBookmarkedQuestions) {
    console.log('✅ getBookmarkedQuestions method found!');
  } else {
    console.log('❌ getBookmarkedQuestions method NOT found!');
  }

} catch (error) {
  console.error('Error testing controller:', error.message);
}
