/**
 * OnboardingConfig Module
 *
 * Handles onboarding config-related routes and middleware
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('@middlewares/auth.middleware');
const onboardingConfigController = require('./onboarding-config.controller');

/**
 * Register routes
 */
function registerRoutes() {
  // Get onboarding config for the authenticated user
  router.get('/', authenticate, onboardingConfigController.getConfig);

  // Update currentStep for the authenticated user
  router.patch(
    '/current-step',
    authenticate,
    onboardingConfigController.updateCurrentStep
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
