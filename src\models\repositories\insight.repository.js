/**
 * Insight Repository
 *
 * Handles data access operations for the Insight model
 */
const { ApiException } = require('@utils/exception.utils');
const { InsightStatus } = require('@utils/enums.utils');
const { PAGINATION, PD_SPOTLIGHT, TRENDING } = require('@utils/constants');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');
const { Sequelize } = require('sequelize');
const { Op } = Sequelize;
const commonRepository = require('./common.repository');
const { INSIGHT } = require('@utils/messages.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      Insight: databaseService.getInsightModel(),
      User: databaseService.getUserModel(),
      PdCategory: databaseService.getPdCategoryModel(),
      Focus: databaseService.getFocusModel(),
      WtdCategory: databaseService.getWtdCategoryModel(),
      Admin: databaseService.getAdminModel(),
      BookmarkedInsight: databaseService.getBookmarkedInsightModel(),
      LikedInsight: databaseService.getLikedInsightModel(),
      ImplementedInsight: databaseService.getImplementedInsightModel(),
      InsightFocus: databaseService.getInsightFocusModel(),
      InsightWtdCategory: databaseService.getInsightWtdCategoryModel(),
      ExperienceWeekInsight: databaseService.getExperienceWeekInsightModel(),
      InsightReport: databaseService.getInsightReportModel(),
    };
  }

  _getInsightAttributes() {
    return {
      exclude: [
        'pdCategoryId',
        'status',
        'reviewedBy',
        'reviewedAt',
        'rejectionReason',
      ],
      include: [
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
            ),
          'likesCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
            ),
          'implementationCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id" AND "Contribution"."isRemoved" = false)`
            ),
          'totalContributions',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "BookmarkedInsight" WHERE "BookmarkedInsight"."insightId" = "Insight"."id")`
            ),
          'bookmarksCount',
        ],
      ],
    };
  }

  _getInsightIncludes() {
    return [
      {
        model: this.models.User,
        as: 'creator',
        attributes: [
          'id',
          'firstName',
          'lastName',
          'profilePic',
          'userType',
          'deviceToken',
        ],
      },
      {
        model: this.models.PdCategory,
        as: 'pdCategory',
        attributes: ['id', 'name'],
      },
      {
        model: this.models.Focus,
        as: 'focus',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        model: this.models.WtdCategory,
        as: 'wtdCategories',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
    ];
  }

  _getAdminInsightIncludes() {
    return [
      ...this._getInsightIncludes(),
      {
        model: this.models.Admin,
        as: 'reviewer',
        attributes: ['id', 'email'],
      },
    ];
  }
}

/**
 * Repository for complex search and filtering operations
 */
class InsightSearchRepository extends BaseRepository {
  async buildWhereClause({
    search,
    focusIds,
    pdCategoryIds,
    wtdCategoryIds,
    creatorId,
  }) {
    const whereClause = {
      status: InsightStatus.APPROVED,
      isRemoved: false,
    };

    if (creatorId) {
      whereClause.createdBy = creatorId;
    }

    if (search) {
      whereClause.insightText = {
        [Op.iLike]: `%${search}%`,
      };
    }

    if (pdCategoryIds?.length > 0) {
      const insightsWithPdCategory = await this.models.Insight.findAll({
        attributes: ['id'],
        where: {
          pdCategoryId: {
            [Op.in]: pdCategoryIds,
          },
          status: InsightStatus.APPROVED,
        },
        raw: true,
      });

      const insightIds = insightsWithPdCategory.map((item) => item.id);
      if (insightIds.length > 0) {
        whereClause.id = {
          [Op.in]: insightIds,
        };
      } else {
        whereClause.id = null;
      }
    }

    if (focusIds?.length > 0) {
      const insightIdsWithFocus = await this.models.InsightFocus.findAll({
        attributes: ['insightId'],
        where: {
          focusId: {
            [Op.in]: focusIds,
          },
        },
        raw: true,
      });

      const insightIds = insightIdsWithFocus.map((item) => item.insightId);
      if (insightIds.length > 0) {
        if (whereClause.id) {
          const existingIds = whereClause.id[Op.in];
          const intersectionIds = existingIds.filter((id) =>
            insightIds.includes(id)
          );
          whereClause.id = {
            [Op.in]: intersectionIds.length > 0 ? intersectionIds : [null],
          };
        } else {
          whereClause.id = {
            [Op.in]: insightIds,
          };
        }
      } else {
        whereClause.id = null;
      }
    }

    if (wtdCategoryIds?.length > 0) {
      const insightIdsWithWtdCategory =
        await this.models.InsightWtdCategory.findAll({
          attributes: ['insightId'],
          where: {
            wtdCategoryId: {
              [Op.in]: wtdCategoryIds,
            },
          },
          raw: true,
        });

      const insightIds = insightIdsWithWtdCategory.map(
        (item) => item.insightId
      );
      if (insightIds.length > 0) {
        if (whereClause.id) {
          const existingIds = whereClause.id[Op.in];
          const intersectionIds = existingIds.filter((id) =>
            insightIds.includes(id)
          );
          whereClause.id = {
            [Op.in]: intersectionIds.length > 0 ? intersectionIds : [null],
          };
        } else {
          whereClause.id = {
            [Op.in]: insightIds,
          };
        }
      } else if (wtdCategoryIds.length > 0) {
        whereClause.id = null;
      }
    }

    return whereClause;
  }
}

/**
 * Helper class for Insight operations
 */
class InsightHelper extends BaseRepository {
  async enrichInsightsWithUserData(insights, userId) {
    if (!userId) return insights;

    const insightIds = insights.map((insight) => insight.id);
    const [bookmarks, likes, implementations, reports] = await Promise.all([
      this.models.BookmarkedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.LikedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.ImplementedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.InsightReport.findAll({
        where: {
          reportedBy: userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
    ]);

    const bookmarkedInsightIds = new Set(bookmarks.map((b) => b.insightId));
    const likedInsightIds = new Set(likes.map((l) => l.insightId));
    const implementedInsightIds = new Set(
      implementations.map((i) => i.insightId)
    );
    const reportedInsightIds = new Set(reports.map((r) => r.insightId));

    return insights.map((insight) => {
      const insightData = insight.toJSON();
      insightData.isBookmarked = bookmarkedInsightIds.has(insight.id);
      insightData.isLiked = likedInsightIds.has(insight.id);
      insightData.isImplemented = implementedInsightIds.has(insight.id);
      insightData.isReported = reportedInsightIds.has(insight.id);
      return insightData;
    });
  }

  async enrichInsightWithUserData(insight, userId) {
    const [bookmark, like, implementation, report] = await Promise.all([
      this.models.BookmarkedInsight.findOne({
        where: {
          userId,
          insightId: insight.id,
        },
      }),
      this.models.LikedInsight.findOne({
        where: {
          userId,
          insightId: insight.id,
        },
      }),
      this.models.ImplementedInsight.findOne({
        where: {
          userId,
          insightId: insight.id,
        },
      }),
      this.models.InsightReport.findOne({
        where: {
          reportedBy: userId,
          insightId: insight.id,
        },
      }),
    ]);

    const insightData = insight.toJSON();
    insightData.isBookmarked = !!bookmark;
    insightData.isLiked = !!like;
    insightData.isImplemented = !!implementation;
    insightData.isReported = !!report;
    return insightData;
  }

  async createFocusAssociations(insightId, focusIds, transaction) {
    const focusEntries = focusIds.map((focusId) => ({
      id: uuidv4(),
      insightId,
      focusId,
    }));
    await this.models.InsightFocus.bulkCreate(focusEntries, { transaction });
  }

  async createWtdCategoryAssociations(insightId, wtdCategoryIds, transaction) {
    const wtdCategoryEntries = wtdCategoryIds.map((wtdCategoryId) => ({
      id: uuidv4(),
      insightId,
      wtdCategoryId,
    }));
    await this.models.InsightWtdCategory.bulkCreate(wtdCategoryEntries, {
      transaction,
    });
  }

  async updateFocusAssociations(insightId, focusIds, transaction) {
    await this.models.InsightFocus.destroy({
      where: { insightId },
      transaction,
    });

    if (focusIds.length > 0) {
      await this.createFocusAssociations(insightId, focusIds, transaction);
    }
  }

  async updateWtdCategoryAssociations(insightId, wtdCategoryIds, transaction) {
    await this.models.InsightWtdCategory.destroy({
      where: { insightId },
      transaction,
    });

    if (wtdCategoryIds.length > 0) {
      await this.createWtdCategoryAssociations(
        insightId,
        wtdCategoryIds,
        transaction
      );
    }
  }

  async findPdCategoryByName(pdCategoryName) {
    const PdCategory = databaseService.getPdCategoryModel();
    return await PdCategory.findOne({
      where: { name: pdCategoryName },
      raw: true,
    });
  }

  getPDSpotlightIncludes() {
    const User = databaseService.getUserModel();
    const PdCategory = databaseService.getPdCategoryModel();
    const Focus = databaseService.getFocusModel();
    const WtdCategory = databaseService.getWtdCategoryModel();

    return [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'profilePic'],
      },
      {
        model: PdCategory,
        as: 'pdCategory',
        attributes: ['id', 'name'],
      },
      {
        model: Focus,
        as: 'focus',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        model: WtdCategory,
        as: 'wtdCategories',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
    ];
  }

  getPDSpotlightAttributes() {
    return {
      exclude: [
        'pdCategoryId',
        'createdBy',
        'status',
        'reviewedBy',
        'reviewedAt',
        'rejectionReason',
      ],
      include: [
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
            ),
          'likesCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
            ),
          'implementationCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id" AND "Contribution"."isRemoved" = false)`
            ),
          'totalContributions',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "BookmarkedInsight" WHERE "BookmarkedInsight"."insightId" = "Insight"."id")`
            ),
          'bookmarksCount',
        ],
      ],
    };
  }

  async findPDSpotlightInsights(pdCategoryId) {
    const Insight = databaseService.getInsightModel();

    return await Insight.findAll({
      where: {
        status: InsightStatus.APPROVED,
        pdCategoryId: pdCategoryId,
        isRemoved: false,
      },
      distinct: true,
      order: [['createdAt', 'DESC']],
      attributes: this.getPDSpotlightAttributes(),
      include: this.getPDSpotlightIncludes(),
      subQuery: false,
    });
  }

  async enrichPDSpotlightInsights(insights, userId) {
    if (!userId) return insights;

    const insightIds = insights.map((insight) => insight.id);
    const [bookmarks, likes, implementations, reports] = await Promise.all([
      this.models.BookmarkedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.LikedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.ImplementedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.InsightReport.findAll({
        where: {
          reportedBy: userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
    ]);

    const bookmarkedInsightIds = new Set(bookmarks.map((b) => b.insightId));
    const likedInsightIds = new Set(likes.map((l) => l.insightId));
    const implementedInsightIds = new Set(
      implementations.map((i) => i.insightId)
    );
    const reportedInsightIds = new Set(reports.map((r) => r.insightId));

    return insights.map((insight) => {
      const insightData = insight.toJSON ? insight.toJSON() : insight;
      insightData.isBookmarked = bookmarkedInsightIds.has(insight.id);
      insightData.isLiked = likedInsightIds.has(insight.id);
      insightData.isImplemented = implementedInsightIds.has(insight.id);
      insightData.isReported = reportedInsightIds.has(insight.id);
      return insightData;
    });
  }
}

/**
 * Main Insight Repository class
 */
class InsightRepository extends BaseRepository {
  constructor() {
    super();
    this.searchRepo = new InsightSearchRepository();
    this.helper = new InsightHelper();
  }

  /**
   * Create a new insight
   * @param {Object} data - Insight data
   * @param {string} data.insightText - The insight text
   * @param {string} data.sourceUrl - Optional source URL
   * @param {string} data.pdCategoryId - PD category ID
   * @param {string} data.createdBy - User ID of creator
   * @param {Array<string>} data.focusIds - Array of focus IDs
   * @param {Array<string>} data.wtdCategoryIds - Array of WTD category IDs
   * @returns {Promise<Insight>} Created insight
   */
  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Temporarily auto-approving all new insights; subject to change based on moderation workflow
      const insight = await this.models.Insight.create(
        {
          insightText: data.insightText,
          sourceUrl: data.sourceUrl,
          pdCategoryId: data.pdCategoryId,
          createdBy: data.createdBy,
          status: InsightStatus.PENDING,
        },
        { transaction }
      );

      if (data.focusIds?.length > 0) {
        await this.helper.createFocusAssociations(
          insight.id,
          data.focusIds,
          transaction
        );
      }

      if (data.wtdCategoryIds?.length > 0) {
        await this.helper.createWtdCategoryAssociations(
          insight.id,
          data.wtdCategoryIds,
          transaction
        );
      }

      await transaction.commit();
      return await this.findById(insight.id, null, InsightStatus.PENDING);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Find all insights with pagination and optional search (for regular users)
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for insightText
   * @param {Array<string>} options.focusIds - Optional array of focus IDs to filter by
   * @param {Array<string>} options.pdCategoryIds - Optional array of PD category IDs to filter by
   * @param {Array<string>} options.wtdCategoryIds - Optional array of WTD category IDs to filter by
   * @param {string} options.userId - Optional user ID to check if insights are bookmarked
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findAll({
    search = '',
    focusIds = [],
    pdCategoryIds = [],
    wtdCategoryIds = [],
    userId = null,
    creatorId = null,
  } = {}) {
    try {
      const whereClause = await this.searchRepo.buildWhereClause({
        search,
        focusIds,
        pdCategoryIds,
        wtdCategoryIds,
        creatorId,
      });

      const rows = await this.models.Insight.findAll({
        where: whereClause,
        distinct: true,
        order: [['createdAt', 'DESC']],
        attributes: this._getInsightAttributes(),
        include: this._getInsightIncludes(),
        subQuery: false,
      });

      const insights = await this.helper.enrichInsightsWithUserData(
        rows,
        userId
      );

      return {
        insights,
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find insight by ID without filters or associations
   * @param {string} id - Insight UUID
   * @returns {Promise<Insight|null>} Insight instance or null
   */
  async findByIdRaw(id) {
    try {
      return await this.models.Insight.findOne({ where: { id } });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find insight by ID (for regular users)
   * @param {string} id - Insight UUID
   * @param {string} userId - Optional user ID to check if insight is bookmarked
   * @returns {Promise<Insight>} Insight instance
   * @throws {ApiException} If insight not found
   */
  async findById(id, userId = null, status = InsightStatus.APPROVED) {
    try {
      const insight = await this.models.Insight.findOne({
        where: {
          id,
          status: status,
          isRemoved: false,
        },
        attributes: this._getInsightAttributes(),
        include: this._getInsightIncludes(),
      });

      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      if (userId) {
        return await this.helper.enrichInsightWithUserData(insight, userId);
      }

      return insight;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find all insights with pagination and optional search (for admin users)
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for insightText
   * @param {string} options.status - Optional filter by status
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findAllForAdmin({
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    search = '',
    status = null,
  } = {}) {
    try {
      const whereClause = {};
      if (search) {
        whereClause.insightText = {
          [Op.iLike]: `%${search}%`,
        };
      }
      if (status) {
        whereClause.status = status;
      }

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.Insight.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
        attributes: {
          exclude: ['pdCategoryId', 'createdBy', 'reviewedBy', 'reviewedAt'],
        },
        include: this._getAdminInsightIncludes(),
        distinct: true,
      });

      return {
        insights: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in findAllForAdmin repository:', error);
      throw error;
    }
  }

  /**
   * Find insight by ID (for admin users)
   * @param {string} id - Insight UUID
   * @returns {Promise<Insight>} Insight instance with admin-specific data
   * @throws {ApiException} If insight not found
   */
  async findByIdForAdmin(id) {
    try {
      const insight = await this.models.Insight.findByPk(id, {
        attributes: {
          exclude: ['pdCategoryId', 'createdBy', 'reviewedBy', 'reviewedAt'],
        },
        include: this._getAdminInsightIncludes(),
      });

      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      return insight;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update insight
   * @param {string} id - Insight UUID
   * @param {Object} data - Data to update
   * @param {string} [data.insightText] - Updated insight text
   * @param {string} [data.sourceUrl] - Updated source URL
   * @param {string} [data.pdCategoryId] - Updated PD category ID
   * @param {Array<string>} [data.focusIds] - Updated array of focus IDs
   * @param {Array<string>} [data.wtdCategoryIds] - Updated array of WTD category IDs
   * @returns {Promise<Insight>} Updated insight
   * @throws {ApiException} If insight not found
   */
  async update(id, data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const insight = await this.findByIdRaw(id);
      Object.assign(insight, data);
      await insight.save({ transaction });

      if (data.focusIds) {
        await this.helper.updateFocusAssociations(
          insight.id,
          data.focusIds,
          transaction
        );
      }

      if (data.wtdCategoryIds) {
        await this.helper.updateWtdCategoryAssociations(
          insight.id,
          data.wtdCategoryIds,
          transaction
        );
      }

      await transaction.commit();
      return await this.findByIdForAdmin(insight.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Delete insight
   * @param {string} id - Insight UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If insight not found
   */
  async delete(id) {
    try {
      const insight = await this.findByIdRaw(id);
      await insight.destroy();
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find trending insights based on contributions and likes
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.userId - User ID to check if insights are bookmarked/liked/implemented
   * @returns {Promise<Object>} Object containing trending insights and pagination info
   */
  async findTrending({ userId } = {}) {
    try {
      const whereClause = {
        status: InsightStatus.APPROVED,
        isRemoved: false,
      };

      const rows = await this.models.Insight.findAll({
        where: whereClause,
        distinct: true,
        order: [
          [
            databaseService.getSequelize().literal(`
              (
                (SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id") * ${TRENDING.WEIGHTS.LIKES} +
                (SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id") * ${TRENDING.WEIGHTS.CONTRIBUTIONS}
              )
            `),
            'DESC',
          ],
          ['createdAt', 'DESC'],
        ],
        attributes: this._getInsightAttributes(),
        include: this._getInsightIncludes(),
        subQuery: false,
      });

      const insights = await this.helper.enrichInsightsWithUserData(
        rows,
        userId
      );

      return {
        insights,
      };
    } catch (error) {
      console.error('Error in findTrending repository:', error);
      throw error;
    }
  }

  /**
   * Find insights by PD category name (specifically for PD Spotlight)
   * @param {Object} options - Query options
   * @param {string} options.pdCategoryName - PD category name to filter by
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.userId - User ID to check if insights are bookmarked/liked/implemented
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findPDSpotlight({
    pdCategoryName = PD_SPOTLIGHT.DEFAULT_CATEGORY,
    userId,
  } = {}) {
    try {
      // Find the PD category
      const pdCategory = await this.helper.findPdCategoryByName(pdCategoryName);

      if (!pdCategory) {
        return {
          insights: [],
        };
      }

      // Find insights
      const rows = await this.helper.findPDSpotlightInsights(pdCategory.id);

      // Enrich insights with user data if userId is provided
      const insights = userId
        ? await this.helper.enrichPDSpotlightInsights(rows, userId)
        : rows;

      return {
        insights,
      };
    } catch (error) {
      console.error('Error in findPDSpotlight repository:', error);
      throw error;
    }
  }

  /**
   * Get insights by experience week ID with pagination
   * @param {string} experienceWeekId - The ID of the experience week
   * @param {Object} pagination - Pagination parameters
   * @param {number} pagination.page - Page number
   * @param {number} pagination.limit - Items per page
   * @param {string} [userId] - Optional user ID to check if insights are bookmarked/liked/implemented
   * @param {string} [status] - Optional status to filter insights (admin use)
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async getInsightsByExperienceWeekId(
    experienceWeekId,
    pagination,
    userId = null,
    status = InsightStatus.APPROVED
  ) {
    try {
      const { page, limit } = pagination;
      const offset = commonRepository.calculateOffset(page, limit);

      // Step 1: Get all insightIds for the week
      const weekInsights = await this.models.ExperienceWeekInsight.findAll({
        where: { experienceWeekId },
        attributes: ['insightId'],
        raw: true,
      });
      const insightIds = weekInsights.map((wi) => wi.insightId);

      if (insightIds.length === 0) {
        return {
          insights: [],
          pagination: commonRepository.buildPaginationInfo(0, page, limit),
        };
      }

      // Step 2: Get count of insights with the given status (or all if status is null/undefined)
      const whereStatus = {
        id: { [Op.in]: insightIds },
        isRemoved: false,
      };
      if (status) {
        whereStatus.status = status;
      }
      // If status is null or undefined, do not filter by status (admin: see all)

      const count = await this.models.Insight.count({
        where: whereStatus,
      });

      if (count === 0) {
        return {
          insights: [],
          pagination: commonRepository.buildPaginationInfo(0, page, limit),
        };
      }

      // Step 3: Get IDs for the current page
      const pagedInsightIds = await this.models.Insight.findAll({
        where: whereStatus,
        attributes: ['id'],
        order: [['createdAt', 'DESC']],
        limit,
        offset,
        raw: true,
      });
      const idsForPage = pagedInsightIds.map((i) => i.id);

      if (idsForPage.length === 0) {
        return {
          insights: [],
          pagination: commonRepository.buildPaginationInfo(count, page, limit),
        };
      }

      // Step 4: Fetch full data for those IDs
      const insights = await this.models.Insight.findAll({
        where: {
          id: { [Op.in]: idsForPage },
          isRemoved: false,
        },
        attributes: this._getInsightAttributes(),
        include: this._getInsightIncludes(),
        order: [['createdAt', 'DESC']],
        distinct: true,
        subQuery: false,
      });
      // Enrich insights with user data if userId provided
      const enrichedInsights = userId
        ? await this.helper.enrichInsightsWithUserData(insights, userId)
        : insights;

      return {
        insights: enrichedInsights,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update insight status (approve or reject) for admin
   * @param {string} id - Insight ID
   * @param {string} adminId - Admin ID
   * @param {string} status - New status (APPROVED or REJECTED)
   * @param {string} rejectionReason - Reason for rejection (required if status is REJECTED)
   * @returns {Promise<Object>} Updated insight
   */
  async updateStatusForAdmin(id, adminId, status, rejectionReason) {
    try {
      const insight = await this.findByIdForAdmin(id);

      // Check if insight is already reviewed
      if (insight.status !== InsightStatus.PENDING) {
        throw new ApiException(400, INSIGHT.ALREADY_REVIEWED);
      }

      // Validate status
      if (!InsightStatus.values.includes(status)) {
        throw new ApiException(400, INSIGHT.INVALID_STATUS);
      }

      // If rejecting, require a reason
      if (status === InsightStatus.REJECTED && !rejectionReason) {
        throw new ApiException(400, INSIGHT.REJECTION_REASON_REQUIRED);
      }

      // Update insight status
      const updateData = {
        status: status,
        reviewedBy: adminId,
        reviewedAt: new Date(),
        rejectionReason:
          status === InsightStatus.REJECTED ? rejectionReason : null,
      };

      await insight.update(updateData);

      const updatedInsight = await this.findByIdForAdmin(id);
      return updatedInsight;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Soft remove an insight (admin only)
   * @param {string} id - Insight ID
   * @returns {Promise<Object>} Updated insight
   */
  async softRemoveInsight(id) {
    try {
      // Fetch the insight regardless of isRemoved status
      const insight = await this.models.Insight.findOne({ where: { id } });
      if (!insight) {
        throw new Error(INSIGHT.NOT_FOUND);
      }
      await insight.update({
        isRemoved: true,
        removedAt: new Date(),
      });
      return insight;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new InsightRepository();
