/**
 * Achievement Model
 * Represents achievements that can be earned by users
 */
const { Model, DataTypes } = require('sequelize');
const { UserType } = require('@utils/enums.utils');

class Achievement extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        formTitle: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        targetValue: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 1,
          validate: {
            min: 1,
          },
        },
        achievementType: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        milestoneId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Milestone',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        userType: {
          type: DataTypes.ENUM(...UserType.values),
          allowNull: false,
          defaultValue: UserType.EDUCATOR,
          validate: {
            isIn: [UserType.values],
          },
        },
      },
      {
        sequelize,
        modelName: 'Achievement',
        tableName: 'Achievement',
        timestamps: true,
        indexes: [
          {
            fields: ['milestoneId'],
            name: 'achievement_milestone_id_idx',
          },
          {
            fields: ['achievementType'],
            name: 'achievement_type_idx',
          },
          {
            fields: ['userType'],
            name: 'achievement_user_type_idx',
          },
          {
            fields: ['isActive'],
            name: 'achievement_is_active_idx',
          },
          {
            fields: ['order'],
            name: 'achievement_order_idx',
          },
          {
            unique: true,
            fields: ['name', 'milestoneId'],
            name: 'achievement_name_milestone_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to milestone
    Achievement.belongsTo(models.Milestone, {
      foreignKey: 'milestoneId',
      as: 'milestone',
      onDelete: 'CASCADE',
    });

    // Has many user achievements
    Achievement.hasMany(models.UserAchievement, {
      foreignKey: 'achievementId',
      as: 'userAchievements',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = Achievement;
