/**
 * Application Enums
 *
 * Contains all application-wide enumerations
 */

/**
 * User roles
 */
const UserRole = {
  ADMIN: 'ADMIN',
  USER: 'USER',
  values: ['ADMIN', 'USER'],
  isValid: (role) => UserRole.values.includes(role),
};

/**
 * User status
 */
const UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  BLOCKED: 'BLOCKED',
  values: ['ACTIVE', 'INACTIVE', 'PENDING', 'BLOCKED'],
  isValid: (status) => UserStatus.values.includes(status),
};

/**
 * User types
 */
const UserType = {
  EDUCATOR: 'EDUCATOR',
  EDUCATOR_PLUS: 'EDUCATOR_PLUS',
  PROVIDER: 'PROVIDER',
  PROVIDER_PLUS: 'PROVIDER_PLUS',
  values: ['EDUCATOR', 'EDUCATOR_PLUS', 'PROVIDER', 'PROVIDER_PLUS'],
  isValid: (type) => UserType.values.includes(type),
};

/**
 * Membership types
 */
const MembershipType = {
  EDUCATOR_PLUS: 'EDUCATOR_PLUS',
  PROVIDER_PLUS: 'PROVIDER_PLUS',
  values: ['EDUCATOR_PLUS', 'PROVIDER_PLUS'],
  isValid: (type) => MembershipType.values.includes(type),
};

/**
 * Platform types
 */
const PlatformType = {
  WEB: 'WEB',
  MOBILE: 'MOBILE',
  API: 'API',
  values: ['WEB', 'MOBILE', 'API'],
  isValid: (platform) => PlatformType.values.includes(platform),
};

/**
 * HTTP status codes
 */
const HttpStatus = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
};

/**
 * Insight status
 */
const InsightStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  values: ['PENDING', 'APPROVED', 'REJECTED'],
  isValid: (status) => InsightStatus.values.includes(status),
};

/**
 * EducatorQuestion status
 */
const EducatorQuestionStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  values: ['PENDING', 'APPROVED', 'REJECTED'],
  isValid: (status) => EducatorQuestionStatus.values.includes(status),
};

/**
 * Media types for experiences and weeks
 */
const MediaType = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  DOCUMENT: 'DOCUMENT',
  LINK: 'LINK',
  AUDIO: 'AUDIO',
  values: ['IMAGE', 'VIDEO', 'DOCUMENT', 'LINK', 'AUDIO'],
  isValid: (type) => MediaType.values.includes(type),
};

/**
 * Report status
 */
const ReportStatus = {
  PENDING: 'PENDING',
  REVIEWED: 'REVIEWED',
  RESOLVED: 'RESOLVED',
  DISMISSED: 'DISMISSED',
  values: ['PENDING', 'REVIEWED', 'RESOLVED', 'DISMISSED'],
  isValid: (status) => ReportStatus.values.includes(status),
};

/**
 * Report reason types
 */
const ReportReason = {
  SPAM: 'SPAM',
  INAPPROPRIATE_CONTENT: 'INAPPROPRIATE_CONTENT',
  HARASSMENT: 'HARASSMENT',
  MISINFORMATION: 'MISINFORMATION',
  COPYRIGHT_VIOLATION: 'COPYRIGHT_VIOLATION',
  HATE_SPEECH: 'HATE_SPEECH',
  OTHER: 'OTHER',
  values: [
    'SPAM',
    'INAPPROPRIATE_CONTENT',
    'HARASSMENT',
    'MISINFORMATION',
    'COPYRIGHT_VIOLATION',
    'HATE_SPEECH',
    'OTHER',
  ],
  isValid: (reason) => ReportReason.values.includes(reason),
};

/**
 * Experience enrollment status
 */
const ExperienceEnrollmentStatus = {
  REGISTERED: 'REGISTERED',
  COMPLETED: 'COMPLETED',
  values: ['REGISTERED', 'COMPLETED'],
  isValid: (status) => ExperienceEnrollmentStatus.values.includes(status),
};

/**
 * Experience week progress status
 */
const ExperienceWeekProgressStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  values: ['PENDING', 'COMPLETED'],
  isValid: (status) => ExperienceWeekProgressStatus.values.includes(status),
};

/**
 * Experience status
 */
const ExperienceStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  values: ['DRAFT', 'PENDING', 'APPROVED', 'REJECTED'],
  isValid: (status) => ExperienceStatus.values.includes(status),
};

/**
 * Question types for milestone questions
 */
const QuestionType = {
  MULTIPLE_CHOICE: 'MULTIPLE_CHOICE',
  TEXT: 'TEXT',
  values: ['MULTIPLE_CHOICE', 'TEXT'],
  isValid: (type) => QuestionType.values.includes(type),
};

/**
 * Achievement Types Enum
 * Organizes all achievement types with their variations
 */
const AchievementType = {
  // Account Related
  CREATE_ACCOUNT: 'CREATE_ACCOUNT',
  PROFILE_PICTURE_UPDATE: 'PROFILE_PICTURE_UPDATE',
  ICE_BREAKER_ANSWERED: 'ICE_BREAKER_ANSWERED',

  // Insight Related - Contributions
  INSIGHT_CONTRIBUTED: 'INSIGHT_CONTRIBUTED',
  INSIGHT_CONTRIBUTED_2: 'INSIGHT_CONTRIBUTED_2',
  INSIGHT_CONTRIBUTED_3: 'INSIGHT_CONTRIBUTED_3',
  INSIGHT_CONTRIBUTED_4: 'INSIGHT_CONTRIBUTED_4',
  INSIGHT_CONTRIBUTED_5: 'INSIGHT_CONTRIBUTED_5',

  // Insight Related - Likes
  INSIGHT_LIKED: 'INSIGHT_LIKED',
  INSIGHT_LIKED_2: 'INSIGHT_LIKED_2',
  PROVIDER_INSIGHT_LIKED: 'PROVIDER_INSIGHT_LIKED',
  PROVIDER_INSIGHT_LIKED_2: 'PROVIDER_INSIGHT_LIKED_2',

  // Insight Related - Saves
  INSIGHT_SAVED: 'INSIGHT_SAVED',
  INSIGHT_SAVED_2: 'INSIGHT_SAVED_2',
  INSIGHT_SAVED_3: 'INSIGHT_SAVED_3',

  // Insight Related - Implementation
  INSIGHT_IMPLEMENTED: 'INSIGHT_IMPLEMENTED',
  INSIGHT_IMPLEMENTED_2: 'INSIGHT_IMPLEMENTED_2',
  INSIGHT_IMPLEMENTED_3: 'INSIGHT_IMPLEMENTED_3',

  // Provider Specific - Posts
  INSIGHT_POSTED: 'INSIGHT_POSTED',
  INSIGHT_POSTED_2: 'INSIGHT_POSTED_2',
  INSIGHT_POSTED_3: 'INSIGHT_POSTED_3',
  INSIGHT_POSTED_4: 'INSIGHT_POSTED_4',

  // Provider Specific - Interactions
  CONTRIBUTE_OTHER_PROVIDER: 'CONTRIBUTE_OTHER_PROVIDER',
  CONTRIBUTE_OTHER_PROVIDER_2: 'CONTRIBUTE_OTHER_PROVIDER_2',
  CONTRIBUTE_OTHER_PROVIDER_3: 'CONTRIBUTE_OTHER_PROVIDER_3',

  // Social Interactions
  FOLLOW_PROVIDERS: 'FOLLOW_PROVIDERS',
  FOLLOW_PROVIDERS_2: 'FOLLOW_PROVIDERS_2',
  FOLLOW_OTHER_PROVIDERS: 'FOLLOW_OTHER_PROVIDERS',
  FOLLOW_OTHER_PROVIDERS_2: 'FOLLOW_OTHER_PROVIDERS_2',
  LIKE_EDUCATOR_CONTRIBUTIONS: 'LIKE_EDUCATOR_CONTRIBUTIONS',

  // Forms/Experiences
  REFLECTION_FORM: 'REFLECTION_FORM',
  VISIONARY_FORM: 'VISIONARY_FORM',
  INNOVATOR_FORM: 'INNOVATOR_FORM',
  TRAILBLAZER_FORM: 'TRAILBLAZER_FORM',
  EXPERIENCE_COMPLETED: 'EXPERIENCE_COMPLETED',
  EXPERIENCE_COMPLETED_2: 'EXPERIENCE_COMPLETED_2',
  EXPERIENCE_CREATED: 'EXPERIENCE_CREATED',
  EXPERIENCE_CREATED_2: 'EXPERIENCE_CREATED_2',

  // Helper method to get base type without number
  getBaseType: (type) => {
    return type.replace(/_\d+$/, '');
  },

  // Helper method to check if type is a numbered variation
  isNumberedVariation: (type) => {
    return /\d+$/.test(type);
  },
};

/**
 * Subscription Plan Slug
 */
const SubscriptionPlanSlug = {
  EDUCATOR_FREE: 'EDUCATOR_FREE',
  EDUCATOR_PLUS_ANNUAL: 'EDUCATOR_PLUS_ANNUAL',
  EDUCATOR_PLUS_3_MONTH: 'EDUCATOR_PLUS_3_MONTH',
  PROVIDER_FREE: 'PROVIDER_FREE',
  PROVIDER_PLUS_ANNUAL: 'PROVIDER_PLUS_ANNUAL',
  values: [
    'EDUCATOR_FREE',
    'EDUCATOR_PLUS_ANNUAL',
    'EDUCATOR_PLUS_3_MONTH',
    'PROVIDER_FREE',
    'PROVIDER_PLUS_ANNUAL',
  ],
  isValid: (slug) => SubscriptionPlanSlug.values.includes(slug),
};

/**
 * Subscription Plan Billing Interval Enum
 */
const SubscriptionPlanBillingInterval = {
  ONE_TIME: 'ONE_TIME',
  MONTHLY: 'MONTHLY',
  EVERY_3_MONTHS: 'EVERY_3_MONTHS',
  YEARLY: 'YEARLY',
  isValid: (value) => {
    return Object.values(SubscriptionPlanBillingInterval).includes(value);
  },
};

/**
 * Subscription Plan Insights Limit Period Enum
 */
const SubscriptionPlanInsightsLimitPeriod = {
  MONTHLY: 'MONTHLY',
  YEARLY: 'YEARLY',
  isValid: (value) => {
    return Object.values(SubscriptionPlanInsightsLimitPeriod).includes(value);
  },
};

/**
 * Subscription status
 */
const SubscriptionStatus = {
  ACTIVE: 'ACTIVE',
  INCOMPLETE: 'INCOMPLETE',
  INCOMPLETE_EXPIRED: 'INCOMPLETE_EXPIRED',
  PAST_DUE: 'PAST_DUE',
  CANCELED: 'CANCELED',
  UNPAID: 'UNPAID',
  TRIALING: 'TRIALING',
  PAUSED: 'PAUSED',
  values: [
    'ACTIVE',
    'INCOMPLETE',
    'INCOMPLETE_EXPIRED',
    'PAST_DUE',
    'CANCELED',
    'UNPAID',
    'TRIALING',
    'PAUSED',
  ],
  isValid: (status) => SubscriptionStatus.values.includes(status),
};

/**
 * Subscription pause behavior
 */
const PauseBehavior = {
  KEEP_AS_DRAFT: 'KEEP_AS_DRAFT',
  MARK_UNCOLLECTIBLE: 'MARK_UNCOLLECTIBLE',
  VOID: 'VOID',
  values: ['KEEP_AS_DRAFT', 'MARK_UNCOLLECTIBLE', 'VOID'],
  isValid: (behavior) => PauseBehavior.values.includes(behavior),
};

/**
 * Subscription types
 */
const SubscriptionType = {
  ONLINE: 'ONLINE',
  OFFLINE: 'OFFLINE',
  values: ['ONLINE', 'OFFLINE'],
  isValid: (type) => ['ONLINE', 'OFFLINE'].includes(type),
};

/**
 * CreatedVia types
 */
const CreatedVia = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  values: ['USER', 'ADMIN'],
  isValid: (type) => ['USER', 'ADMIN'].includes(type),
};

/**
 * Device types
 */
const DeviceType = {
  IOS: 'IOS',
  ANDROID: 'ANDROID',
  WEB: 'WEB',
  values: ['IOS', 'ANDROID', 'WEB'],
  isValid: (type) => DeviceType.values.includes(type),
};

/**
 * Notification types
 */
const NotificationType = {
  CONTRIBUTION: 'CONTRIBUTION',
  IMPLEMENTATION: 'IMPLEMENTATION',
  EXPERIENCE_COMPLETION: 'EXPERIENCE_COMPLETION',
  INSIGHT_APPROVED: 'INSIGHT_APPROVED',
  EXPERIENCE_APPROVED: 'EXPERIENCE_APPROVED',
  NEW_FOLLOWERS: 'NEW_FOLLOWERS',
  NEW_FOLLOWERS_MONTHLY: 'NEW_FOLLOWERS_MONTHLY',
  LIKE_CONTRIBUTION: 'LIKE_CONTRIBUTION',
  EXPERIENCE_START_REMINDER: 'EXPERIENCE_START_REMINDER',
  EXPERIENCE_STARTED: 'EXPERIENCE_STARTED',
  MILESTONE_COMPLETED: 'MILESTONE_COMPLETED',
  EDUCATOR_QUESTION_APPROVED: 'EDUCATOR_QUESTION_APPROVED',
  values: [
    'CONTRIBUTION',
    'IMPLEMENTATION',
    'EXPERIENCE_COMPLETION',
    'INSIGHT_APPROVED',
    'EXPERIENCE_APPROVED',
    'NEW_FOLLOWERS',
    'NEW_FOLLOWERS_MONTHLY',
    'LIKE_CONTRIBUTION',
    'EXPERIENCE_START_REMINDER',
    'EXPERIENCE_STARTED',
    'MILESTONE_COMPLETED',
    'EDUCATOR_QUESTION_APPROVED',
  ],
  isValid: (type) => NotificationType.values.includes(type),
};

/**
 * Account status for user deletion workflow
 */
const AccountStatus = {
  ACTIVE: 'ACTIVE',
  ON_HOLD: 'ON_HOLD',
  DELETED: 'DELETED',
  values: ['ACTIVE', 'ON_HOLD', 'DELETED'],
  isValid: (status) => AccountStatus.values.includes(status),
};

/**
 * Milestone limits for plans
 */
const EDUCATOR_FREE_MILESTONE_LIMIT = 4;

module.exports = {
  UserRole,
  UserStatus,
  UserType,
  MembershipType,
  PlatformType,
  HttpStatus,
  InsightStatus,
  EducatorQuestionStatus,
  MediaType,
  ReportStatus,
  ReportReason,
  ExperienceEnrollmentStatus,
  ExperienceWeekProgressStatus,
  ExperienceStatus,
  QuestionType,
  AchievementType,
  SubscriptionPlanSlug,
  SubscriptionPlanBillingInterval,
  SubscriptionPlanInsightsLimitPeriod,
  SubscriptionStatus,
  PauseBehavior,
  SubscriptionType,
  CreatedVia,
  DeviceType,
  NotificationType,
  AccountStatus,
  EDUCATOR_FREE_MILESTONE_LIMIT,
};
