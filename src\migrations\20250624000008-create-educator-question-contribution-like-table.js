'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('EducatorQuestionContributionLike', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      contributionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'EducatorQuestionContribution',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add unique constraint
    await queryInterface.addIndex(
      'EducatorQuestionContributionLike',
      ['userId', 'contributionId'],
      {
        unique: true,
        name: 'educator_question_contribution_like_unique_idx',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('EducatorQuestionContributionLike');
  },
};
