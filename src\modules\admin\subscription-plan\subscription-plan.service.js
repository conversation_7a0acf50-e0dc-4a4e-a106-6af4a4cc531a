/* eslint-disable camelcase */
/**
 * Admin Subscription Plan Service
 *
 * Handles subscription plan business logic
 */
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const {
  SUBSCRIPTION_PLAN: SUBSCRIPTION_PLAN_MESSAGES,
} = require('@utils/messages.utils');
const {
  SubscriptionPlanBillingInterval,
  SubscriptionPlanInsightsLimitPeriod,
} = require('@utils/enums.utils');
const { SUBSCRIPTION_PLAN } = require('@utils/constants');
const { generateSlug } = require('@utils/helpers.utils');
const subscriptionPlanRepository = require('@models/repositories/subscription-plan.repository');
// const {
//   StripeSubscription,
// } = require('@integrations/stripe/stripe.subscription');

const StripeProduct = require('@integrations/stripe/stripe.product');

/**
 * Subscription Plan service
 */
const subscriptionPlanService = {
  /**
   * Seed subscription plans
   * @returns {Promise<Array>} Array of created plans
   * @throws {ApiException} If any plan already exists
   */
  seedPlans: async () => {
    const plans = Object.entries(SUBSCRIPTION_PLAN).map(([key, plan]) => ({
      ...plan,
      billingInterval: SubscriptionPlanBillingInterval[plan.billingInterval],
      insightsLimitPeriod: plan.insightsLimitPeriod
        ? SubscriptionPlanInsightsLimitPeriod[plan.insightsLimitPeriod]
        : null,
      slug: generateSlug(plan.name),
    }));

    const createdPlans = [];
    for (const plan of plans) {
      try {
        // First check if plan exists in database
        const existingPlan = await subscriptionPlanRepository.findBySlug(
          plan.slug
        );

        if (existingPlan) {
          throw new ApiException(
            HttpStatus.CONFLICT,
            SUBSCRIPTION_PLAN_MESSAGES.PLAN_EXISTS(plan.slug)
          );
        }

        // If plan has a price, check if product exists in Stripe first
        let stripeIds = {};
        if (plan.priceCents > 0) {
          // Check if plan exists in Stripe by slug
          const existingStripePlan = await StripeProduct.checkExistingPlan(
            plan.slug
          );

          if (existingStripePlan) {
            stripeIds = {
              stripeProductId: existingStripePlan.stripeProductId,
              stripePriceId: existingStripePlan.stripePriceId,
            };
          } else {
            // If no existing product, create new one
            stripeIds = await StripeProduct.createProductAndPrice(
              plan,
              plan.slug
            );
          }
        }

        // Create plan in database with Stripe IDs if available
        const newPlan = await subscriptionPlanRepository.createPlan({
          ...plan,
          ...stripeIds,
        });

        createdPlans.push(newPlan);
      } catch (error) {
        throw error;
      }
    }
    return createdPlans;
  },

  /**
   * Get all subscription plans
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search term
   * @param {string} options.targetUserType - Filter by target user type (EDUCATOR or PROVIDER)
   * @returns {Promise<Object>} Paginated plans
   */
  getAllPlans: async (options) => {
    const plans = await subscriptionPlanRepository.findAll(options);

    if (!plans) {
      throw new ApiException(
        HttpStatus.NOT_FOUND,
        SUBSCRIPTION_PLAN_MESSAGES.FIND_ERROR
      );
    }
    return plans;
  },

  /**
   * Get subscription plan by ID
   * @param {string} id - Plan ID
   * @returns {Promise<Object>} Plan details
   */
  getPlanById: async (id) => {
    const plan = await subscriptionPlanRepository.findById(id);
    if (!plan) {
      throw new ApiException(
        HttpStatus.NOT_FOUND,
        SUBSCRIPTION_PLAN_MESSAGES.NOT_FOUND
      );
    }
    return plan;
  },

  /**
   * Create subscription plan
   * @param {Object} data - Plan data
   * @returns {Promise<Object>} Created plan
   */
  createPlan: async (data) => {
    const planData = {
      ...data,
      slug: generateSlug(data.name),
    };

    // If plan has a price, check if product exists in Stripe first
    let stripeIds = {};
    if (data.priceCents > 0) {
      // Check if plan exists in Stripe by slug
      const existingStripePlan = await StripeProduct.checkExistingPlan(
        planData.slug
      );

      if (existingStripePlan) {
        stripeIds = {
          stripeProductId: existingStripePlan.stripeProductId,
          stripePriceId: existingStripePlan.stripePriceId,
        };
      } else {
        // If no existing product, create new one
        stripeIds = await StripeProduct.createProductAndPrice(
          planData,
          planData.slug
        );
      }
    }

    // Create plan in database with Stripe IDs
    const newPlan = await subscriptionPlanRepository.createPlan({
      ...planData,
      ...stripeIds,
    });

    return newPlan;
  },

  /**
   * Update subscription plan
   * @param {string} id - Plan ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated plan
   */
  updatePlan: async (id, data) => {
    const planData = {
      ...data,
      slug: data.name ? generateSlug(data.name) : undefined,
    };
    return await subscriptionPlanRepository.updatePlan(id, planData);
  },

  /**
   * Delete subscription plan
   * @param {string} id - Plan ID
   * @returns {Promise<boolean>} Success status
   */
  deletePlan: async (id) => {
    const plan = await subscriptionPlanRepository.findById(id);
    if (!plan) {
      throw new ApiException(
        HttpStatus.NOT_FOUND,
        SUBSCRIPTION_PLAN_MESSAGES.NOT_FOUND
      );
    }

    if (plan.stripeProductId) {
      await StripeProduct.deleteProduct(plan.stripeProductId);
    }

    await subscriptionPlanRepository.delete(id);
    return true;
  },
};

module.exports = subscriptionPlanService;
