/**
 * MilestoneQuestion Model
 * Represents questions for milestone progression
 */
const { Model, DataTypes } = require('sequelize');
const { QuestionType } = require('@utils/enums.utils');

class MilestoneQuestion extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        milestoneId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Milestone',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        question: {
          type: DataTypes.TEXT,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        questionType: {
          type: DataTypes.ENUM(...QuestionType.values),
          allowNull: false,
          defaultValue: QuestionType.MULTIPLE_CHOICE,
          validate: {
            isIn: [QuestionType.values],
          },
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
        isRequired: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
      },
      {
        sequelize,
        modelName: 'MilestoneQuestion',
        tableName: 'MilestoneQuestion',
        timestamps: true,
        indexes: [
          {
            fields: ['milestoneId'],
            name: 'milestone_question_milestone_id_idx',
          },
          {
            fields: ['isActive'],
            name: 'milestone_question_is_active_idx',
          },
          {
            fields: ['order'],
            name: 'milestone_question_order_idx',
          },
          {
            fields: ['questionType'],
            name: 'milestone_question_type_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to milestone
    MilestoneQuestion.belongsTo(models.Milestone, {
      foreignKey: 'milestoneId',
      as: 'milestone',
      onDelete: 'CASCADE',
    });

    // Has many user responses
    MilestoneQuestion.hasMany(models.UserMilestoneResponse, {
      foreignKey: 'questionId',
      as: 'responses',
      onDelete: 'CASCADE',
    });

    // Has many options
    MilestoneQuestion.hasMany(models.MilestoneQuestionOption, {
      foreignKey: 'questionId',
      as: 'options',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = MilestoneQuestion;
