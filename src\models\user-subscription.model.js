const { Model, DataTypes } = require('sequelize');

class UserSubscription extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        subscriptionPlanId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'SubscriptionPlan',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        stripeSubscriptionId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        stripeSubscriptionDetails: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        status: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        currentPeriodStart: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        currentPeriodEnd: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        cancelAtPeriodEnd: {
          type: DataTypes.BOOLEAN,
          allowNull: true,
          defaultValue: false,
        },
        canceledAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        trialStart: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        trialEnd: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        paymentMethodId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'PaymentMethod',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        pausedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        resumedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        pauseBehavior: {
          type: DataTypes.STRING,
          allowNull: true, // null means not paused
        },
        previousSubscriptionId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'UserSubscription',
            key: 'id',
          },
          onDelete: 'SET NULL',
        },
        type: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: 'ONLINE',
        },
      },
      {
        sequelize,
        modelName: 'UserSubscription',
        tableName: 'UserSubscription',
        timestamps: true,
        indexes: [
          {
            fields: ['userId'],
            name: 'user_subscription_user_id_idx',
          },
          {
            fields: ['subscriptionPlanId'],
            name: 'user_subscription_plan_id_idx',
          },
          {
            fields: ['status'],
            name: 'user_subscription_status_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    UserSubscription.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
    UserSubscription.belongsTo(models.SubscriptionPlan, {
      foreignKey: 'subscriptionPlanId',
      as: 'plan',
      onDelete: 'CASCADE',
    });
    UserSubscription.belongsTo(models.PaymentMethod, {
      foreignKey: 'paymentMethodId',
      as: 'paymentMethod',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = UserSubscription;
