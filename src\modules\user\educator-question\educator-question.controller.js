/**
 * User EducatorQuestion Controller
 *
 * Handles educator-question-related HTTP requests for users
 */
const educatorQuestionService = require('./services/educator-question.service');
const { ApiResponse } = require('@utils/response.utils');
const { EDUCATOR_QUESTION } = require('@utils/messages.utils');
const likeService = require('./services/like.service');
const bookmarkService = require('./services/bookmark.service');
const implementService = require('./services/implement.service');

const educatorQuestionController = {
  /**
   * Create a new educator question (pass req.body, req.user directly)
   */
  createEducatorQuestion: async (req, res, next) => {
    try {
      const question = await educatorQuestionService.createEducatorQuestion(
        req.body,
        req.user
      );
      return ApiResponse.created(res, EDUCATOR_QUESTION.CREATED, question);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all educator questions (pass req.query, req.user directly)
   */
  getAllEducatorQuestions: async (req, res, next) => {
    try {
      const result = await educatorQuestionService.getAllEducatorQuestions(
        req.query,
        req.user
      );
      return ApiResponse.success(
        res,
        EDUCATOR_QUESTION.ALL_RETRIEVED,
        result.questions
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get educator question by ID (pass req.params, req.user directly)
   */
  getEducatorQuestionById: async (req, res, next) => {
    try {
      const { questionId } = req.params;
      const question = await educatorQuestionService.getEducatorQuestionById(
        questionId,
        req.user
      );
      return ApiResponse.success(res, EDUCATOR_QUESTION.RETRIEVED, question);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle like status for an educator question
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  toggleLike: async (req, res, next) => {
    try {
      const { questionId } = req.params;
      const result = await likeService.toggleLike(req.user, questionId);
      return ApiResponse.success(
        res,
        result.isLiked
          ? EDUCATOR_QUESTION.LIKE_ADDED
          : EDUCATOR_QUESTION.LIKE_REMOVED,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle implement status for an educator question
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  toggleImplement: async (req, res, next) => {
    try {
      const { questionId } = req.params;
      const result = await implementService.toggleImplement(
        req.user,
        questionId
      );
      return ApiResponse.success(
        res,
        result.isImplemented
          ? EDUCATOR_QUESTION.IMPLEMENT_ADDED
          : EDUCATOR_QUESTION.IMPLEMENT_REMOVED,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle bookmark status for an educator question
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  toggleBookmark: async (req, res, next) => {
    try {
      const { questionId } = req.params;
      const result = await bookmarkService.toggleBookmark(req.user, questionId);
      return ApiResponse.success(
        res,
        result.isBookmarked
          ? EDUCATOR_QUESTION.BOOKMARK_ADDED
          : EDUCATOR_QUESTION.BOOKMARK_REMOVED,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all bookmarked educator questions
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getBookmarkedQuestions: async (req, res, next) => {
    try {
      const { page, limit } = req.pagination;
      const result = await bookmarkService.getBookmarkedQuestions(req.user.id, {
        page,
        limit,
      });
      return ApiResponse.success(
        res,
        EDUCATOR_QUESTION.BOOKMARKS_RETRIEVED,
        result.questions,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },
};

module.exports = educatorQuestionController;
