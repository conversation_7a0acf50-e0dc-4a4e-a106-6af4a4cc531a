'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Notification', 'receiverUserId', {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });
    await queryInterface.addIndex('Notification', ['receiverUserId'], {
      name: 'notification_receiver_user_id_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'Notification',
      'notification_receiver_user_id_idx'
    );
    await queryInterface.removeColumn('Notification', 'receiverUserId');
  },
};
