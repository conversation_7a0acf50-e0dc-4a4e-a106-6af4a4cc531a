openapi: 3.0.0
info:
  title: WTD Platform User Upload API
  version: 1.0.0
  description: API endpoints for file uploads and retrievals

paths:
  /upload:
    post:
      tags:
        - Upload
      summary: Upload File
      description: Upload a file to S3 storage
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The file to upload (max size 100MB)
      responses:
        '201':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '400':
          description: Bad Request - No file provided or invalid file
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '413':
          description: Payload Too Large - File size exceeds 100MB limit
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /upload/{key}:
    get:
      tags:
        - Upload
      summary: Get File URL
      description: Get the URL of an uploaded file by its key
      security:
        - BearerAuth: []
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: The file key (unique identifier)
      responses:
        '200':
          description: File URL retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUrlResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: File not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    UploadResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "File uploaded successfully"
        data:
          type: object
          properties:
            key:
              type: string
              description: The unique identifier for the uploaded file
              example: "uploads/123e4567-e89b-12d3-a456-426614174000.pdf"
            url:
              type: string
              description: The URL where the file can be accessed
              example: "https://example-bucket.s3.amazonaws.com/uploads/123e4567-e89b-12d3-a456-426614174000.pdf"

    FileUrlResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "File URL retrieved successfully"
        data:
          type: object
          properties:
            url:
              type: string
              description: The URL where the file can be accessed
              example: "https://example-bucket.s3.amazonaws.com/uploads/123e4567-e89b-12d3-a456-426614174000.pdf"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        error:
          type: string
          example: "Detailed error information" 