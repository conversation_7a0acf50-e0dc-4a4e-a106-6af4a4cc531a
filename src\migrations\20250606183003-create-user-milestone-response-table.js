'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('UserMilestoneResponse', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      questionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'MilestoneQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      response: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      selectedOptionId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'MilestoneQuestionOption',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
    await queryInterface.addIndex(
      'UserMilestoneResponse',
      ['userId', 'questionId'],
      { name: 'user_milestone_response_unique_idx', unique: true }
    );
    await queryInterface.addIndex('UserMilestoneResponse', ['userId'], {
      name: 'user_milestone_response_user_id_idx',
    });
    await queryInterface.addIndex('UserMilestoneResponse', ['questionId'], {
      name: 'user_milestone_response_question_id_idx',
    });
    await queryInterface.addIndex(
      'UserMilestoneResponse',
      ['selectedOptionId'],
      { name: 'user_milestone_response_option_id_idx' }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'UserMilestoneResponse',
      'user_milestone_response_option_id_idx'
    );
    await queryInterface.removeIndex(
      'UserMilestoneResponse',
      'user_milestone_response_question_id_idx'
    );
    await queryInterface.removeIndex(
      'UserMilestoneResponse',
      'user_milestone_response_user_id_idx'
    );
    await queryInterface.removeIndex(
      'UserMilestoneResponse',
      'user_milestone_response_unique_idx'
    );
    await queryInterface.dropTable('UserMilestoneResponse');
  },
};
