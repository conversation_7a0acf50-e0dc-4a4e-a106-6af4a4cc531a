/**
 * Admin Subscription Plan Module
 *
 * This module handles subscription plan-related functionality for admins
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const paginationMiddleware = require('@middlewares/pagination.middleware');
const subscriptionPlanController = require('./subscription-plan.controller');
const subscriptionPlanValidation = require('./subscription-plan.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Seed subscription plans
  router.post(
    '/seed',
    authenticate,
    validate(subscriptionPlanValidation.seed),
    subscriptionPlanController.seedSubscriptionPlans
  );

  // Get all subscription plans
  router.get(
    '/',
    authenticate,
    paginationMiddleware,
    validate(subscriptionPlanValidation.getAll),
    subscriptionPlanController.getAllSubscriptionPlans
  );

  // Get subscription plan by ID
  router.get(
    '/:id',
    authenticate,
    validate(subscriptionPlanValidation.getById),
    subscriptionPlanController.getSubscriptionPlanById
  );

  // Create subscription plan
  router.post(
    '/',
    authenticate,
    validate(subscriptionPlanValidation.create),
    subscriptionPlanController.createSubscriptionPlan
  );

  // Update subscription plan
  router.put(
    '/:id',
    authenticate,
    validate(subscriptionPlanValidation.update),
    subscriptionPlanController.updateSubscriptionPlan
  );

  // Delete subscription plan
  router.delete(
    '/:id',
    authenticate,
    validate(subscriptionPlanValidation.delete),
    subscriptionPlanController.deleteSubscriptionPlan
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
