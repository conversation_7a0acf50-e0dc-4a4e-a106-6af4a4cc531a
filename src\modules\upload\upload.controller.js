/**
 * Upload Controller
 *
 * Handles file upload-related HTTP requests
 */
const uploadService = require('./upload.service');
const { ApiResponse } = require('@utils/response.utils');
const { UPLOAD } = require('@utils/messages.utils');

/**
 * Upload controller
 */
const uploadController = {
  /**
   * Upload a file to S3
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  uploadFile: async (req, res, next) => {
    try {
      if (!req.file) {
        return ApiResponse.error(res, 'No file uploaded', null, 400);
      }

      const result = await uploadService.uploadFile(req.file);
      return ApiResponse.success(res, UPLOAD.FILE_UPLOADED, {
        key: result.key,
        fileUrl: result.fileUrl,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get file URL by key
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getFileByKey: async (req, res, next) => {
    try {
      const { key } = req.params;
      const result = await uploadService.getFileByKey(key);
      return ApiResponse.success(res, UPLOAD.FILE_RETRIEVED, {
        fileUrl: result.fileUrl,
      });
    } catch (error) {
      next(error);
    }
  },
};

module.exports = uploadController;
