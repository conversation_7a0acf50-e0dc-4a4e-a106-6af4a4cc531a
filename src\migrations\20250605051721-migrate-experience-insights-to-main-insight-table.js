'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Simply delete all existing experiences to avoid complex data migration
    // This is simpler than migrating and acceptable if data loss is okay
    await queryInterface.sequelize.query(`DELETE FROM "Experience";`);
  },

  async down(queryInterface, Sequelize) {
    // No rollback needed since we just deleted data
    // Warning: This cannot restore deleted experiences
  },
};
