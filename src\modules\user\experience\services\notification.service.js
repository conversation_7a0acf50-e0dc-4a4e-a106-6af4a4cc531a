const notificationRepository = require('@repositories/notification.repository');
const {
  sendPushNotification,
  sendSilentNotification,
} = require('@integrations/firebase/notification.service');
const { NotificationType } = require('@utils/enums.utils');
const { NOTIFICATION } = require('@utils/messages.utils');

/**
 * Notify provider when experience is completed by educators (scheduled)
 * Trigger: Weekly cron job (Sunday 10am EST/8:30pm IST)
 * @param {Object} params
 * @param {Object} params.provider - The provider (creator) of the experience
 * @param {Object} params.experience - The experience object
 * @param {number} params.count - Number of educators who completed the experience
 */
async function notifyExperienceCompletedThisWeek({
  provider,
  experience,
  count,
}) {
  try {
    if (provider && provider.deviceToken) {
      const notificationText = `${count} educators completed your experience this week.`;
      const commonNotificationPayload = {
        userId: null,
        receiverUserId: provider.id,
        type: NotificationType.EXPERIENCE_COMPLETION,
        title: NOTIFICATION.EXPERIENCE_COMPLETED_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { experienceId: experience.id },
      });
      await sendPushNotification({
        deviceToken: provider.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          experienceId: experience.id,
          redirectUrl: `/experinces/${experience.id}`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Notify provider when experience is approved
 * Trigger: When an experience is approved by admin
 * @param {Object} experience - The experience object
 */
async function notifyExperienceApproved(experience) {
  const provider = experience.creator;
  try {
    if (provider && provider.deviceToken) {
      const notificationText = `${experience.title} is approved and now live!`;
      const commonNotificationPayload = {
        userId: null,
        receiverUserId: provider.id,
        type: NotificationType.EXPERIENCE_APPROVED,
        title: NOTIFICATION.EXPERIENCE_APPROVED_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { experienceId: experience.id },
      });
      await sendPushNotification({
        deviceToken: provider.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          experienceId: experience.id,
          redirectUrl: `/experinces/${experience.id}`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Notify user 5 days before experience starts
 * Trigger: Daily cron job (10am EST/8:30pm IST), 5 days before experience start date
 * @param {Object} params
 * @param {Object} params.user - The user to notify
 * @param {Object} params.experience - The experience object
 */
async function notifyExperienceStartReminder({ user, experience }) {
  try {
    if (user && user.deviceToken) {
      const notificationText = `Your registered experience, ${experience.title}, begins on Monday.`;
      const commonNotificationPayload = {
        userId: null,
        receiverUserId: user.id,
        type: NotificationType.EXPERIENCE_START_REMINDER,
        title: NOTIFICATION.EXPERIENCE_START_REMINDER_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { experienceId: experience.id },
      });
      await sendPushNotification({
        deviceToken: user.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          experienceId: experience.id,
          redirectUrl: `/experinces/${experience.id}`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Notify user when experience starts
 * Trigger: Daily cron job (10am EST/8:30pm IST), on the day the experience starts
 * @param {Object} params
 * @param {Object} params.user - The user to notify
 * @param {Object} params.experience - The experience object
 */
async function notifyExperienceStarted({ user, experience }) {
  try {
    if (user && user.deviceToken) {
      const notificationText = `${experience.title} has officially begun. Cheers to the start of something new!`;
      const commonNotificationPayload = {
        userId: null,
        receiverUserId: user.id,
        type: NotificationType.EXPERIENCE_STARTED,
        title: NOTIFICATION.EXPERIENCE_STARTED_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { experienceId: experience.id },
      });
      await sendPushNotification({
        deviceToken: user.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          experienceId: experience.id,
          redirectUrl: `/experinces/${experience.id}`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Notify user when a milestone is completed (all achievements completed)
 * @param {Object} params
 * @param {Object} params.user - The user who completed the milestone
 * @param {Object} params.milestone - The milestone object
 */
async function notifyMilestoneCompleted({ user, milestone }) {
  try {
    const notificationText = `Congratulations! You have completed all achievements for the milestone: ${milestone.name}`;
    const commonNotificationPayload = {
      userId: null,
      receiverUserId: user.id,
      type: NotificationType.MILESTONE_COMPLETED,
      title: NOTIFICATION.MILESTONE_COMPLETED_TITLE,
      body: notificationText,
    };
    await notificationRepository.createNotification({
      ...commonNotificationPayload,
      data: { milestoneId: milestone.id },
    });
    if (user && user.deviceToken) {
      // Send the whole notification data in the silent push
      await sendSilentNotification({
        deviceToken: user.deviceToken,
        data: {
          ...commonNotificationPayload,
          milestoneId: milestone.id,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

module.exports = {
  notifyExperienceCompletedThisWeek,
  notifyExperienceApproved,
  notifyExperienceStartReminder,
  notifyExperienceStarted,
  notifyMilestoneCompleted,
};
