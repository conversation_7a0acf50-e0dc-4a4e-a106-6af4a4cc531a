/**
 * UserAchievement Model - Tracks individual achievement progress
 */
const { Model, DataTypes } = require('sequelize');

class UserAchievement extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        achievementId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Achievement',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        currentValue: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        isCompleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        completedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'UserAchievement',
        tableName: 'UserAchievement',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'achievementId'],
            name: 'user_achievement_unique_idx',
          },
          {
            fields: ['userId'],
            name: 'user_achievement_user_id_idx',
          },
          {
            fields: ['achievementId'],
            name: 'user_achievement_achievement_id_idx',
          },
          {
            fields: ['isCompleted'],
            name: 'user_achievement_is_completed_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to user
    UserAchievement.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    // Belongs to achievement
    UserAchievement.belongsTo(models.Achievement, {
      foreignKey: 'achievementId',
      as: 'achievement',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = UserAchievement;
