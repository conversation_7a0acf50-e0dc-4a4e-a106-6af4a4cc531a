'use strict';

const { UserType } = require('../utils/enums.utils');

/**
 * Seeder to create a trigger that automatically completes the first milestone for new users
 * This trigger will run whenever a new user is created
 */
module.exports = {
  async up(queryInterface) {
    // Create a sequence for generating unique IDs
    await queryInterface.sequelize.query(`
      CREATE SEQUENCE IF NOT EXISTS user_milestone_id_seq;
      CREATE SEQUENCE IF NOT EXISTS user_achievement_id_seq;
    `);

    // Create the function that will be called by the trigger
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION complete_first_milestone()
      RETURNS TRIGGER AS $$
      DECLARE
        first_milestone_id UUID;
        milestone_identifier TEXT;
        user_milestone_id UUID;
        user_achievement_id UUID;
      BEGIN
        -- Set the milestone identifier based on user type
        milestone_identifier := CASE NEW."userType"
          WHEN '${UserType.EDUCATOR}' THEN 'EDUCATOR_WTD_BEGINNER'
          WHEN '${UserType.EDUCATOR_PLUS}' THEN 'EDUCATOR_PLUS_WTD_BEGINNER'
          WHEN '${UserType.PROVIDER}' THEN 'PROVIDER_WTD_BEGINNER'
          WHEN '${UserType.PROVIDER_PLUS}' THEN 'PROVIDER_PLUS_WTD_BEGINNER'
          ELSE NULL
        END;

        -- Get the first milestone for this user type
        SELECT id INTO first_milestone_id
        FROM "Milestone"
        WHERE "userType" = NEW."userType"
        AND "milestoneIdentifier" = milestone_identifier
        AND "isActive" = true
        ORDER BY "order" ASC
        LIMIT 1;

        -- If milestone found, create the completion entries
        IF first_milestone_id IS NOT NULL THEN
          -- Generate unique IDs using sequences
          user_milestone_id := md5(nextval('user_milestone_id_seq')::text)::uuid;
          user_achievement_id := md5(nextval('user_achievement_id_seq')::text)::uuid;

          -- Create UserMilestone entry
          INSERT INTO "UserMilestone" (
            "id",
            "userId",
            "milestoneId",
            "createdAt",
            "updatedAt",
            "progress",
            "isCurrent"
          ) VALUES (
            user_milestone_id,
            NEW.id,
            first_milestone_id,
            NOW(),
            NOW(),
            100,
            true
          );

          -- Get the CREATE_ACCOUNT achievement for this milestone
          WITH achievement AS (
            SELECT id 
            FROM "Achievement"
            WHERE "milestoneId" = first_milestone_id
            AND "achievementType" = 'CREATE_ACCOUNT'
            AND "isActive" = true
            LIMIT 1
          )
          INSERT INTO "UserAchievement" (
            "id",
            "userId",
            "achievementId",
            "completedAt",
            "createdAt",
            "updatedAt",
            "isCompleted",
            "currentValue"
          )
          SELECT 
            user_achievement_id,
            NEW.id,
            id,
            NOW(),
            NOW(),
            NOW(),
            true,
            1
          FROM achievement
          WHERE id IS NOT NULL;
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the trigger
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS complete_first_milestone_trigger ON "User";
      CREATE TRIGGER complete_first_milestone_trigger
      AFTER INSERT ON "User"
      FOR EACH ROW
      EXECUTE FUNCTION complete_first_milestone();
    `);
  },

  async down(queryInterface) {
    // Drop the trigger, function, and sequences
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS complete_first_milestone_trigger ON "User";
      DROP FUNCTION IF EXISTS complete_first_milestone();
      DROP SEQUENCE IF EXISTS user_milestone_id_seq;
      DROP SEQUENCE IF EXISTS user_achievement_id_seq;
    `);
  },
};
