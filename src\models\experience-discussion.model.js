/**
 * Experience Discussion Model
 * Represents user discussions (comments) on experiences
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceDiscussion extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        experienceId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Experience',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        content: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
      },
      {
        sequelize,
        modelName: 'ExperienceDiscussion',
        tableName: 'ExperienceDiscussion',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceId'],
            name: 'experience_discussion_experience_id_idx',
          },
          {
            fields: ['createdBy'],
            name: 'experience_discussion_created_by_idx',
          },
          {
            fields: ['createdAt'],
            name: 'experience_discussion_created_at_idx',
          },
          {
            fields: ['experienceId', 'createdAt'],
            name: 'experience_discussion_experience_created_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Experience
    this.belongsTo(models.Experience, {
      foreignKey: 'experienceId',
      as: 'experience',
      onDelete: 'CASCADE',
    });

    // Belongs to User (creator)
    this.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'CASCADE',
    });

    // Has many discussion likes
    this.hasMany(models.ExperienceDiscussionLike, {
      foreignKey: 'discussionId',
      as: 'likes',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };

    // Add computed fields
    if (this.likes && Array.isArray(this.likes)) {
      const likesCount = this.likes.length;
      values.likesCount = likesCount;
    }

    return values;
  }
}

module.exports = ExperienceDiscussion;
