/**
 * Payment Method Validation
 * Validation schemas for payment method-related endpoints
 */
const { body, param, query } = require('express-validator');

/**
 * Validation schemas for payment method endpoints
 */
const paymentMethodValidation = {
  /**
   * Validate add payment method request
   */
  add: [
    body('paymentMethodId')
      .isString()
      .withMessage('Payment method ID is required'),
    body('isDefault')
      .optional()
      .isBoolean()
      .withMessage('isDefault must be a boolean'),
    body('billingAddressStreet')
      .optional()
      .isString()
      .withMessage('Billing address street must be a string'),
    body('billingAddressCity')
      .optional()
      .isString()
      .withMessage('Billing address city must be a string'),
    body('billingAddressZipCode')
      .optional()
      .isString()
      .withMessage('Billing address zip code must be a string'),
  ],

  /**
   * Validate remove payment method request
   */
  remove: [param('id').isUUID(4).withMessage('Invalid payment method ID')],

  /**
   * Validate set default payment method request
   */
  setDefault: [param('id').isUUID(4).withMessage('Invalid payment method ID')],

  /**
   * Validate get payment methods request
   */
  getPaymentMethods: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Limit must be a positive integer'),
  ],
};

module.exports = paymentMethodValidation;
