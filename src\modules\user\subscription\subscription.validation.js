/**
 * Subscription Validation
 *
 * Validation schemas for subscription-related endpoints
 */
const { body, param, query } = require('express-validator');
const { UserType } = require('@utils/enums.utils');

/**
 * Subscription validation schemas
 */
const subscriptionValidation = {
  /**
   * Create subscription validation schema
   */
  createSubscription: [
    body('planId')
      .isUUID()
      .withMessage('planId must be a valid UUID')
      .notEmpty()
      .withMessage('planId is required'),
    body('paymentMethodId')
      .optional()
      .isString()
      .withMessage('paymentMethodId must be a string'),
    body('currentStep').optional().isInt(),
  ],

  /**
   * Validate get all subscriptions request
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Limit must be a positive integer')
      .toInt(),
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
    query('targetUserType')
      .optional()
      .isIn([UserType.EDUCATOR, UserType.PROVIDER])
      .withMessage('Target user type must be either EDUCATOR or PROVIDER'),
  ],

  /**
   * Validate get subscription by ID request
   */
  getById: [
    param('subscriptionId')
      .isUUID()
      .withMessage('subscriptionId must be a valid UUID')
      .notEmpty()
      .withMessage('subscriptionId is required'),
  ],

  /**
   * Update payment method validation schema
   */
  updatePaymentMethod: [
    param('subscriptionId')
      .isUUID()
      .withMessage('subscriptionId must be a valid UUID')
      .notEmpty()
      .withMessage('subscriptionId is required'),
    body('paymentMethodId')
      .isString()
      .withMessage('paymentMethodId must be a string')
      .notEmpty()
      .withMessage('paymentMethodId is required'),
  ],

  /**
   * Pause subscription validation schema
   */
  pauseSubscription: [
    param('subscriptionId')
      .isUUID()
      .withMessage('subscriptionId must be a valid UUID')
      .notEmpty()
      .withMessage('subscriptionId is required'),
  ],

  /**
   * Resume subscription validation schema
   */
  resumeSubscription: [
    param('subscriptionId')
      .isUUID()
      .withMessage('subscriptionId must be a valid UUID')
      .notEmpty()
      .withMessage('subscriptionId is required'),
  ],

  /**
   * Cancel subscription validation schema
   */
  cancelSubscription: [
    param('subscriptionId')
      .isUUID()
      .withMessage('subscriptionId must be a valid UUID')
      .notEmpty()
      .withMessage('subscriptionId is required'),
  ],

  /**
   * Upgrade subscription validation schema
   */
  upgradeSubscription: [
    param('subscriptionId')
      .isUUID()
      .withMessage('subscriptionId must be a valid UUID')
      .notEmpty()
      .withMessage('subscriptionId is required'),
    body('planId')
      .isUUID()
      .withMessage('planId must be a valid UUID')
      .notEmpty()
      .withMessage('planId is required'),
    body('paymentMethodId')
      .optional()
      .isString()
      .withMessage('paymentMethodId must be a string'),
  ],
};

module.exports = subscriptionValidation;
