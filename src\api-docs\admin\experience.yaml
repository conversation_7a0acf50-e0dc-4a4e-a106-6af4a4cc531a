openapi: 3.0.0
info:
  title: WTD Platform Admin Experience API
  version: 1.0.0
  description: API endpoints for managing experiences as an admin

paths:
  /admin/experience:
    get:
      tags:
        - Admin Experience
      summary: Get All Experiences
      description: Get a list of all experiences with optional filtering
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/OptionalUserIdParam'
      responses:
        '200':
          description: Successfully retrieved experiences
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceListResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /admin/experience/{experienceId}:
    get:
      tags:
        - Admin Experience
      summary: Get experience details
      description: Get complete experience details with all associations
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      responses:
        '200':
          description: Experience details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - Admin Experience
      summary: Update Experience
      description: Update an existing experience with new data
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExperienceUpdateRequest'
      responses:
        '200':
          description: Successfully updated experience
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceResponse'
        '400':
          description: Invalid request data
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Experience not found

    delete:
      tags:
        - Admin Experience
      summary: Delete Experience
      description: Delete an experience and all its related data (insights, discussions, reviews, etc.)
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      responses:
        '200':
          description: Successfully deleted experience
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceDeleteResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/experience/{experienceId}/discussions:
    get:
      tags:
        - Admin Experience
      summary: Get Experience Discussions
      description: Get all discussions associated with a specific experience
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved discussions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscussionListResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/experience/{experienceId}/reviews:
    get:
      tags:
        - Admin Experience
      summary: Get Experience Reviews
      description: Get all reviews associated with a specific experience
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved reviews
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReviewListResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/experience/week/{weekId}:
    get:
      tags:
        - Admin Experience
      summary: Get Week Details
      description: Get detailed information about a specific week including weeklyWhy, insights, and engagement statistics
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/WeekIdParam'
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Week details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeekDetailsResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Week not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/experience/{experienceId}/status:
    patch:
      tags:
        - Admin Experience
      summary: Update Experience Status
      description: Update the status of an experience and cascade the status update to all linked insights
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExperienceStatusUpdateRequest'
      responses:
        '200':
          description: Successfully updated experience status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceResponse'
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Experience not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    ExperienceIdParam:
      name: experienceId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Experience ID

    WeekIdParam:
      name: weekId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Week ID

    OptionalUserIdParam:
      name: userId
      in: query
      schema:
        type: string
        format: uuid
      description: Filter experiences by a specific user's ID (optional)

  schemas:
    DiscussionListResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Discussions retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Discussion'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ReviewListResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Reviews retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Review'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Discussion:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        content:
          type: string
          example: "This is a discussion post about the experience"
        userId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        experienceId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          example: "2024-03-20T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-03-20T10:00:00Z"

    Review:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        rating:
          type: integer
          minimum: 1
          maximum: 5
          example: 4
        comment:
          type: string
          example: "Great learning experience!"
        userId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        experienceId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          example: "2024-03-20T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-03-20T10:00:00Z"

    WeekDetailsResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Week details retrieved successfully"
        data:
          type: object
          properties:
            weekInfo:
              $ref: '#/components/schemas/ExperienceWeek'
            insights:
              type: array
              items:
                $ref: '#/components/schemas/ExperienceInsight'
            engagementStats:
              type: object
              properties:
                peerInteractions:
                  type: integer
                  description: Total number of peer interactions
                  example: 25
                insightContributions:
                  type: integer
                  description: Number of insight contributions
                  example: 10
                completionRate:
                  type: number
                  description: Completion rate percentage
                  example: 75.5
                videosWatched:
                  type: integer
                  description: Number of videos watched
                  example: 15
                discussionPostContributions:
                  type: integer
                  description: Number of discussion posts
                  example: 5
        pagination:
          $ref: '#/components/schemas/Pagination'

    ExperienceUpdateRequest:
      type: object
      example:
        title: "Learning React Framework"
        shortDescription: "A comprehensive React learning experience"
        longDescription: "This experience covers all fundamentals of React development, including components, state, and lifecycle methods."
        experienceLength: 4
        personalNote: "Built for frontend bootcamps"
        pdCategoryIds: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
        wtdCategoryIds: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]
        media:
          - type: "VIDEO"
            url: "https://example.com/intro.mp4"
            title: "Intro to React"
        weeks:
          - weekNumber: 1
            title: "JSX & Components"
            weeklyWhy: "To understand UI structure in React"
            media:
              - type: "DOCUMENT"
                url: "https://example.com/week1-notes.pdf"
                title: "Week 1 Notes"
            insights:
              - text: "JSX combines HTML with JavaScript logic"
                sourceUrl: "https://reactjs.org/docs/introducing-jsx.html"
                focusIds: ["29c7c9cc-0d6f-493f-9f02-5a04f1e99999"]
                pdCategoryIds: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
                wtdCategoryIds: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]
      properties:
        title:
          type: string
          description: Experience title
          example: "Learning React Framework"
        shortDescription:
          type: string
          description: Brief description of the experience
          example: "A comprehensive React learning experience"
        longDescription:
          type: string
          description: Detailed description of the experience
          example: "This experience covers all fundamentals of React development, including components, state, and lifecycle methods."
        experienceLength:
          type: integer
          minimum: 1
          maximum: 52
          description: Duration in weeks
          example: 4
        personalNote:
          type: string
          description: Creator's personal notes
          example: "Built for frontend bootcamps"
        media:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
                description: Type of media
                example: "VIDEO"
              url:
                type: string
                format: uri
                description: Media URL
                example: "https://example.com/intro.mp4"
              title:
                type: string
                description: Media title
                example: "Intro to React"
          description: Experience-level media
        pdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: IDs of associated PD categories
          example: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
        wtdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: IDs of associated WTD categories
          example: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]
        weeks:
          type: array
          items:
            type: object
            properties:
              weekNumber:
                type: integer
                minimum: 1
                description: Week number
                example: 1
              title:
                type: string
                description: Week title
                example: "JSX & Components"
              weeklyWhy:
                type: string
                description: Purpose of the week
                example: "To understand UI structure in React"
              media:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
                      enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
                      description: Type of media
                      example: "DOCUMENT"
                    url:
                      type: string
                      format: uri
                      description: Media URL
                      example: "https://example.com/week1-notes.pdf"
                    title:
                      type: string
                      description: Media title
                      example: "Week 1 Notes"
                description: Week-specific media
              insights:
                type: array
                items:
                  type: object
                  properties:
                    text:
                      type: string
                      description: Insight text
                      example: "JSX combines HTML with JavaScript logic"
                    sourceUrl:
                      type: string
                      format: uri
                      description: Source URL for the insight
                      example: "https://reactjs.org/docs/introducing-jsx.html"
                    focusIds:
                      type: array
                      items:
                        type: string
                        format: uuid
                      description: IDs of associated focus areas
                      example: ["29c7c9cc-0d6f-493f-9f02-5a04f1e99999"]
                    pdCategoryIds:
                      type: array
                      items:
                        type: string
                        format: uuid
                      description: IDs of associated PD categories
                      example: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
                    wtdCategoryIds:
                      type: array
                      items:
                        type: string
                        format: uuid
                      description: IDs of associated WTD categories
                      example: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]
                description: Week-specific insights
            required:
              - weekNumber
              - title
              - weeklyWhy
          description: Weekly breakdown of the experience

    ExperienceWeekUpdate:
      type: object
      properties:
        weekNumber:
          type: integer
          minimum: 1
          description: Week number
        title:
          type: string
          description: Week title
        weeklyWhy:
          type: string
          description: Purpose/rationale for the week
        media:
          type: array
          items:
            $ref: '#/components/schemas/WeekMedia'
          description: Week-specific media
        insights:
          type: array
          items:
            $ref: '#/components/schemas/ExperienceInsight'
          description: Weekly insights

    ExperienceListResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "All experiences retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Experience'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ExperienceResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Experience retrieved successfully"
        data:
          $ref: '#/components/schemas/Experience'

    Experience:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the experience
        title:
          type: string
          description: Experience title
        shortDescription:
          type: string
          description: Brief description of the experience
        longDescription:
          type: string
          description: Detailed description of the experience
        experienceLength:
          type: integer
          description: Duration in weeks
        personalNote:
          type: string
          description: Creator's personal notes
        createdBy:
          type: string
          format: uuid
          description: ID of the user who created the experience
        creator:
          $ref: '#/components/schemas/UserRef'
        media:
          type: array
          items:
            $ref: '#/components/schemas/Media'
          description: Experience-level media
        pdCategories:
          type: array
          items:
            $ref: '#/components/schemas/PdCategoryRef'
          description: Associated PD categories
        wtdCategories:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategoryRef'
          description: Associated WTD categories
        weeks:
          type: array
          items:
            $ref: '#/components/schemas/ExperienceWeek'
          description: Weekly breakdown of the experience
        createdAt:
          type: string
          format: date-time
          description: Date and time when the experience was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the experience was last updated

    ExperienceWeek:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the week
        experienceId:
          type: string
          format: uuid
          description: ID of the parent experience
        weekNumber:
          type: integer
          description: Week number
        title:
          type: string
          description: Week title
        weeklyWhy:
          type: string
          description: Purpose/rationale for the week
        media:
          type: array
          items:
            $ref: '#/components/schemas/WeekMedia'
          description: Week-specific media
        insights:
          type: array
          items:
            $ref: '#/components/schemas/ExperienceInsight'
          description: Weekly insights
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Media:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the media
        experienceId:
          type: string
          format: uuid
          description: ID of the parent experience
        type:
          type: string
          enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
          description: Type of media
        url:
          type: string
          format: uri
          description: Media URL
        title:
          type: string
          description: Media title
        order:
          type: integer
          description: Display order
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    WeekMedia:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the week media
        experienceWeekId:
          type: string
          format: uuid
          description: ID of the parent week
        type:
          type: string
          enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
          description: Type of media
        url:
          type: string
          format: uri
          description: Media URL
        title:
          type: string
          description: Media title
        order:
          type: integer
          description: Display order
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ExperienceInsight:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the insight
        experienceWeekId:
          type: string
          format: uuid
          description: ID of the parent week
        order:
          type: integer
          description: Display order within the week
        text:
          type: string
          description: Insight content
        sourceUrl:
          type: string
          format: uri
          description: Optional source URL
        focuses:
          type: array
          items:
            $ref: '#/components/schemas/FocusRef'
          description: Associated focus areas
        pdCategories:
          type: array
          items:
            $ref: '#/components/schemas/PdCategoryRef'
          description: Associated PD categories
        wtdCategories:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategoryRef'
          description: Associated WTD categories
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UserRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email

    PdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    WtdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    FocusRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    Pagination:
      type: object
      properties:
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 1
        totalItems:
          type: integer
          description: Total number of items
          example: 3
        itemsPerPage:
          type: integer
          description: Number of items per page
          example: 10
        hasNextPage:
          type: boolean
          description: Whether there is a next page
          example: false
        hasPrevPage:
          type: boolean
          description: Whether there is a previous page
          example: false

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: "Error message"
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                description: Field name that caused the error
              message:
                type: string
                description: Error message for the field

    ExperienceStatusUpdateRequest:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [APPROVED, REJECTED]
          description: New status for the experience

    ExperienceDeleteResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: "Experience deleted successfully"
