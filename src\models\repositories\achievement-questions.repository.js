/**
 * Achievement Questions Repository
 *
 * Handles data access operations for achievement questions and responses
 */
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const databaseService = require('@config/database.config');
const { IMPACT, VALIDATION } = require('@utils/messages.utils');

class AchievementQuestionsRepository {
  constructor() {
    this.models = {
      Achievement: databaseService.getAchievementModel(),
      AchievementQuestion: databaseService.getAchievementQuestionModel(),
      AchievementQuestionOption:
        databaseService.getAchievementQuestionOptionModel(),
      UserAchievementResponse:
        databaseService.getUserAchievementResponseModel(),
    };
  }

  /**
   * Get achievement questions with options
   * @param {string} achievementId
   * @returns {Promise<Array>}
   */
  async getAchievementQuestions(achievementId) {
    try {
      const achievement = await this.models.Achievement.findOne({
        where: { id: achievementId, isActive: true },
      });
      if (!achievement) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          IMPACT.ACHIEVEMENT_NOT_FOUND
        );
      }
      const questions = await this.models.AchievementQuestion.findAll({
        where: { achievementId, isActive: true },
        include: [
          {
            model: this.models.AchievementQuestionOption,
            as: 'options',
            required: false,
          },
        ],
        order: [
          ['order', 'ASC'],
          [
            { model: this.models.AchievementQuestionOption, as: 'options' },
            'order',
            'ASC',
          ],
        ],
      });
      return questions;
    } catch (error) {
      if (error instanceof ApiException) throw error;
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Check if achievement has questions
   * @param {string} achievementId
   * @returns {Promise<boolean>}
   */
  async hasAchievementQuestions(achievementId) {
    try {
      const count = await this.models.AchievementQuestion.count({
        where: { achievementId, isActive: true },
      });
      return count > 0;
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        IMPACT.RETRIEVAL_FAILED
      );
    }
  }

  /**
   * Submit achievement question responses
   * @param {string} userId
   * @param {string} achievementId
   * @param {Array<Object>} answers
   * @returns {Promise<Object>}
   */
  async submitAchievementResponses(userId, achievementId, answers) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Get all questions for this achievement
      const questions = await this.models.AchievementQuestion.findAll({
        where: { achievementId, isActive: true },
        attributes: ['id', 'isRequired'],
      });
      if (!questions.length) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          IMPACT.ACHIEVEMENT_QUESTIONS_NOT_FOUND
        );
      }

      // Remove existing responses
      await this.models.UserAchievementResponse.destroy({
        where: {
          userId,
          questionId: questions.map((q) => q.id),
        },
        transaction,
      });

      // Validate all required questions are answered
      const requiredQuestions = questions.filter((q) => q.isRequired);
      const answeredQuestionIds = answers.map((a) => a.questionId);
      const missingQuestions = requiredQuestions.filter(
        (q) => !answeredQuestionIds.includes(q.id)
      );
      if (missingQuestions.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          IMPACT.MISSING_REQUIRED_ACHIEVEMENT_QUESTIONS(
            missingQuestions.map((q) => q.id)
          )
        );
      }

      // Prepare and insert responses
      const responseData = answers.map((answer) => ({
        userId,
        questionId: answer.questionId,
        response: answer.response,
        selectedOptionId: answer.selectedOptionId || null,
      }));

      await this.models.UserAchievementResponse.bulkCreate(responseData, {
        transaction,
      });

      await transaction.commit();
      return {
        message: IMPACT.QUESTIONS_SUBMITTED,
        achievementId,
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

module.exports = new AchievementQuestionsRepository();
