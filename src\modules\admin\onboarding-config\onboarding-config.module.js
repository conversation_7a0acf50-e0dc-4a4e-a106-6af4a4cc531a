/**
 * OnboardingConfig Module
 *
 * This module handles onboarding config-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const paginationMiddleware = require('@middlewares/pagination.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const onboardingConfigController = require('./onboarding-config.controller');
const onboardingConfigValidation = require('./onboarding-config.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all configs
  router.get(
    '/',
    authenticate,
    validate(onboardingConfigValidation.getAll),
    paginationMiddleware,
    onboardingConfigController.getAllConfigs
  );

  // Get config by ID
  router.get(
    '/:id',
    authenticate,
    validate(onboardingConfigValidation.getById),
    onboardingConfigController.getConfigById
  );

  // Create config
  router.post(
    '/',
    authenticate,
    validate(onboardingConfigValidation.create),
    onboardingConfigController.createConfig
  );

  // Update config
  router.put(
    '/:id',
    authenticate,
    validate(onboardingConfigValidation.update),
    onboardingConfigController.updateConfig
  );

  // Delete config
  router.delete(
    '/:id',
    authenticate,
    validate(onboardingConfigValidation.delete),
    onboardingConfigController.deleteConfig
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
