/**
 * OnboardingConfig Service
 *
 * Handles onboarding config-related business logic
 */
const onboardingConfigRepository = require('@models/repositories/onboarding-config.repository');

/**
 * OnboardingConfig service
 */
const onboardingConfigService = {
  /**
   * Get all onboarding configs with pagination
   * @param {Object} query - Request query parameters
   * @param {Object} pagination - Pagination parameters
   * @returns {Promise<Object>} Onboarding configs and pagination info
   */
  getAllConfigs: async (query, pagination) => {
    return await onboardingConfigRepository.fetchOnboardingConfigs(
      query,
      pagination
    );
  },

  /**
   * Get onboarding config by ID
   * @param {string} id - Onboarding config ID
   * @returns {Promise<Object>} Onboarding config
   */
  getConfigById: async (id) => {
    return await onboardingConfigRepository.findById(id);
  },

  /**
   * Create a new onboarding config
   * @param {Object} data - Onboarding config data
   * @returns {Promise<Object>} Created onboarding config
   */
  createConfig: async (data) => {
    return await onboardingConfigRepository.create(data);
  },

  /**
   * Update onboarding config
   * @param {string} id - Onboarding config ID
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated onboarding config
   */
  updateConfig: async (id, data) => {
    return await onboardingConfigRepository.update(id, data);
  },

  /**
   * Delete onboarding config
   * @param {string} id - Onboarding config ID
   * @returns {Promise<boolean>} True if deleted
   */
  deleteConfig: async (id) => {
    return await onboardingConfigRepository.delete(id);
  },
};

module.exports = onboardingConfigService;
