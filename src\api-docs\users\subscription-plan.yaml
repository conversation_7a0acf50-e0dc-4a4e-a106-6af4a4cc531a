openapi: 3.0.0
info:
  title: WTD Platform User Subscription Plans API
  version: 1.0.0
  description: Public endpoints for listing and viewing subscription plans

paths:
  /user/subscription-plans:
    get:
      tags:
        - Subscription Plans
      summary: Get all subscription plans
      description: Retrieve all available subscription plans (public)
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SubscriptionPlanSearchParam'
        - $ref: '#/components/parameters/SubscriptionPlanTargetUserTypeParam'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPlansList'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/subscription-plans/{id}:
    get:
      tags:
        - Subscription Plans
      summary: Get subscription plan by ID
      description: Retrieve a specific subscription plan by its ID (public)
      parameters:
        - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          $ref: '#/components/responses/SubscriptionPlanDetail'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  parameters:
    PageParam:
      in: query
      name: page
      schema:
        type: integer
        minimum: 1
      description: Page number for pagination
    LimitParam:
      in: query
      name: limit
      schema:
        type: integer
        minimum: 1
        maximum: 100
      description: Number of items per page
    SubscriptionPlanSearchParam:
      in: query
      name: search
      schema:
        type: string
      description: Search term for plan name or description
    SubscriptionPlanTargetUserTypeParam:
      in: query
      name: targetUserType
      schema:
        type: string
        enum: [EDUCATOR, PROVIDER]
      description: Filter by target user type
    IdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Subscription plan ID
  responses:
    SubscriptionPlansList:
      description: List of subscription plans retrieved successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/PaginatedResponse'
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/SubscriptionPlan'
    SubscriptionPlanDetail:
      description: Subscription plan retrieved successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/SuccessResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubscriptionPlan'
    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
  schemas:
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: 'Operation completed successfully'
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: 'An error occurred'
        code:
          type: string
          example: 'ERROR_CODE'
    PaginatedResponse:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
        total:
          type: integer
          example: 100
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 10
        totalPages:
          type: integer
          example: 10
    SubscriptionPlan:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        name:
          type: string
          minLength: 3
          example: 'Educator Free'
        description:
          type: string
          minLength: 10
          example: 'Free forever. View 12 insights per month, no experience registration.'
        stripeProductId:
          type: string
          nullable: true
          example: 'prod_1234567890'
        stripePriceId:
          type: string
          nullable: true
          example: 'price_1234567890'
        price:
          type: number
          example: 0
        priceCents:
          type: integer
          minimum: 0
          example: 0
        billingInterval:
          type: string
          enum: [ONE_TIME, MONTHLY, YEARLY]
          example: ONE_TIME
        targetUserType:
          type: string
          enum: [EDUCATOR, PROVIDER]
          example: EDUCATOR
        trialDays:
          type: integer
          minimum: 0
          nullable: true
          example: null
        canRegisterExperiences:
          type: boolean
          example: false
        insightsLimit:
          type: integer
          minimum: 0
          nullable: true
          example: 12
        insightsLimitPeriod:
          type: string
          enum: [MONTHLY, YEARLY]
          nullable: true
          example: MONTHLY
        isActive:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
          example: '2024-03-20T10:30:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2024-03-20T10:30:00Z'
