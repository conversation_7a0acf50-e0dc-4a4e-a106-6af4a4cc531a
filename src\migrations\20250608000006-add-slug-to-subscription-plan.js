'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('SubscriptionPlan', 'slug', {
      type: Sequelize.STRING,
      allowNull: false,
      unique: true,
      after: 'name',
    });

    // Add index for slug field
    await queryInterface.addIndex('SubscriptionPlan', ['slug'], {
      name: 'subscription_plan_slug_idx',
      unique: true,
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    await queryInterface.removeIndex(
      'SubscriptionPlan',
      'subscription_plan_slug_idx'
    );

    // Remove column
    await queryInterface.removeColumn('SubscriptionPlan', 'slug');
  },
};
