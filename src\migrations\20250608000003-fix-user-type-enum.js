'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // First remove default values
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" DROP DEFAULT;

      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" DROP DEFAULT;
    `);

    // Then convert columns to text type temporarily
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" TYPE text 
      USING "userType"::text;

      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" TYPE text 
      USING "userType"::text;
    `);

    // Now we can safely drop the enum types
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_User_userType";
      DROP TYPE IF EXISTS "enum_Milestone_userType";
    `);

    // Create the new shared enum type with the correct naming structure
    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_User_userType" AS ENUM (
        'EDUCATOR',
        'EDUCATOR_PLUS',
        'PROVIDER',
        'PROVIDER_PLUS'
      );
    `);

    // Convert columns to use the new enum type
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" TYPE "enum_User_userType" 
      USING "userType"::text::"enum_User_userType";

      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" TYPE "enum_User_userType" 
      USING "userType"::text::"enum_User_userType";
    `);

    // Set default values back
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" SET DEFAULT 'EDUCATOR'::"enum_User_userType";

      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" SET DEFAULT 'EDUCATOR'::"enum_User_userType";
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // First remove default values
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" DROP DEFAULT;

      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" DROP DEFAULT;
    `);

    // Then convert columns to text type temporarily
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" TYPE text 
      USING "userType"::text;

      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" TYPE text 
      USING "userType"::text;
    `);

    // Drop the shared enum type
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_User_userType";
    `);

    // Recreate the original enum types
    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_User_userType" AS ENUM (
        'EDUCATOR',
        'EDUCATOR_PLUS',
        'PROVIDER_PLUS'
      );
      
      CREATE TYPE "enum_Milestone_userType" AS ENUM (
        'EDUCATOR',
        'EDUCATOR_PLUS',
        'PROVIDER',
        'PROVIDER_PLUS'
      );
    `);

    // Convert columns back to their original enum types
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" TYPE "enum_User_userType" 
      USING "userType"::text::"enum_User_userType";
      
      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" TYPE "enum_Milestone_userType" 
      USING "userType"::text::"enum_Milestone_userType";
    `);

    // Set default values back
    await queryInterface.sequelize.query(`
      ALTER TABLE "User" 
      ALTER COLUMN "userType" SET DEFAULT 'EDUCATOR'::"enum_User_userType";

      ALTER TABLE "Milestone" 
      ALTER COLUMN "userType" SET DEFAULT 'EDUCATOR'::"enum_Milestone_userType";
    `);
  },
};
