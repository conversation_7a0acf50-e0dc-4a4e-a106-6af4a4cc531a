/**
 * Achievement Service
 * Handles all achievement-related functionality including:
 * - Milestone management
 * - Achievement progress tracking
 * - User achievement initialization
 * - Achievement completion checks
 */

const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { AchievementType, HttpStatus } = require('@utils/enums.utils');
const { IMPACT, USER, COMMON } = require('@utils/messages.utils');
const experienceNotificationService = require('./notification.service');

/**
 * Service for managing user achievements and milestones
 */
class AchievementService {
  constructor() {
    this.models = {
      User: databaseService.getUserModel(),
      UserAchievement: databaseService.getUserAchievementModel(),
      Achievement: databaseService.getAchievementModel(),
      UserMilestone: databaseService.getUserMilestoneModel(),
      Milestone: databaseService.getMilestoneModel(),
    };
  }

  /**
   * Get current milestone for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Current milestone
   */
  async getCurrentMilestone(userId) {
    try {
      const userMilestone = await this.models.UserMilestone.findOne({
        where: { userId, isCurrent: true },
        include: [
          {
            model: this.models.Milestone,
            as: 'milestone',
            required: true,
          },
        ],
      });

      if (!userMilestone) {
        throw new ApiException(HttpStatus.NOT_FOUND, IMPACT.NO_NEXT_MILESTONE);
      }

      return userMilestone;
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        COMMON.INTERNAL_ERROR
      );
    }
  }

  /**
   * Check and update milestone completion
   * @param {string} userId - User ID
   * @param {string} milestoneId - Milestone ID
   * @returns {Promise<Object>} Updated milestone
   */
  async checkAndUpdateMilestone(userId, milestoneId) {
    try {
      const user = await this.models.User.findByPk(userId);
      if (!user) {
        throw new ApiException(HttpStatus.NOT_FOUND, USER.USER_NOT_FOUND);
      }

      // Get all active achievements for this milestone
      const milestoneAchievements = await this.models.Achievement.findAll({
        where: {
          milestoneId,
          userType: user.userType,
          isActive: true,
        },
        attributes: ['id'],
      });

      // Get user's completed achievements for this milestone
      const userAchievements = await this.models.UserAchievement.findAll({
        where: {
          userId,
          achievementId: milestoneAchievements.map((a) => a.id),
        },
        attributes: ['achievementId', 'isCompleted'],
      });

      // Check if all achievements are completed
      const allCompleted =
        milestoneAchievements.length > 0 &&
        userAchievements.filter((ua) => ua.isCompleted).length ===
          milestoneAchievements.length;

      if (allCompleted) {
        // Get current milestone
        const currentMilestone = await this.models.UserMilestone.findOne({
          where: {
            userId,
            milestoneId,
            isCurrent: true,
          },
          include: [
            {
              model: this.models.Milestone,
              as: 'milestone',
              required: true,
            },
          ],
        });

        if (currentMilestone && currentMilestone.milestone) {
          // Set milestone progress to 100%
          await currentMilestone.update({ progress: 100 });
          // Send milestone completed notification
          await experienceNotificationService.notifyMilestoneCompleted({
            user,
            milestone: currentMilestone.milestone,
          });
          // // Get next milestone
          // const nextMilestone = await this.models.Milestone.findOne({
          //   where: {
          //     userType: user.userType,
          //     order: currentMilestone.milestone.order + 1,
          //     isActive: true,
          //   },
          // });

          // if (nextMilestone) {
          //   // Update current milestone
          //   await this.models.UserMilestone.update(
          //     { isCurrent: false },
          //     {
          //       where: {
          //         userId,
          //         milestoneId,
          //       },
          //     }
          //   );

          // // Create next milestone
          // await this.models.UserMilestone.create({
          //   userId,
          //   milestoneId: nextMilestone.id,
          //   isCurrent: true,
          //   startedAt: new Date(),
          // });
          // }
        }
      }
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        COMMON.INTERNAL_ERROR
      );
    }
  }

  /**
   * Get achievements for current milestone
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Achievements
   */
  async getCurrentMilestoneAchievements(userId) {
    try {
      const currentMilestone = await this.getCurrentMilestone(userId);
      const user = await this.models.User.findByPk(userId);

      if (!user) {
        throw new ApiException(HttpStatus.NOT_FOUND, USER.USER_NOT_FOUND);
      }

      const achievements = await this.models.Achievement.findAll({
        where: {
          milestoneId: currentMilestone.milestoneId,
          userType: user.userType,
          isActive: true,
        },
        include: [
          {
            model: this.models.UserAchievement,
            where: { userId },
            required: false,
          },
        ],
      });

      return achievements.map((achievement) => ({
        id: achievement.id,
        name: achievement.name,
        description: achievement.description,
        isCompleted: achievement.UserAchievements?.[0]?.isCompleted || false,
        targetValue: achievement.targetValue,
        currentValue: achievement.UserAchievements?.[0]?.currentValue || 0,
        progress: achievement.UserAchievements?.[0]
          ? Math.round(
              (achievement.UserAchievements[0].currentValue /
                achievement.targetValue) *
                100
            )
          : 0,
        completedAt: achievement.UserAchievements?.[0]?.completedAt,
      }));
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        COMMON.INTERNAL_ERROR
      );
    }
  }

  /**
   * Update achievement progress
   * @param {string} userId - User ID
   * @param {string} achievementType - Type of achievement from AchievementType enum
   * @param {number} [increment=1] - Amount to increment progress
   * @returns {Promise<Object>} Updated achievement
   */
  async updateAchievementProgress(userId, achievementType, increment = 1) {
    try {
      const user = await this.models.User.findByPk(userId);
      if (!user) {
        throw new ApiException(HttpStatus.NOT_FOUND, USER.USER_NOT_FOUND);
      }

      // Skip CREATE_ACCOUNT as it's handled during initialization
      if (achievementType === AchievementType.CREATE_ACCOUNT) {
        return null;
      }

      const currentMilestone = await this.getCurrentMilestone(userId);

      // First try to find the exact achievement type in current milestone
      let achievement = await this.models.Achievement.findOne({
        where: {
          achievementType,
          milestoneId: currentMilestone.milestoneId,
          userType: user.userType,
          isActive: true,
        },
      });

      // If not found and it's a numbered variation, try to find the base type
      if (
        !achievement &&
        AchievementType.isNumberedVariation(achievementType)
      ) {
        const baseType = AchievementType.getBaseType(achievementType);
        // logger.info(`Checking for base achievement type: ${baseType}`);

        achievement = await this.models.Achievement.findOne({
          where: {
            achievementType: baseType,
            milestoneId: currentMilestone.milestoneId,
            userType: user.userType,
            isActive: true,
          },
        });
      }

      // If still not found, check if there's a higher numbered variation that should be completed first
      if (!achievement) {
        const allMilestoneAchievements = await this.models.Achievement.findAll({
          where: {
            milestoneId: currentMilestone.milestoneId,
            userType: user.userType,
            isActive: true,
          },
        });

        // Find the highest numbered variation of this achievement type
        const numberedAchievements = allMilestoneAchievements
          .filter(
            (a) =>
              AchievementType.getBaseType(a.achievementType) ===
              AchievementType.getBaseType(achievementType)
          )
          .sort((a, b) => {
            const numA = parseInt(a.achievementType.split('_').pop()) || 0;
            const numB = parseInt(b.achievementType.split('_').pop()) || 0;
            return numA - numB;
          });

        // Get the lowest numbered achievement
        if (numberedAchievements.length > 0) {
          achievement = numberedAchievements[0];
        }
      }

      if (!achievement) {
        return null;
      }

      let userAchievement = await this.models.UserAchievement.findOne({
        where: {
          userId,
          achievementId: achievement.id,
        },
      });

      if (!userAchievement) {
        userAchievement = await this.models.UserAchievement.create({
          userId,
          achievementId: achievement.id,
          currentValue: 0,
          isCompleted: false,
        });
      }

      if (userAchievement.isCompleted) {
        return userAchievement;
      }

      userAchievement.currentValue += increment;
      userAchievement.isCompleted =
        userAchievement.currentValue >= achievement.targetValue;

      if (userAchievement.isCompleted) {
        userAchievement.completedAt = new Date();
      }

      await userAchievement.save();

      if (userAchievement.isCompleted) {
        await this.checkAndUpdateMilestone(
          userId,
          currentMilestone.milestoneId
        );
      }

      return userAchievement;
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        COMMON.INTERNAL_ERROR
      );
    }
  }

  /**
   * Get all achievements for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} User achievements
   */
  async getUserAchievements(userId) {
    try {
      const user = await this.models.User.findByPk(userId);
      if (!user) {
        throw new ApiException(HttpStatus.NOT_FOUND, USER.USER_NOT_FOUND);
      }

      const achievements = await this.models.Achievement.findAll({
        where: {
          userType: user.userType,
          isActive: true,
        },
        include: [
          {
            model: this.models.UserAchievement,
            where: { userId },
            required: false,
          },
        ],
      });

      return achievements.map((achievement) => ({
        id: achievement.id,
        name: achievement.name,
        description: achievement.description,
        isCompleted: achievement.UserAchievements?.[0]?.isCompleted || false,
        targetValue: achievement.targetValue,
        currentValue: achievement.UserAchievements?.[0]?.currentValue || 0,
        progress: achievement.UserAchievements?.[0]
          ? Math.round(
              (achievement.UserAchievements[0].currentValue /
                achievement.targetValue) *
                100
            )
          : 0,
        completedAt: achievement.UserAchievements?.[0]?.completedAt,
      }));
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        COMMON.INTERNAL_ERROR
      );
    }
  }

  /**
   * Initialize user achievements
   * @param {string} userId - User ID
   */
  async initializeUserAchievements(userId) {
    try {
      const user = await this.models.User.findByPk(userId);
      if (!user) {
        throw new ApiException(HttpStatus.NOT_FOUND, USER.USER_NOT_FOUND);
      }

      // Get first milestone for user type
      const firstMilestone = await this.models.Milestone.findOne({
        where: {
          userType: user.userType,
          order: 1,
          isActive: true,
        },
      });

      if (!firstMilestone) {
        throw new ApiException(HttpStatus.NOT_FOUND, IMPACT.NO_NEXT_MILESTONE);
      }

      // Create user milestone
      await this.models.UserMilestone.create({
        userId,
        milestoneId: firstMilestone.id,
        isCurrent: true,
        startedAt: new Date(),
      });

      // Get achievements for first milestone
      const achievements = await this.models.Achievement.findAll({
        where: {
          milestoneId: firstMilestone.id,
          userType: user.userType,
          isActive: true,
        },
      });

      // Create user achievements
      for (const achievement of achievements) {
        if (achievement.achievementType === AchievementType.CREATE_ACCOUNT) {
          await this.models.UserAchievement.create({
            userId,
            achievementId: achievement.id,
            currentValue: achievement.targetValue,
            isCompleted: true,
            completedAt: new Date(),
          });
        } else {
          await this.models.UserAchievement.create({
            userId,
            achievementId: achievement.id,
            currentValue: 0,
            isCompleted: false,
          });
        }
      }

      // Check if first milestone should be completed (in case CREATE_ACCOUNT was the only achievement)
      await this.checkAndUpdateMilestone(userId, firstMilestone.id);
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        COMMON.INTERNAL_ERROR
      );
    }
  }
}

module.exports = new AchievementService();
