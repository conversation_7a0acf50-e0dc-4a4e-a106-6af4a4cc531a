/**
 * Authentication and Authorization Middleware
 */
const jwt = require('jsonwebtoken');
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { UserRole, HttpStatus } = require('@utils/enums.utils');
const { AUTH } = require('@utils/messages.utils');

/**
 * Admin authenticate middleware
 * Verifies JW<PERSON> token and attaches admin to request
 */
const authenticateAdmin = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith('Bearer ')) {
      throw new ApiException(
        HttpStatus.UNAUTHORIZED,
        AUTH.AUTHENTICATION_FAILED
      );
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    try {
      const Admin = databaseService.getAdminModel();
      const admin = await Admin.findByPk(decoded.id);

      if (!admin) {
        throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN);
      }

      if (
        decoded.tokenVersion === undefined ||
        admin.tokenVersion !== decoded.tokenVersion
      ) {
        throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN);
      }

      // Use the role from the decoded token
      req.user = {
        id: admin.id,
        email: admin.email,
        role: decoded.role || UserRole.ADMIN, // Fallback to admin role if not in token
      };
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.UNAUTHORIZED,
        AUTH.AUTHENTICATION_FAILED
      );
    }

    next();
  } catch (error) {
    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError'
    ) {
      return next(
        new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN)
      );
    }
    next(error);
  }
};

/**
 * User authenticate middleware
 * Verifies JWT token and attaches user to request
 */
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith('Bearer ')) {
      throw new ApiException(
        HttpStatus.UNAUTHORIZED,
        AUTH.AUTHENTICATION_FAILED
      );
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    try {
      const User = databaseService.getUserModel();
      const user = await User.findByPk(decoded.id);

      if (!user) {
        throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN);
      }

      if (
        decoded.tokenVersion === undefined ||
        user.tokenVersion !== decoded.tokenVersion
      ) {
        throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN);
      }

      // Attach user to request
      req.user = {
        id: user.id,
        email: user.email,
        userType: user.userType,
        role: UserRole.USER,
        firstName: user.firstName,
        lastName: user.lastName,
        stripeCustomerId: user.stripeCustomerId,
      };
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.UNAUTHORIZED,
        AUTH.AUTHENTICATION_FAILED
      );
    }

    next();
  } catch (error) {
    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError'
    ) {
      return next(
        new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN)
      );
    }
    next(error);
  }
};

/**
 * Generic authenticate middleware
 * Determines the type of user from the token and routes to the appropriate authentication middleware
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith('Bearer ')) {
      throw new ApiException(
        HttpStatus.UNAUTHORIZED,
        AUTH.AUTHENTICATION_FAILED
      );
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Determine the path type
    let pathType;
    if (req.originalUrl.startsWith('/admin')) {
      pathType = 'admin';
    } else if (req.originalUrl.startsWith('/user')) {
      pathType = 'user';
    } else if (req.originalUrl.startsWith('/upload')) {
      pathType = 'upload';
    }

    // Handle authentication based on path type and role
    switch (pathType) {
      case 'upload':
        // For upload paths, both admin and user roles are allowed
        switch (decoded.role) {
          case UserRole.ADMIN:
            return authenticateAdmin(req, res, next);
          case UserRole.USER:
            return authenticateUser(req, res, next);
          default:
            throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.UNAUTHORIZED);
        }

      case 'admin':
        if (decoded.role !== UserRole.ADMIN) {
          throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.UNAUTHORIZED);
        }
        return authenticateAdmin(req, res, next);

      case 'user':
        if (decoded.role !== UserRole.USER) {
          throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.UNAUTHORIZED);
        }
        return authenticateUser(req, res, next);

      default:
        // For any other paths, use role-based authentication
        if (decoded.role === UserRole.ADMIN) {
          return authenticateAdmin(req, res, next);
        } else {
          return authenticateUser(req, res, next);
        }
    }
  } catch (error) {
    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError'
    ) {
      return next(
        new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN)
      );
    }
    next(error);
  }
};

/**
 * Authorize middleware
 * Checks if user has required role
 * @param {Array} roles - Array of allowed roles
 */
const authorize = (roles = []) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new ApiException(
          HttpStatus.UNAUTHORIZED,
          AUTH.AUTHENTICATION_FAILED
        );
      }

      // Convert string role to array if needed
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      // If no roles specified, allow all authenticated users
      if (allowedRoles.length === 0) {
        return next();
      }

      // Check if user's role is in the allowed roles
      if (!allowedRoles.includes(req.user.role)) {
        throw new ApiException(HttpStatus.FORBIDDEN, AUTH.FORBIDDEN);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  authenticate,
  authenticateAdmin,
  authenticateUser,
  authorize,
};
