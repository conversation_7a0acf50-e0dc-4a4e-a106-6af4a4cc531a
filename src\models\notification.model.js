const { Model, DataTypes } = require('sequelize');

class Notification extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        receiverUserId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        type: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        title: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        body: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        data: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        isRead: {
          type: DataTypes.BOOLEAN,
          allowNull: true,
          defaultValue: false,
        },
      },
      {
        sequelize,
        modelName: 'Notification',
        tableName: 'Notification',
        timestamps: true,
        indexes: [
          {
            fields: ['userId'],
            name: 'notification_user_id_idx',
          },
          {
            fields: ['isRead'],
            name: 'notification_is_read_idx',
          },
          {
            fields: ['receiverUserId'],
            name: 'notification_receiver_user_id_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    Notification.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
    Notification.belongsTo(models.User, {
      foreignKey: 'receiverUserId',
      as: 'receiver',
      onDelete: 'CASCADE',
    });
  }

  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = Notification;
