/**
 * User Subscription Controller
 *
 * Handles HTTP requests related to user subscriptions
 */
const subscriptionService = require('./subscription.service');
const { ApiResponse } = require('@utils/response.utils');
const { SUBSCRIPTION } = require('@utils/messages.utils');

/**
 * Subscription controller
 */
const subscriptionController = {
  /**
   * Create a new subscription
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  createSubscription: async (req, res, next) => {
    try {
      const subscription = await subscriptionService.createSubscription(
        req.user,
        req.body
      );

      return ApiResponse.created(res, SUBSCRIPTION.CREATED, subscription);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all subscriptions for a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getUserSubscriptions: async (req, res, next) => {
    try {
      const user = req.user;
      const result = await subscriptionService.getUserSubscriptions(
        user.id,
        req.pagination
      );
      return ApiResponse.success(
        res,
        SUBSCRIPTION.RETRIEVED,
        result.subscriptions,
        {
          pagination: result.pagination,
        }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get active subscription for a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getActiveSubscription: async (req, res, next) => {
    try {
      const user = req.user;
      const subscription = await subscriptionService.getActiveSubscription(
        user.id
      );
      return ApiResponse.success(
        res,
        SUBSCRIPTION.ACTIVE_RETRIEVED,
        subscription || {}
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update payment method for a subscription
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updatePaymentMethod: async (req, res, next) => {
    try {
      const { subscriptionId } = req.params;
      const { paymentMethodId } = req.body;

      const subscription = await subscriptionService.updatePaymentMethod(
        subscriptionId,
        paymentMethodId
      );

      return ApiResponse.success(
        res,
        SUBSCRIPTION.PAYMENT_METHOD_UPDATED,
        subscription
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Pause a subscription
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  pauseSubscription: async (req, res, next) => {
    try {
      const { subscriptionId } = req.params;
      const subscription =
        await subscriptionService.pauseSubscription(subscriptionId);
      return ApiResponse.success(res, SUBSCRIPTION.PAUSED, subscription);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Resume a paused subscription
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  resumeSubscription: async (req, res, next) => {
    try {
      const { subscriptionId } = req.params;
      const subscription =
        await subscriptionService.resumeSubscription(subscriptionId);
      return ApiResponse.success(res, SUBSCRIPTION.RESUMED, subscription);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Cancel a subscription
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  cancelSubscription: async (req, res, next) => {
    try {
      const { subscriptionId } = req.params;
      const subscription =
        await subscriptionService.cancelSubscription(subscriptionId);
      return ApiResponse.success(res, SUBSCRIPTION.CANCELED, subscription);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Handle Stripe webhook events
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  handleWebhook: async (req, res, next) => {
    try {
      const event = req.body;
      await subscriptionService.handleWebhookEvent(event);
      return ApiResponse.success(res, SUBSCRIPTION.WEBHOOK_RECEIVED);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Upgrade a subscription's plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  upgradeSubscription: async (req, res, next) => {
    try {
      const result = await subscriptionService.upgradeSubscription(
        req.params,
        req.body,
        req.user
      );
      return ApiResponse.success(res, SUBSCRIPTION.UPGRADED, result);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = subscriptionController;
