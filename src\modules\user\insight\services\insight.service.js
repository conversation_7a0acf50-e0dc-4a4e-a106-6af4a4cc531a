/**
 * User Insight Service
 *
 * Handles business logic for user insights
 */
const insightRepository = require('@models/repositories/insight.repository');
const pdCategoryRepository = require('@models/repositories/pd-category.repository');
const focusRepository = require('@models/repositories/focus.repository');
const wtdCategoryRepository = require('@models/repositories/wtd-category.repository');
const insightViewRepository = require('@models/repositories/insight-view.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { AchievementType } = require('@utils/enums.utils');

/**
 * Insight service
 */
const insightService = {
  /**
   * Get all insights with optional search
   * @param {Object} options - Query options
   * @param {string} options.search - Search term
   * @param {string} options.userId - Optional user ID to filter by creator
   * @param {string} options.queryUserId - Optional user ID to filter by
   * @param {Array<string>} options.focusIds - Optional array of focus IDs to filter by
   * @param {Array<string>} options.pdCategoryIds - Optional array of PD category IDs to filter by
   * @param {Array<string>} options.wtdCategoryIds - Optional array of WTD category IDs to filter by
   * @returns {Promise<Object>} Insights
   */
  getAllInsights: async ({
    search,
    userId,
    queryUserId,
    focusIds = [],
    pdCategoryIds = [],
    wtdCategoryIds = [],
  }) => {
    try {
      return await insightRepository.findAll({
        search,
        focusIds,
        pdCategoryIds,
        wtdCategoryIds,
        userId,
        creatorId: queryUserId,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get insight by ID
   * @param {string} insightId - Insight ID
   * @param {string} userId - Optional user ID to check if user is the creator
   * @returns {Promise<Object>} Insight data
   */
  getInsightById: async (insightId, userId) => {
    try {
      const insight = await insightRepository.findById(insightId, userId);

      // check for monthly insight viewing limit
      await insightViewRepository.checkMonthlyInsightLimit(userId, insightId);

      return insight;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Create a new insight
   * @param {Object} data - Insight data
   * @param {string} userId - User ID from auth
   * @returns {Promise<Object>} Created insight
   */
  createInsight: async (data, userId) => {
    try {
      // Validate PD category exists
      await pdCategoryRepository.findById(data.pdCategoryId);

      // Validate focus IDs if provided
      if (data.focusIds && data.focusIds.length > 0) {
        for (const focusId of data.focusIds) {
          await focusRepository.findById(focusId);
        }
      }

      // Validate WTD category IDs if provided
      if (data.wtdCategoryIds && data.wtdCategoryIds.length > 0) {
        for (const categoryId of data.wtdCategoryIds) {
          await wtdCategoryRepository.findById(categoryId);
        }
      }

      // Create insight
      const insight = await insightRepository.create(data);

      // // Update achievement progress for insight creation
      // await achievementService.updateAchievementProgress(
      //   userId,
      //   AchievementType.INSIGHT_POSTED
      // );

      return insight;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get trending insights based on contributions and likes
   * @param {Object} options - Query options
   * @param {string} options.userId - User ID for personalization (required from auth)
   * @returns {Promise<Array>} Trending insights
   */
  getTrendingInsights: async ({ userId }) => {
    try {
      return await insightRepository.findTrending({
        userId,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get PD Spotlight insights (from Classroom Management category)
   * @param {Object} options - Query options
   * @param {string} options.userId - User ID for personalization (required from auth)
   * @returns {Promise<Array>} Array of PD Spotlight insights
   */
  getPDSpotlight: async ({ userId }) => {
    try {
      const result = await insightRepository.findPDSpotlight({
        userId,
      });
      return result.insights;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = insightService;
