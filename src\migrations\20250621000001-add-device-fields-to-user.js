'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('User', 'deviceToken', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('User', 'deviceType', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('User', 'deviceToken');
    await queryInterface.removeColumn('User', 'deviceType');
  },
};
