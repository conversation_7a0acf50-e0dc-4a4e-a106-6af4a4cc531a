const notificationRepository = require('@repositories/notification.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');

const notificationService = {
  /**
   * List notifications with pagination
   */
  listNotifications: (userId, { limit = 10, page = 1 }) => {
    return notificationRepository.getUserNotificationsWithPagination(
      userId,
      page,
      limit
    );
  },

  /**
   * Mark a notification as read
   */
  readNotification: async (userId, notificationId) => {
    return notificationRepository.checkAndReadNotification(
      notificationId,
      userId
    );
  },
};

module.exports = notificationService;
