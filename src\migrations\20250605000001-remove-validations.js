'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Remove URL validation from Insight table
    await queryInterface.changeColumn('Insight', 'sourceUrl', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    // Remove URL validation and text validation from ExperienceWeekInsight table
    await queryInterface.changeColumn('ExperienceWeekInsight', 'sourceUrl', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.changeColumn('ExperienceWeekInsight', 'text', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    // Restore URL validation for Insight table
    await queryInterface.changeColumn('Insight', 'sourceUrl', {
      type: Sequelize.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    });

    // Restore URL validation and text validation for ExperienceWeekInsight table
    await queryInterface.changeColumn('ExperienceWeekInsight', 'sourceUrl', {
      type: Sequelize.TEXT,
      allowNull: true,
      validate: {
        isUrl: {
          msg: 'Source URL must be a valid URL',
        },
      },
    });

    await queryInterface.changeColumn('ExperienceWeekInsight', 'text', {
      type: Sequelize.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 500],
      },
    });
  },
};
