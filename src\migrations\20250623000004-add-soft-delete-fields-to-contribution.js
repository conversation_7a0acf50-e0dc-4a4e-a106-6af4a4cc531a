'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add soft delete fields to Contribution table
    await queryInterface.addColumn('Contribution', 'isRemoved', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });

    await queryInterface.addColumn('Contribution', 'removedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    // Add index for better query performance
    await queryInterface.addIndex('Contribution', ['isRemoved'], {
      name: 'contribution_is_removed_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove index
    await queryInterface.removeIndex(
      'Contribution',
      'contribution_is_removed_idx'
    );

    // Remove columns from Contribution table
    await queryInterface.removeColumn('Contribution', 'removedAt');
    await queryInterface.removeColumn('Contribution', 'isRemoved');
  },
};
