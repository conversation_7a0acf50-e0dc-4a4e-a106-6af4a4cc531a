/**
 * Migration to modify stripeSubscriptionId column in UserSubscription table
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'UserSubscription',
      'stripeSubscriptionId',
      {
        type: Sequelize.STRING,
        allowNull: true,
      }
    );

    // Remove unique constraint if it exists
    await queryInterface.removeIndex(
      'UserSubscription',
      'user_subscription_stripe_id_idx'
    );
  },

  down: async (queryInterface, Sequelize) => {
    // Add back unique constraint
    await queryInterface.addIndex(
      'UserSubscription',
      ['stripeSubscriptionId'],
      {
        name: 'user_subscription_stripe_id_idx',
        unique: true,
      }
    );

    // Revert column to not null
    await queryInterface.changeColumn(
      'UserSubscription',
      'stripeSubscriptionId',
      {
        type: Sequelize.STRING,
        allowNull: false,
      }
    );
  },
};
