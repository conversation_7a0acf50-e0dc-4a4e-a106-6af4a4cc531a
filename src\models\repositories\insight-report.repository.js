/**
 * InsightReport Repository
 * Handles database operations for insight reports
 */
const databaseService = require('@config/database.config');

class InsightReportRepository {
  /**
   * Find insight report by ID
   * @param {string} id - Report ID
   * @returns {Promise<Object|null>} Report or null
   */
  async findById(id) {
    try {
      const InsightReport = databaseService.getInsightReportModel();
      const User = databaseService.getUserModel();
      const Insight = databaseService.getInsightModel();

      return await InsightReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'reporter',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            model: Insight,
            as: 'insight',
            attributes: ['id', 'insightText', 'status'],
          },
        ],
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a new insight report
   * @param {Object} reportData - Report data
   * @returns {Promise<Object>} Created report
   */
  async create(reportData) {
    try {
      const InsightReport = databaseService.getInsightReportModel();
      const report = await InsightReport.create(reportData);
      return await this.findById(report.id);
    } catch (error) {
      throw new Error(`Error creating insight report: ${error.message}`);
    }
  }

  /**
   * Find all insight reports with pagination and filtering
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Reports and pagination info
   */
  async findAll({
    page = 1,
    limit = 10,
    search,
    sortBy = 'createdAt',
    sortOrder = 'DESC',
  }) {
    try {
      const InsightReport = databaseService.getInsightReportModel();
      const User = databaseService.getUserModel();
      const Insight = databaseService.getInsightModel();

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Build include options with search
      const includeOptions = [
        {
          model: User,
          as: 'reporter',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          where: search
            ? {
                [databaseService.getSequelize().Op.or]: [
                  {
                    firstName: {
                      [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                    },
                  },
                  {
                    lastName: {
                      [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                    },
                  },
                  {
                    email: {
                      [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                    },
                  },
                ],
              }
            : undefined,
          required: search ? true : false,
        },
        {
          model: Insight,
          as: 'insight',
          attributes: ['id', 'insightText', 'status', 'isRemoved'],
          where: search
            ? {
                insightText: {
                  [databaseService.getSequelize().Op.iLike]: `%${search}%`,
                },
              }
            : undefined,
          required: false,
        },
      ];

      const { count, rows } = await InsightReport.findAndCountAll({
        where: whereClause,
        include: includeOptions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true,
      });

      return {
        reports: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      };
    } catch (error) {
      throw new Error(`Error finding insight reports: ${error.message}`);
    }
  }

  /**
   * Check if user has already reported this insight
   * @param {string} insightId - Insight ID
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} True if already reported
   */
  async hasUserReported(insightId, userId) {
    try {
      const InsightReport = databaseService.getInsightReportModel();
      const report = await InsightReport.findOne({
        where: {
          insightId,
          reportedBy: userId,
        },
      });
      return !!report;
    } catch (error) {
      throw new Error(`Error checking user report: ${error.message}`);
    }
  }
}

module.exports = new InsightReportRepository();
