/**
 * Payment Method Repository
 * Handles database operations for PaymentMethod model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { HttpStatus } = require('@utils/enums.utils');
const { PAYMENT_METHOD } = require('@utils/messages.utils');
const commonRepository = require('./common.repository');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      PaymentMethod: databaseService.getPaymentMethodModel(),
    };
  }

  _getPaymentMethodAttributes() {
    return {
      exclude: ['createdAt', 'updatedAt'],
    };
  }

  _getPaymentMethodIncludes() {
    return [];
  }
}

class PaymentMethodRepository extends BaseRepository {
  /**
   * Create payment method
   * @param {Object} user - User object
   * @param {Object} data - Payment method data
   * @param {Object} stripeData - Stripe payment method data
   * @returns {Promise<Object>} Created payment method
   */
  async create(user, data, stripeData) {
    try {
      const {
        isDefault,
        billingAddressStreet,
        billingAddressCity,
        billingAddressZipCode,
      } = data;

      return await this.models.PaymentMethod.create({
        userId: user.id,
        stripePaymentMethodId: stripeData.id,
        type: stripeData.type,
        last4: stripeData.card.last4,
        brand: stripeData.card.brand,
        expMonth: stripeData.card.exp_month,
        expYear: stripeData.card.exp_year,
        isDefault,
        billingAddressStreet,
        billingAddressCity,
        billingAddressZipCode,
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.CREATE_ERROR
      );
    }
  }

  /**
   * Get all payment methods for a user with pagination
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @returns {Promise<Object>} Object containing payment methods and pagination info
   */
  async getPaymentMethods(userId, { page = 1, limit = 10 } = {}) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.PaymentMethod.findAndCountAll({
        where: { userId },
        ...this._getPaymentMethodAttributes(),
        include: this._getPaymentMethodIncludes(),
        order: [['createdAt', 'DESC']],
        limit,
        offset,
      });

      return {
        paymentMethods: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in getPaymentMethods repository:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.FIND_ERROR
      );
    }
  }

  /**
   * Find user's default payment method
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Default payment method
   */
  async findDefaultByPaymentMethod(userId) {
    try {
      return await this.models.PaymentMethod.findOne({
        where: { userId, isDefault: true },
        ...this._getPaymentMethodAttributes(),
        include: this._getPaymentMethodIncludes(),
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.FIND_ERROR
      );
    }
  }

  /**
   * Delete payment method
   * @param {string} id - Payment method ID
   * @returns {Promise<boolean>} Success status
   */
  async delete(id) {
    try {
      const deleted = await this.models.PaymentMethod.destroy({
        where: { id },
      });
      if (!deleted) {
        throw new ApiException(HttpStatus.NOT_FOUND, PAYMENT_METHOD.NOT_FOUND);
      }
      return true;
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.DELETE_ERROR
      );
    }
  }

  /**
   * Set a payment method as default and update others to non-default
   * @param {string} id - Payment method ID to set as default
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated payment method
   */
  async setAsDefault(id, userId) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // First, set all user's payment methods to non-default
      const [nonDefaultCount] = await this.models.PaymentMethod.update(
        { isDefault: false },
        {
          where: { userId },
          transaction,
        }
      );
      console.log(`Updated ${nonDefaultCount} payment methods to non-default`);

      // Then set the specified payment method as default
      const [defaultCount] = await this.models.PaymentMethod.update(
        { isDefault: true },
        {
          where: { id, userId },
          transaction,
        }
      );
      console.log(`Updated ${defaultCount} payment methods to default`);

      if (defaultCount === 0) {
        throw new ApiException(HttpStatus.NOT_FOUND, PAYMENT_METHOD.NOT_FOUND);
      }

      // Fetch and return the updated payment method
      const updatedPaymentMethod = await this.models.PaymentMethod.findOne({
        where: { id, userId },
        transaction,
      });

      if (!updatedPaymentMethod) {
        throw new ApiException(HttpStatus.NOT_FOUND, PAYMENT_METHOD.NOT_FOUND);
      }

      await transaction.commit();
      return updatedPaymentMethod;
    } catch (error) {
      await transaction.rollback();
      console.error('Error in setAsDefault:', error);
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.SET_DEFAULT_ERROR
      );
    }
  }

  /**
   * Find payment method by ID
   * @param {string} id - Payment method ID
   * @returns {Promise<Object>} Payment method
   */
  async findById(id) {
    try {
      return await this.models.PaymentMethod.findByPk(id, {
        ...this._getPaymentMethodAttributes(),
        include: this._getPaymentMethodIncludes(),
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.FIND_ERROR
      );
    }
  }

  /**
   * Check if a card with the same details already exists for a user
   * @param {string} userId - User ID
   * @param {Object} cardDetails - Card details to check
   * @returns {Promise<boolean>} True if duplicate exists
   */
  async isDuplicateCard(userId, cardDetails) {
    try {
      const existingPaymentMethods = await this.getPaymentMethods(userId);
      return existingPaymentMethods.paymentMethods.some(
        (pm) =>
          pm.last4 === cardDetails.last4 &&
          pm.brand === cardDetails.brand &&
          pm.expMonth === cardDetails.expMonth &&
          pm.expYear === cardDetails.expYear
      );
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.FIND_ERROR
      );
    }
  }
}

module.exports = new PaymentMethodRepository();
