/**
 * Admin User Controller
 * Handles admin CRUD operations for users
 */
const userService = require('./user.service');
const { ApiResponse } = require('@utils/response.utils');
const { USER } = require('@utils/messages.utils');

const userController = {
  /**
   * Create a new user (admin)
   */
  createUser: async (req, res, next) => {
    try {
      const user = await userService.createUser(req.body);
      return ApiResponse.created(res, USER.USER_CREATED, user);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update an existing user (admin)
   */
  updateUser: async (req, res, next) => {
    try {
      const user = await userService.updateUser(req.params.userId, req.body);
      return ApiResponse.success(res, USER.USER_UPDATED, user);
    } catch (error) {
      next(error);
    }
  },

  /**
   * List all users (admin) with pagination
   */
  listUsers: async (req, res, next) => {
    try {
      const { page, limit } = req.pagination;
      const result = await userService.listUsers(req.query, page, limit);
      return ApiResponse.success(res, USER.USERS_RETRIEVED, result.data, {
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Retrieve a particular user (admin)
   */
  getUserByUserId: async (req, res, next) => {
    try {
      const user = await userService.getUserByUserId(req.params.userId);
      return ApiResponse.success(res, USER.USER_RETRIEVED, user);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete a user (admin)
   */
  deleteUser: async (req, res, next) => {
    try {
      await userService.deleteUser(req.params.userId);
      return ApiResponse.success(res, USER.USER_DELETED);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = userController;
