'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('EducatorQuestionContribution', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      educatorQuestionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'EducatorQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      isRemoved: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      removedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex(
      'EducatorQuestionContribution',
      ['educatorQuestionId'],
      {
        name: 'educator_question_contribution_question_id_idx',
      }
    );
    await queryInterface.addIndex('EducatorQuestionContribution', ['userId'], {
      name: 'educator_question_contribution_user_id_idx',
    });
    await queryInterface.addIndex(
      'EducatorQuestionContribution',
      ['isRemoved'],
      {
        name: 'educator_question_contribution_is_removed_idx',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('EducatorQuestionContribution');
  },
};
