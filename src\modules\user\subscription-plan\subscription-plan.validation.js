/**
 * Admin Subscription Plan Validation
 *
 * Validation schemas for subscription plan-related endpoints
 */
const { param, query } = require('express-validator');
const { UserType } = require('@utils/enums.utils');

/**
 * Validation schemas for subscription plan endpoints
 */
const subscriptionPlanValidation = {
  /**
   * Validate get all subscription plans request
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
    query('targetUserType')
      .optional()
      .isIn([UserType.EDUCATOR, UserType.PROVIDER])
      .withMessage('Target user type must be either EDUCATOR or PROVIDER'),
  ],

  /**
   * Validate get subscription plan by ID request
   */
  getById: [param('id').isUUID(4).withMessage('Invalid subscription plan ID')],
};

module.exports = subscriptionPlanValidation;
