/**
 * Payment Method Controller
 *
 * Handles payment method-related HTTP requests
 */
const paymentMethodService = require('./payment-method.service');
const { ApiResponse } = require('@utils/response.utils');
const { PAYMENT_METHOD } = require('@utils/messages.utils');

/**
 * Payment Method controller
 */
const paymentMethodController = {
  /**
   * Create a setup intent for securely collecting payment method details
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createSetupIntent: async (req, res, next) => {
    try {
      const setupIntent = await paymentMethodService.createSetupIntent(
        req.user
      );
      return ApiResponse.success(
        res,
        PAYMENT_METHOD.SETUP_INTENT_CREATED,
        setupIntent
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Add a new payment method
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  addPaymentMethod: async (req, res, next) => {
    try {
      const paymentMethod = await paymentMethodService.addPaymentMethod(
        req.user,
        req.body
      );

      return ApiResponse.created(res, PAYMENT_METHOD.CREATED, paymentMethod);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all payment methods for a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getPaymentMethods: async (req, res, next) => {
    try {
      const result = await paymentMethodService.getPaymentMethods(
        req.user,
        req.pagination
      );

      return ApiResponse.success(
        res,
        PAYMENT_METHOD.ALL_RETRIEVED,
        result.paymentMethods,
        { pagination: result.pagination }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get default payment method for a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getDefaultPaymentMethod: async (req, res, next) => {
    try {
      const paymentMethod = await paymentMethodService.getDefaultPaymentMethod(
        req.user
      );

      return ApiResponse.success(
        res,
        PAYMENT_METHOD.RETRIEVED,
        paymentMethod || {}
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Remove a payment method
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  removePaymentMethod: async (req, res, next) => {
    try {
      const { id } = req.params;
      await paymentMethodService.removePaymentMethod(id, req.user);

      return ApiResponse.success(res, PAYMENT_METHOD.DELETED);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Set a payment method as default
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  setDefaultPaymentMethod: async (req, res, next) => {
    try {
      const { id } = req.params;
      await paymentMethodService.setDefaultPaymentMethod(id, req.user);

      return ApiResponse.success(res, PAYMENT_METHOD.SET_DEFAULT);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = paymentMethodController;
