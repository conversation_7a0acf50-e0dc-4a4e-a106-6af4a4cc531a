'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Milestone', 'milestoneIdentifier', {
      type: Sequelize.STRING,
      allowNull: false,
      unique: true,
    });

    await queryInterface.addIndex('Milestone', ['milestoneIdentifier'], {
      unique: true,
      name: 'milestone_identifier_unique_idx',
    });
  },

  async down(queryInterface) {
    await queryInterface.removeIndex(
      'Milestone',
      'milestone_identifier_unique_idx'
    );
    await queryInterface.removeColumn('Milestone', 'milestoneIdentifier');
  },
};
