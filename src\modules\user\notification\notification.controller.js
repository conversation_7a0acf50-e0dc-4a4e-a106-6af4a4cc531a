const notificationService = require('./notification.service');
const { ApiResponse } = require('@utils/response.utils');
const { NOTIFICATION } = require('@utils/messages.utils');

const notificationController = {
  /**
   * List notifications with pagination
   */
  listNotifications: async (req, res, next) => {
    try {
      const userId = req.user.id;
      const { page, limit } = req.pagination;
      const { data, pagination } = await notificationService.listNotifications(
        userId,
        {
          page,
          limit,
        }
      );
      return ApiResponse.success(res, NOTIFICATION.ALL_RETRIEVED, data, {
        pagination,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Mark a notification as read
   */
  readNotification: async (req, res, next) => {
    try {
      const userId = req.user.id;
      const notificationId = req.params.id;
      await notificationService.readNotification(userId, notificationId);
      return ApiResponse.success(res, NOTIFICATION.READ);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = notificationController;
