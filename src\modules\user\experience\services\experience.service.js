/**
 * Experience Service
 *
 * Business logic for experience operations
 */
const experienceRepository = require('@models/repositories/experience.repository');
const insightRepository = require('@models/repositories/insight.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE } = require('@utils/messages.utils');
const { InsightStatus } = require('@utils/enums.utils');

/**
 * Experience service
 */
const experienceService = {
  /**
   * Create a new experience with weeks, insights, and media
   * @param {Object} data - Experience data
   * @param {string} userId - Creator user ID
   * @returns {Promise<Object>} Created experience
   */
  createExperience: async (data, userId) => {
    try {
      const experienceData = {
        ...data,
        createdBy: userId,
      };

      const experience = await experienceRepository.create(experienceData);
      return experience;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all experiences with pagination
   * @param {Object} query - Query parameters from req.query
   * @param {Object} pagination - Pagination data from req.pagination
   * @param {string} userId - User ID for enrollment check
   * @param {string} queryUserId - User ID for query
   * @returns {Promise<Object>} Experiences with pagination
   */
  getAllExperiences: async (query, pagination, userId, queryUserId) => {
    try {
      const { page, limit } = pagination;

      return await experienceRepository.findAll({
        page,
        limit,
        createdBy: queryUserId,
        userId,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get complete experience details with all associations, statistics, and user-specific data
   * @param {string} id - Experience ID
   * @param {string} userId - User ID for enrollment check and user-specific data
   * @returns {Promise<Object>} Complete experience details with all associations
   */
  getExperienceDetails: async (id, userId) => {
    try {
      const experience = await experienceRepository.getExperienceDetails(
        id,
        true,
        userId
      );

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return experience;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get week details including weeklyWhy, insights, user engagement stats, and provider engagement stats (not week-specific)
   * @param {string} weekId - The ID of the week
   * @param {string} loggedInUser - The ID of the logged-in user
   * @param {Object} pagination - Pagination parameters
   * @param {number} pagination.page - Page number
   * @param {number} pagination.limit - Items per page
   * @returns {Promise<Object>} Week details with insights, user engagement stats, and provider engagement stats
   */
  getWeekDetails: async (weekId, loggedInUser, pagination) => {
    const { id: userId, userType } = loggedInUser;
    try {
      // Get week details
      const week = await experienceRepository.findWeekById(weekId);
      if (!week) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.WEEK_NOT_FOUND);
      }

      // Get insights for the week with pagination
      const { insights, pagination: insightPagination } =
        await insightRepository.getInsightsByExperienceWeekId(
          weekId,
          pagination,
          userId,
          InsightStatus.APPROVED
        );

      // Get engagement stats
      const engagementStats = await experienceRepository.getWeekEngagementStats(
        week.experienceId,
        userId,
        week.weekNumber
      );

      // Get provider engagement stats (not week-specific, same as experience details)
      const providerEngagementStats =
        await experienceRepository.getProviderEngagementStats(
          week.experienceId
        );

      // Prepare response data
      const data = {
        userType,
        weekInfo: week,
        engagementStats,
        providerEngagementStats,
        insights,
      };

      return {
        message: EXPERIENCE.WEEK_DETAILS_RETRIEVED,
        data,
        pagination: insightPagination,
      };
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceService;
