/**
 * Experience Week Media Watch Model
 * Tracks which videos a user has watched in experience weeks
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWeekMediaWatch extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        experienceWeekMediaId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeekMedia',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        watchDuration: {
          type: DataTypes.INTEGER,
          allowNull: true,
          comment: 'Duration watched in seconds',
        },
        isCompleted: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: 'Whether the video was watched completely',
        },
        lastWatchedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          comment: 'Last time the video was watched',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekMediaWatch',
        tableName: 'ExperienceWeekMediaWatch',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceWeekMediaId'],
            name: 'experience_week_media_watch_media_id_idx',
          },
          {
            fields: ['userId'],
            name: 'experience_week_media_watch_user_id_idx',
          },
          {
            fields: ['experienceWeekMediaId', 'userId'],
            name: 'experience_week_media_watch_unique_idx',
            unique: true,
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to ExperienceWeekMedia
    this.belongsTo(models.ExperienceWeekMedia, {
      foreignKey: 'experienceWeekMediaId',
      as: 'watchedMedia',
      onDelete: 'CASCADE',
    });

    // Belongs to User
    this.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'watcher',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = ExperienceWeekMediaWatch;
