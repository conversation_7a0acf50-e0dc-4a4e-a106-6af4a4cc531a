'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceWeekMediaWatch', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceWeekMediaId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeekMedia',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      watchDuration: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'Duration watched in seconds',
      },
      isCompleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Whether the video was watched completely',
      },
      lastWatchedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Last time the video was watched',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Add indexes
    await queryInterface.addIndex(
      'ExperienceWeekMediaWatch',
      ['experienceWeekMediaId'],
      {
        name: 'experience_week_media_watch_media_id_idx',
      }
    );

    await queryInterface.addIndex('ExperienceWeekMediaWatch', ['userId'], {
      name: 'experience_week_media_watch_user_id_idx',
    });

    await queryInterface.addIndex(
      'ExperienceWeekMediaWatch',
      ['experienceWeekMediaId', 'userId'],
      {
        name: 'experience_week_media_watch_unique_idx',
        unique: true,
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceWeekMediaWatch');
  },
};
