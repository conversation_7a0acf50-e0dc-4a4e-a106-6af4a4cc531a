'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('MilestoneQuestion', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      milestoneId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Milestone',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      question: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      questionType: {
        type: Sequelize.ENUM('MULTIPLE_CHOICE', 'TEXT'),
        allowNull: false,
        defaultValue: 'MULTIPLE_CHOICE',
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      isRequired: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
    await queryInterface.addIndex('MilestoneQuestion', ['milestoneId'], {
      name: 'milestone_question_milestone_id_idx',
    });
    await queryInterface.addIndex('MilestoneQuestion', ['isActive'], {
      name: 'milestone_question_is_active_idx',
    });
    await queryInterface.addIndex('MilestoneQuestion', ['order'], {
      name: 'milestone_question_order_idx',
    });
    await queryInterface.addIndex('MilestoneQuestion', ['questionType'], {
      name: 'milestone_question_type_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'MilestoneQuestion',
      'milestone_question_type_idx'
    );
    await queryInterface.removeIndex(
      'MilestoneQuestion',
      'milestone_question_order_idx'
    );
    await queryInterface.removeIndex(
      'MilestoneQuestion',
      'milestone_question_is_active_idx'
    );
    await queryInterface.removeIndex(
      'MilestoneQuestion',
      'milestone_question_milestone_id_idx'
    );
    await queryInterface.dropTable('MilestoneQuestion');
  },
};
