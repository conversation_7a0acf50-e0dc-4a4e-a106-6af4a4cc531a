'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('User', 'resetToken', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('User', 'resetTokenExpiry', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    // Add index for resetToken for faster lookups
    await queryInterface.addIndex('User', ['resetToken'], {
      name: 'user_reset_token_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('User', 'user_reset_token_idx');
    await queryInterface.removeColumn('User', 'resetTokenExpiry');
    await queryInterface.removeColumn('User', 'resetToken');
  },
};
