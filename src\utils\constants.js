/**
 * Application Constants
 *
 * This file contains all the constant values used across the application.
 * It helps maintain consistency and makes it easier to update values in one place.
 */

// Default values for pagination
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
};

// Default values for PD Spotlight
const PD_SPOTLIGHT = {
  DEFAULT_CATEGORY: 'Classroom Management',
};

// Default values for trending insights
const TRENDING = {
  WEIGHTS: {
    LIKES: 1.0,
    CONTRIBUTIONS: 1.0,
  },
};

// Auth related constants
const AUTH = {
  RESET_TOKEN_EXPIRY_MINUTES: 15, // 15 minutes
  MINUTE_IN_MS: 60 * 1000, // 1 minute in milliseconds
  get RESET_TOKEN_EXPIRY_MS() {
    return this.RESET_TOKEN_EXPIRY_MINUTES * this.MINUTE_IN_MS;
  },
};

// Subscription Plan Constants
const SUBSCRIPTION_PLAN = {
  EDUCATOR_FREE: {
    name: 'Educator Free',
    targetUserType: 'EDUCATOR',
    description:
      'Free forever. View 12 insights per month, no experience registration.',
    price: 0,
    priceCents: 0,
    billingInterval: 'ONE_TIME',
    trialDays: null,
    canRegisterExperiences: false,
    insightsLimit: 12,
    insightsLimitPeriod: 'MONTHLY',
    isActive: true,
  },
  EDUCATOR_PLUS_ANNUAL: {
    name: 'Educator Plus (Annual)',
    targetUserType: 'EDUCATOR',
    description: '$159/year full access',
    price: 159,
    priceCents: 15900,
    billingInterval: 'YEARLY',
    trialDays: null,
    canRegisterExperiences: true,
    insightsLimit: null,
    insightsLimitPeriod: null,
    isActive: true,
  },
  EDUCATOR_PLUS_3_MONTH: {
    name: 'Educator Plus (3-Month Pass)',
    targetUserType: 'EDUCATOR',
    description: '$49.99 every 3 months',
    price: 49.99,
    priceCents: 4999,
    billingInterval: 'EVERY_3_MONTHS',
    trialDays: null,
    canRegisterExperiences: true,
    insightsLimit: null,
    insightsLimitPeriod: null,
    isActive: true,
  },
  PROVIDER_FREE: {
    name: 'Provider Free',
    targetUserType: 'PROVIDER',
    description: 'Free provider account with limited features',
    price: 0,
    priceCents: 0,
    billingInterval: 'ONE_TIME',
    trialDays: null,
    canRegisterExperiences: false,
    insightsLimit: 999,
    insightsLimitPeriod: 'MONTHLY',
    isActive: false,
  },
  PROVIDER_PLUS_ANNUAL: {
    name: 'Provider Plus (Annual)',
    targetUserType: 'PROVIDER',
    description: '$500/year full access, free for first year',
    price: 500,
    priceCents: 50000,
    billingInterval: 'YEARLY',
    trialDays: 365,
    canRegisterExperiences: true,
    insightsLimit: null,
    insightsLimitPeriod: null,
    isActive: true,
  },
};

module.exports = {
  PAGINATION,
  PD_SPOTLIGHT,
  TRENDING,
  SUBSCRIPTION_PLAN,
  AUTH,
};
