const notificationRepository = require('@repositories/notification.repository');
const {
  sendPushNotification,
} = require('@integrations/firebase/notification.service');
const userRepository = require('@repositories/user.repository');
const { NotificationType } = require('@utils/enums.utils');
const { NOTIFICATION } = require('@utils/messages.utils');

/**
 * Notify the creator of an insight when a contribution is made
 * @param {Object} params
 * @param {Object} params.contributor - The user who contributed
 * @param {Object} params.insight - The insight object
 * @param {Object} params.contribution - The contribution object
 */
async function notifyContributionToInsight({
  contributor,
  insight,
  contribution,
}) {
  if (insight.createdBy === contributor.id) return;
  try {
    const creator = await userRepository.findById(insight.createdBy);
    if (creator && creator.deviceToken) {
      const educatorName = `${contributor.firstName} ${contributor.lastName}`;
      const notificationText = `${educatorName} contributed to your insight.`;
      const commonNotificationPayload = {
        userId: contributor.id,
        receiverUserId: creator.id,
        type: NotificationType.CONTRIBUTION,
        title: NOTIFICATION.NEW_CONTRIBUTION_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { insightId: insight.id, contributionId: contribution.id },
      });
      await sendPushNotification({
        deviceToken: creator.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          insightId: insight.id,
          contributionId: contribution.id,
          redirectUrl: `/lounge?notification=true`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Notify the creator of an insight when it is implemented by another user
 * @param {Object} params
 * @param {Object} params.implementer - The user who implemented
 * @param {Object} params.insight - The insight object
 */
async function notifyInsightImplemented({ implementer, insight }) {
  if (insight.createdBy === implementer.id) return;
  try {
    const creator = await userRepository.findById(insight.createdBy);
    if (creator && creator.deviceToken) {
      const implementerName = `${implementer.firstName} ${implementer.lastName}`;
      const notificationText = `${implementerName} implemented your insight.`;
      const commonNotificationPayload = {
        userId: implementer.id,
        receiverUserId: creator.id,
        type: NotificationType.IMPLEMENTATION,
        title: NOTIFICATION.INSIGHT_IMPLEMENTED_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { insightId: insight.id },
      });
      await sendPushNotification({
        deviceToken: creator.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          insightId: insight.id,
          redirectUrl: `/lounge?notification=true`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Notify the creator of an insight when it is approved by admin
 * @param {Object} insight - The insight object
 */
async function notifyInsightApproved(insight) {
  const provider = insight.creator;
  try {
    console.log(
      '[notifyInsightApproved] Called with insight:',
      JSON.stringify(insight)
    );

    if (!provider || !provider.deviceToken) {
      console.log(
        '[notifyInsightApproved] No provider or deviceToken found for insight:',
        insight.id
      );
      return;
    }

    // Check if notification already exists for this insight approval
    const existingNotification = await notificationRepository.findNotification({
      receiverUserId: provider.id,
      type: NotificationType.INSIGHT_APPROVED,
      data: {
        insightId: insight.id,
      },
    });

    if (existingNotification) {
      console.log(
        '[notifyInsightApproved] Notification already sent for insightId:',
        insight.id,
        'existing notification ID:',
        existingNotification.id
      );
      return;
    }

    const notificationText = `Congratulations! Your insight is live.`;
    const commonNotificationPayload = {
      userId: null,
      receiverUserId: provider.id,
      type: NotificationType.INSIGHT_APPROVED,
      title: NOTIFICATION.INSIGHT_APPROVED_TITLE,
      body: notificationText,
    };
    console.log(
      '[notifyInsightApproved] Creating notification with payload:',
      JSON.stringify(commonNotificationPayload)
    );
    await notificationRepository.createNotification({
      ...commonNotificationPayload,
      data: { insightId: insight.id },
    });
    console.log(
      '[notifyInsightApproved] Sending push notification to deviceToken:',
      provider.deviceToken
    );
    await sendPushNotification({
      deviceToken: provider.deviceToken,
      title: commonNotificationPayload.title,
      body: commonNotificationPayload.body,
      data: {
        ...commonNotificationPayload,
        insightId: insight.id,
        redirectUrl: `/lounge?notification=true`,
      },
    });
    console.log(
      '[notifyInsightApproved] Notification and push sent for insightId:',
      insight.id
    );
  } catch (error) {
    console.error('[notifyInsightApproved] Error:', error);
    throw error;
  }
}

/**
 * Notify a contributor when their contribution is liked by another user
 * @param {Object} params
 * @param {Object} params.liker - The user who liked the contribution
 * @param {Object} params.contribution - The contribution object
 */
async function notifyContributionLiked({ liker, contribution }) {
  const contributor = contribution.contributor;
  if (!contributor || contributor.id === liker.id) return;
  try {
    if (contributor.deviceToken) {
      const likerName = `${liker.firstName} ${liker.lastName}`;
      const notificationText = `${likerName} liked your contribution.`;
      const commonNotificationPayload = {
        userId: liker.id,
        receiverUserId: contributor.id,
        type: NotificationType.LIKE_CONTRIBUTION,
        title: NOTIFICATION.CONTRIBUTION_LIKED_TITLE,
        body: notificationText,
      };
      await notificationRepository.createNotification({
        ...commonNotificationPayload,
        data: { contributionId: contribution.id },
      });
      await sendPushNotification({
        deviceToken: contributor.deviceToken,
        title: commonNotificationPayload.title,
        body: commonNotificationPayload.body,
        data: {
          ...commonNotificationPayload,
          contributionId: contribution.id,
          redirectUrl: `/lounge?notification=true`,
        },
      });
    }
  } catch (error) {
    throw error;
  }
}

module.exports = {
  notifyContributionToInsight,
  notifyInsightImplemented,
  notifyInsightApproved,
  notifyContributionLiked,
};
