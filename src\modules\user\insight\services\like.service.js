/**
 * Like Service
 *
 * Handles business logic for liking insights
 */
const likedInsightRepository = require('@models/repositories/liked-insight.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { ApiException } = require('@utils/exception.utils');
const { AchievementType, UserType } = require('@utils/enums.utils');
const insightRepository = require('@models/repositories/insight.repository');
const contributionRepository = require('@repositories/contribution.repository');
const { notifyContributionLiked } = require('./notification.service');
const { INSIGHT } = require('@utils/messages.utils');

/**
 * Helper function to check if a provider is liking another provider's insight
 * @param {Object} user - User object
 * @param {Object} insight - Insight object
 * @returns {boolean} True if provider is liking another provider's insight
 */
function isProviderLikingAnotherProvider(user, insight) {
  const userId = user.id;
  const userType = user.userType;
  const creatorId = insight.creator.id;
  const creatorUserType = insight.creator.userType;

  const isProviderUser =
    userType === UserType.PROVIDER || userType === UserType.PROVIDER_PLUS;
  const isOtherProvider =
    creatorUserType === UserType.PROVIDER ||
    creatorUserType === UserType.PROVIDER_PLUS;
  const isLikingOwnInsight = creatorId === userId;

  return isProviderUser && !isLikingOwnInsight && isOtherProvider;
}

/**
 * Like Service
 */
const likeService = {
  /**
   * Toggle like status for an insight
   * @param {Object} user - User object containing user type and other details
   * @param {string} insightId - Insight ID
   * @param {string} contributionId - Contribution ID
   * @returns {Promise<Object>} Object with isLiked status
   */
  toggleLike: async (user, insightId, contributionId) => {
    try {
      // First check if the insight exists
      const insight = await insightRepository.findById(insightId);
      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      const result = await likedInsightRepository.toggleLike(
        user.id,
        insightId
      );

      // Determine the correct achievement type based on user type and insight creator
      const achievementType = isProviderLikingAnotherProvider(user, insight)
        ? AchievementType.PROVIDER_INSIGHT_LIKED
        : AchievementType.INSIGHT_LIKED;

      // Update achievement progress with increment or decrement
      await achievementService.updateAchievementProgress(
        user.id,
        achievementType,
        result.isLiked ? 1 : -1
      );

      // Notify the contributor if liked
      if (result.isLiked && contributionId) {
        const contribution =
          await contributionRepository.findById(contributionId);
        if (contribution && contribution.contributor) {
          await notifyContributionLiked({
            liker: user,
            contribution,
            contributor: contribution.contributor,
          });
        }
      }

      return result;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = likeService;
