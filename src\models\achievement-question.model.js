/**
 * AchievementQuestion Model
 * Represents questions for achievement completion
 */
const { Model, DataTypes } = require('sequelize');
const { QuestionType, UserType } = require('@utils/enums.utils');

class AchievementQuestion extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        achievementId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Achievement',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        userType: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        question: {
          type: DataTypes.TEXT,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        questionType: {
          type: DataTypes.ENUM(...QuestionType.values),
          allowNull: false,
          defaultValue: QuestionType.TEXT,
          validate: {
            isIn: [QuestionType.values],
          },
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
        isRequired: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
      },
      {
        sequelize,
        modelName: 'AchievementQuestion',
        tableName: 'AchievementQuestion',
        timestamps: true,
        indexes: [
          {
            fields: ['achievementId'],
            name: 'achievement_question_achievement_id_idx',
          },
          {
            fields: ['userType'],
            name: 'achievement_question_user_type_idx',
          },
          {
            fields: ['isActive'],
            name: 'achievement_question_is_active_idx',
          },
          {
            fields: ['order'],
            name: 'achievement_question_order_idx',
          },
          {
            fields: ['questionType'],
            name: 'achievement_question_type_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to achievement
    AchievementQuestion.belongsTo(models.Achievement, {
      foreignKey: 'achievementId',
      as: 'achievement',
      onDelete: 'CASCADE',
    });

    // Has many user responses
    AchievementQuestion.hasMany(models.UserAchievementResponse, {
      foreignKey: 'questionId',
      as: 'responses',
      onDelete: 'CASCADE',
    });

    // Has many options
    AchievementQuestion.hasMany(models.AchievementQuestionOption, {
      foreignKey: 'questionId',
      as: 'options',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = AchievementQuestion;
