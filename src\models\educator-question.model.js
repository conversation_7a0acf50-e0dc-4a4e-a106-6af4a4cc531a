/**
 * EducatorQuestion Model
 * Represents EducatorQuestion
 */
const { Model, DataTypes } = require('sequelize');
const { EducatorQuestionStatus } = require('@utils/enums.utils');

class EducatorQuestion extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        text: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        sourceUrl: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        pdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
        },
        status: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: EducatorQuestionStatus.PENDING,
        },
        reviewedBy: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'Admin',
            key: 'id',
          },
        },
        reviewedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        rejectionReason: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        isRemoved: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        removedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'EducatorQuestion',
        tableName: 'EducatorQuestion',
        timestamps: true,
      }
    );
  }

  static associate(models) {
    // One PD Category
    this.belongsTo(models.PdCategory, {
      foreignKey: 'pdCategoryId',
      as: 'pdCategory',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
    });

    // One Admin (reviewer)
    this.belongsTo(models.Admin, {
      foreignKey: 'reviewedBy',
      as: 'reviewer',
      onDelete: 'SET NULL',
    });

    // One User (creator)
    this.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'CASCADE',
    });

    // Many Focus Tags
    this.belongsToMany(models.Focus, {
      through: models.EducatorQuestionFocus,
      as: 'focus',
      foreignKey: 'educatorQuestionId',
      otherKey: 'focusId',
      onDelete: 'CASCADE',
    });

    // Many WTD Categories
    this.belongsToMany(models.WtdCategory, {
      through: models.EducatorQuestionWtdCategory,
      as: 'wtdCategories',
      foreignKey: 'educatorQuestionId',
      otherKey: 'wtdCategoryId',
      onDelete: 'CASCADE',
    });

    // Bookmarked by users association
    this.belongsToMany(models.User, {
      through: models.BookmarkedEducatorQuestion,
      as: 'bookmarkedByUsers',
      foreignKey: 'educatorQuestionId',
      otherKey: 'userId',
      onDelete: 'CASCADE',
    });

    // Liked by users association
    this.belongsToMany(models.User, {
      through: models.LikedEducatorQuestion,
      as: 'likedByUsers',
      foreignKey: 'educatorQuestionId',
      otherKey: 'userId',
      onDelete: 'CASCADE',
    });

    // Implemented by users association
    this.belongsToMany(models.User, {
      through: models.ImplementedEducatorQuestion,
      as: 'implementedByUsers',
      foreignKey: 'educatorQuestionId',
      otherKey: 'userId',
      onDelete: 'CASCADE',
    });

    // Has many contributions
    this.hasMany(models.EducatorQuestionContribution, {
      foreignKey: 'educatorQuestionId',
      as: 'contributions',
      onDelete: 'CASCADE',
    });

    // Has many educator question views
    this.hasMany(models.EducatorQuestionView, {
      foreignKey: 'educatorQuestionId',
      as: 'educatorQuestionViews',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = EducatorQuestion;
