/**
 * EducatorQuestion Service
 * Handles business logic for educator questions
 */
const educatorQuestionRepository = require('@models/repositories/educator-question.repository');
// Placeholder for bookmark, like, contribute, implement, report repositories/services

const educatorQuestionService = {
  /**
   * Create a new educator question
   */
  createEducatorQuestion: async (data, loggedInUser) => {
    return educatorQuestionRepository.create(data, loggedInUser);
  },

  /**
   * Get all educator questions
   */
  getAllEducatorQuestions: async (query, loggedInUser) => {
    return educatorQuestionRepository.findAll(query, loggedInUser);
  },

  /**
   * Get educator question by ID
   */
  getEducatorQuestionById: async (questionId, loggedInUser) => {
    return educatorQuestionRepository.findById(questionId, loggedInUser);
  },

  /**
   * Toggle like status for an educator question
   */
  toggleLike: async (user, educatorQuestionId) => {
    return educatorQuestionRepository.toggleLike(user.id, educatorQuestionId);
  },

  /**
   * Toggle bookmark status for an educator question
   */
  toggleBookmark: async (user, educatorQuestionId) => {
    return educatorQuestionRepository.toggleBookmark(
      user.id,
      educatorQuestionId
    );
  },

  // TODO: Implement contribute, implement, report, etc. mirroring insightService
};

module.exports = educatorQuestionService;
