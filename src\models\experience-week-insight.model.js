/**
 * ExperienceWeekInsight Model
 * Links insights to experience weeks (max 5 per week)
 * References main Insight model for data storage
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWeekInsight extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        experienceWeekId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeek',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
            max: 5,
          },
          comment: 'Order of insight within week (1-5)',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekInsight',
        tableName: 'ExperienceWeekInsight',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceWeekId'],
            name: 'experience_week_insight_week_id_idx',
          },
          {
            fields: ['insightId'],
            name: 'experience_week_insight_insight_id_idx',
          },
          {
            fields: ['order'],
            name: 'experience_week_insight_order_idx',
          },
          {
            unique: true,
            fields: ['experienceWeekId', 'order'],
            name: 'experience_week_insight_week_order_unique_idx',
          },
          {
            unique: true,
            fields: ['experienceWeekId', 'insightId'],
            name: 'experience_week_insight_week_insight_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to ExperienceWeek
    this.belongsTo(models.ExperienceWeek, {
      foreignKey: 'experienceWeekId',
      as: 'experienceWeek',
      onDelete: 'CASCADE',
    });

    // Belongs to Insight
    this.belongsTo(models.Insight, {
      foreignKey: 'insightId',
      as: 'insight',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };

    // Add computed fields if needed
    return values;
  }
}

module.exports = ExperienceWeekInsight;
