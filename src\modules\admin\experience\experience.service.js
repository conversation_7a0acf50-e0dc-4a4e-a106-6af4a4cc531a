/**
 * Experience Service
 *
 * Handles business logic for experience operations
 */
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus, ExperienceStatus } = require('@utils/enums.utils');
const { EXPERIENCE } = require('@utils/messages.utils');
const experienceRepository = require('@models/repositories/experience.repository');
const insightRepository = require('@models/repositories/insight.repository');
const experienceReviewRepository = require('@models/repositories/experience-review.repository');
const experienceDiscussionRepository = require('@models/repositories/experience-discussion.repository');
const experienceNotificationService = require('@modules/user/experience/services/notification.service');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { AchievementType } = require('@utils/enums.utils');

const experienceService = {
  /**
   * Get all experiences with pagination and search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.userId - User ID for filtering
   * @returns {Promise<Object>} Paginated list of experiences
   */
  getAllExperiences: async ({ page, limit, userId } = {}) => {
    try {
      const result = await experienceRepository.findAllForAdmin({
        page,
        limit,
        createdBy: userId,
      });

      return {
        message: EXPERIENCE.ALL_RETRIEVED,
        experiences: result.experiences,
        pagination: result.pagination,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get experience details by ID
   * @param {string} id - Experience ID
   * @param {string} userId - User ID for enrollment check
   * @returns {Promise<Object>} Experience details
   */
  getExperienceDetails: async (id, userId) => {
    try {
      const experience =
        await experienceRepository.getExperienceDetailsForAdmin(id);

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return experience;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update an experience
   * @param {string} id - Experience ID
   * @param {Object} data - Experience data to update
   * @returns {Promise<Object>} Updated experience
   */
  updateExperience: async (id, data) => {
    try {
      // Check if experience exists
      const experience = await experienceRepository.findById(id);
      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      // Update experience
      const updatedExperience = await experienceRepository.update(id, data);

      return updatedExperience;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get week details including weeklyWhy, insights and engagement stats
   * @param {string} weekId - The ID of the week
   * @param {Object} pagination - Pagination parameters
   * @param {number} pagination.page - Page number
   * @param {number} pagination.limit - Items per page
   * @returns {Promise<Object>} Week details with insights and engagement stats
   */
  getWeekDetails: async (weekId, pagination) => {
    // Get week details
    const week = await experienceRepository.findWeekById(weekId);
    if (!week) {
      throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.WEEK_NOT_FOUND);
    }

    // Always use the creator's userId
    const userId = await experienceRepository.findCreatorId(week.experienceId);

    // Get insights for the week with pagination
    const { insights, pagination: insightPagination } =
      await insightRepository.getInsightsByExperienceWeekId(
        weekId,
        pagination,
        userId,
        null
      );

    // Get engagement stats
    const engagementStats = await experienceRepository.getWeekEngagementStats(
      week.experienceId,
      userId,
      week.weekNumber
    );

    // Prepare response data
    const data = {
      weekInfo: week,
      engagementStats,
      insights,
    };

    return {
      message: EXPERIENCE.WEEK_DETAILS_RETRIEVED,
      data,
      pagination: insightPagination,
    };
  },

  /**
   * Get discussions by experience ID with pagination
   * @param {string} experienceId - Experience ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Discussions with pagination
   */
  getExperienceDiscussions: async (experienceId, page, limit) => {
    try {
      // Verify experience exists
      const experience = await experienceRepository.findById(experienceId);
      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return await experienceDiscussionRepository.findExpDiscussionsForAdmin(
        experienceId,
        page,
        limit
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get reviews by experience ID with pagination
   * @param {string} experienceId - Experience ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Reviews with pagination
   */
  getExperienceReviews: async (experienceId, page, limit) => {
    try {
      // Verify experience exists
      const experience = await experienceRepository.findById(experienceId);
      if (!experience) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_REVIEW.EXPERIENCE_NOT_FOUND
        );
      }

      return await experienceReviewRepository.findReviewsByExperienceId(
        experienceId,
        page,
        limit
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update experience status and linked insights
   * @param {string} id - Experience ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated experience
   */
  updateExperienceStatus: async (id, status) => {
    try {
      const updatedExperience =
        await experienceRepository.updateExperienceStatusWithInsights(
          id,
          status
        );

      // Update achievement progress for experience creation
      await achievementService.updateAchievementProgress(
        updatedExperience.creator.id,
        AchievementType.EXPERIENCE_CREATED
      );

      // Send notification if approved
      if (status === ExperienceStatus.APPROVED && updatedExperience.creator) {
        await experienceNotificationService.notifyExperienceApproved(
          updatedExperience
        );
      }

      return updatedExperience;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Delete an experience and all related data
   * @param {string} id - Experience ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteExperience: async (id) => {
    try {
      await experienceRepository.deleteExperience(id);
      return { message: EXPERIENCE.DELETED };
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceService;
