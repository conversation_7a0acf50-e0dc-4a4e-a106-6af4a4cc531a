/**
 * Subscription Plan Repository
 * Handles database operations for SubscriptionPlan model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { HttpStatus } = require('@utils/enums.utils');
const { SUBSCRIPTION_PLAN } = require('@utils/messages.utils');
const { Op } = require('sequelize');
const commonRepository = require('@models/repositories/common.repository');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      SubscriptionPlan: databaseService.getSubscriptionPlanModel(),
    };
  }

  _getSubscriptionPlanAttributes() {
    return {
      exclude: ['createdAt', 'updatedAt'],
    };
  }

  _getSubscriptionPlanIncludes() {
    return [];
  }
}

/**
 * Subscription Plan Repository class
 */
class SubscriptionPlanRepository extends BaseRepository {
  constructor() {
    super();
  }

  /**
   * Find subscription plan by ID
   * @param {string} id - Plan ID
   * @returns {Promise<Object>} Subscription plan
   */
  async findById(id) {
    try {
      return await this.models.SubscriptionPlan.findByPk(id, {
        ...this._getSubscriptionPlanAttributes(),
        include: this._getSubscriptionPlanIncludes(),
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION_PLAN.FIND_ERROR
      );
    }
  }

  /**
   * Find subscription plan by name
   * @param {string} name - Plan name
   * @returns {Promise<Object>} Subscription plan
   */
  async findByName(name) {
    try {
      return await this.models.SubscriptionPlan.findOne({
        where: { name },
        ...this._getSubscriptionPlanAttributes(),
        include: this._getSubscriptionPlanIncludes(),
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION_PLAN.FIND_ERROR
      );
    }
  }

  /**
   * Find subscription plan by slug
   * @param {string} slug - Plan slug
   * @returns {Promise<Object>} Subscription plan
   */
  async findBySlug(slug) {
    try {
      return await this.models.SubscriptionPlan.findOne({
        where: { slug },
        ...this._getSubscriptionPlanAttributes(),
        include: this._getSubscriptionPlanIncludes(),
      });
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION_PLAN.FIND_ERROR
      );
    }
  }

  /**
   * Create subscription plan
   * @param {Object} data - Plan data
   * @returns {Promise<Object>} Created plan
   */
  async create(data) {
    try {
      return await this.models.SubscriptionPlan.create(data);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create subscription plan with validation
   * @param {Object} data - Plan data
   * @returns {Promise<Object>} Created plan
   */
  async createPlan(data) {
    try {
      // Convert input to uppercase for validation
      const billingInterval = data.billingInterval?.toUpperCase();
      const insightsLimitPeriod = data.insightsLimitPeriod?.toUpperCase();
      const targetUserType = data.targetUserType?.toUpperCase();

      // Store in uppercase
      data.billingInterval = billingInterval;
      data.targetUserType = targetUserType;
      if (insightsLimitPeriod) {
        data.insightsLimitPeriod = insightsLimitPeriod;
      }

      const existingPlan = await this.findBySlug(data.slug);
      if (existingPlan) {
        throw new ApiException(
          HttpStatus.CONFLICT,
          SUBSCRIPTION_PLAN.ALREADY_EXISTS
        );
      }

      return await this.create(data);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update subscription plan with validation
   * @param {string} id - Plan ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated plan
   */
  async updatePlan(id, data) {
    try {
      const plan = await this.findById(id);
      if (!plan) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          SUBSCRIPTION_PLAN.NOT_FOUND
        );
      }

      // Convert input to uppercase for validation
      const billingInterval = data.billingInterval?.toUpperCase();
      const insightsLimitPeriod = data.insightsLimitPeriod?.toUpperCase();
      const targetUserType = data.targetUserType?.toUpperCase();

      // Store in uppercase
      if (billingInterval) data.billingInterval = billingInterval;
      if (targetUserType) data.targetUserType = targetUserType;
      if (insightsLimitPeriod) {
        data.insightsLimitPeriod = insightsLimitPeriod;
      }

      await plan.update(data);
      return plan;
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION_PLAN.UPDATE_ERROR
      );
    }
  }

  /**
   * Delete subscription plan
   * @param {string} id - Plan ID
   * @returns {Promise<boolean>} Success status
   */
  async delete(id) {
    try {
      const plan = await this.findById(id);
      if (!plan) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          SUBSCRIPTION_PLAN.NOT_FOUND
        );
      }

      await plan.destroy();
      return true;
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION_PLAN.DELETE_ERROR
      );
    }
  }

  /**
   * Find all subscription plans with pagination and filters
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search term
   * @param {string} options.targetUserType - Filter by target user type
   * @returns {Promise<Object>} Paginated subscription plans
   */
  async findAll({ page, limit, search, targetUserType } = {}) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);
      const where = {};

      if (search) {
        where[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } },
        ];
      }

      if (targetUserType) {
        where.targetUserType = targetUserType;
      }

      const { count, rows } =
        await this.models.SubscriptionPlan.findAndCountAll({
          where,
          ...this._getSubscriptionPlanAttributes(),
          include: this._getSubscriptionPlanIncludes(),
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        plans: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        SUBSCRIPTION_PLAN.FIND_ERROR
      );
    }
  }
}

module.exports = new SubscriptionPlanRepository();
