'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('EducatorQuestionWtdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      educatorQuestionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'EducatorQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      wtdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'WtdCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add unique constraint
    await queryInterface.addIndex(
      'EducatorQuestionWtdCategory',
      ['educatorQuestionId', 'wtdCategoryId'],
      {
        unique: true,
        name: 'educator_question_wtd_category_unique_idx',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('EducatorQuestionWtdCategory');
  },
};
