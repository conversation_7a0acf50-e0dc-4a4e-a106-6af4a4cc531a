'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('EducatorQuestionView', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      educatorQuestionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'EducatorQuestion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      ipAddress: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      userAgent: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex(
      'EducatorQuestionView',
      ['educatorQuestionId'],
      {
        name: 'educator_question_view_question_id_idx',
      }
    );
    await queryInterface.addIndex('EducatorQuestionView', ['userId'], {
      name: 'educator_question_view_user_id_idx',
    });
    await queryInterface.addIndex('EducatorQuestionView', ['createdAt'], {
      name: 'educator_question_view_created_at_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('EducatorQuestionView');
  },
};
