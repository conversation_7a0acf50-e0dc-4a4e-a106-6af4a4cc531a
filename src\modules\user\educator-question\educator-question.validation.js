const { body, query, param } = require('express-validator');

const create = [
  body('text').notEmpty().withMessage('Question text is required'),
  body('sourceUrl').optional().isString(),
  body('pdCategoryId').optional().isString(),
  body('focusIds').optional().isArray(),
  body('wtdCategoryIds').optional().isArray(),
];

const getAll = [
  query('search').optional().isString(),
  query('focusIds').optional().isArray(),
  query('wtdCategoryIds').optional().isArray(),
];

const getById = [
  param('questionId')
    .notEmpty()
    .isString()
    .withMessage('questionId is required'),
];

const questionIdParam = [
  param('questionId')
    .notEmpty()
    .isString()
    .withMessage('questionId is required'),
];

const getBookmarkedEducatorQuestions = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

module.exports = {
  create,
  getAll,
  getById,
  questionIdParam,
  getBookmarkedEducatorQuestions,
};
