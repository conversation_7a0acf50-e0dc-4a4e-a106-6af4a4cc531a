/**
 * Bookmark Service for EducatorQuestion
 * Handles business logic for bookmarking educator questions
 */
const educatorQuestionRepository = require('@models/repositories/educator-question.repository');
const { ApiException } = require('@utils/exception.utils');
const { EDUCATOR_QUESTION } = require('@utils/messages.utils');

const bookmarkService = {
  /**
   * Toggle bookmark status for an educator question
   * @param {Object} user - User object
   * @param {string} educatorQuestionId - EducatorQuestion ID
   * @returns {Promise<Object>} Object with isBookmarked status
   */
  toggleBookmark: async (user, educatorQuestionId) => {
    // 1. Check if the educator question exists
    const question = await educatorQuestionRepository.findById(
      educatorQuestionId,
      user.id
    );
    if (!question) {
      throw new ApiException(404, EDUCATOR_QUESTION.NOT_FOUND);
    }
    // 2. Toggle bookmark
    const result = await educatorQuestionRepository.toggleBookmark(
      user.id,
      educatorQuestionId
    );
    // 3. (Optional) Add achievement/notification logic here
    return result;
  },

  /**
   * Get all bookmarked educator questions for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Bookmarked educator questions and pagination info
   */
  getBookmarkedEducatorQuestions: async (userId, { page, limit }) => {
    try {
      return await educatorQuestionRepository.getBookmarkedEducatorQuestions(
        userId,
        {
          page,
          limit,
        }
      );
    } catch (error) {
      throw error;
    }
  },
};

module.exports = bookmarkService;
