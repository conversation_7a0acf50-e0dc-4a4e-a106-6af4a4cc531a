openapi: 3.0.0
info:
  title: WTD Platform User Payment Methods API
  version: 1.0.0
  description: API endpoints for managing user payment methods

paths:
  /user/payment-method/setup-intent:
    post:
      tags:
        - Payment Method
      summary: Create a setup intent
      description: Create a setup intent for adding a new payment method
      security:
        - BearerAuth: []
      responses:
        '200':
          $ref: '#/components/responses/SetupIntentCreated'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/payment-method:
    post:
      tags:
        - Payment Method
      summary: Add a new payment method
      description: Adds a new payment method for the authenticated user
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentMethodInput'
      responses:
        '201':
          $ref: '#/components/responses/PaymentMethodCreated'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '409':
          $ref: '#/components/responses/CardAlreadyExists'
        '500':
          $ref: '#/components/responses/InternalServerError'

    get:
      tags:
        - Payment Method
      summary: Get all payment methods for a user
      description: Retrieves all payment methods for the authenticated user with pagination
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          $ref: '#/components/responses/PaymentMethodList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/payment-method/default:
    get:
      tags:
        - Payment Method
      summary: Get default payment method
      description: Get the default payment method for the authenticated user
      security:
        - BearerAuth: []
      responses:
        '200':
          $ref: '#/components/responses/PaymentMethodDetail'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/payment-method/{id}:
    delete:
      tags:
        - Payment Method
      summary: Remove a payment method
      description: Remove a payment method by ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PaymentMethodIdParam'
      responses:
        '200':
          $ref: '#/components/responses/PaymentMethodDeleted'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/payment-method/{id}/default:
    patch:
      tags:
        - Payment Method
      summary: Set default payment method
      description: Set a payment method as the default for the authenticated user
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PaymentMethodIdParam'
      responses:
        '200':
          $ref: '#/components/responses/PaymentMethodSetDefault'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  parameters:
    PaymentMethodIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Payment method ID

    PageParam:
      name: page
      in: query
      description: Page number (1-based)
      required: false
      schema:
        type: integer
        minimum: 1
        default: 1

    LimitParam:
      name: limit
      in: query
      description: Number of items per page
      required: false
      schema:
        type: integer
        minimum: 1
        default: 10

  schemas:
    PaymentMethod:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Payment method ID
          example: 123e4567-e89b-12d3-a456-************
        userId:
          type: string
          format: uuid
          description: User ID
          example: 123e4567-e89b-12d3-a456-************
        stripePaymentMethodId:
          type: string
          description: Stripe payment method ID
          example: pm_1234567890
        type:
          type: string
          description: Payment method type
          example: card
        last4:
          type: string
          description: Last 4 digits of the card
          example: 4242
        brand:
          type: string
          description: Card brand
          example: visa
        expMonth:
          type: integer
          description: Card expiration month
          example: 12
        expYear:
          type: integer
          description: Card expiration year
          example: 2025
        isDefault:
          type: boolean
          description: Whether this is the default payment method
          example: false
        billingAddressStreet:
          type: string
          description: Billing address street
          example: 123 Main St
        billingAddressCity:
          type: string
          description: Billing address city
          example: New York
        billingAddressZipCode:
          type: string
          description: Billing address zip code
          example: 10001

    PaymentMethodInput:
      type: object
      required:
        - paymentMethodId
      properties:
        paymentMethodId:
          type: string
          description: Stripe payment method ID
          example: pm_1234567890
        isDefault:
          type: boolean
          description: Whether this payment method should be set as default
          example: false
        billingAddressStreet:
          type: string
          description: Billing address street
          example: 123 Main St
        billingAddressCity:
          type: string
          description: Billing address city
          example: New York
        billingAddressZipCode:
          type: string
          description: Billing address zip code
          example: 10001

    PaginationInfo:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 25
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 3

  responses:
    PaymentMethodList:
      description: Payment methods retrieved successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: Payment methods retrieved successfully
              data:
                type: array
                items:
                  $ref: '#/components/schemas/PaymentMethod'
              pagination:
                $ref: '#/components/schemas/PaginationInfo'

    PaymentMethodCreated:
      description: Payment method added successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: Payment method added successfully
              data:
                $ref: '#/components/schemas/PaymentMethod'

    PaymentMethodDetail:
      description: Payment method retrieved successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: Payment method retrieved successfully
              data:
                $ref: '#/components/schemas/PaymentMethod'

    PaymentMethodDeleted:
      description: Payment method removed successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: Payment method removed successfully

    PaymentMethodSetDefault:
      description: Payment method set as default successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: Payment method set as default successfully

    SetupIntentCreated:
      description: Setup intent created successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: Setup intent created successfully
              data:
                type: object
                properties:
                  clientSecret:
                    type: string
                    description: Client secret for the setup intent
                    example: seti_1234567890_secret_1234567890

    CardAlreadyExists:
      description: Card already exists
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    # Common responses
    SuccessResponse:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: Operation successful

    BadRequestError:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: error
              message:
                type: string
                example: Bad Request
              errors:
                type: array
                items:
                  type: object
                  properties:
                    msg:
                      type: string
                      example: Invalid field
                    param:
                      type: string
                      example: fieldName
                    location:
                      type: string
                      example: body

    UnauthorizedError:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: error
              message:
                type: string
                example: Unauthorized

    NotFoundError:
      description: Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: error
              message:
                type: string
                example: Resource not found

    InternalServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: error
              message:
                type: string
                example: Internal Server Error
              error:
                type: string
                example: Something went wrong on the server 