const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripeCustomer {
  /**
   * Create a Stripe customer
   * @param {Object} user - User object containing email, firstName, lastName, and id
   * @returns {Promise<Object>} Created Stripe customer
   */
  async create(user) {
    try {
      return await stripe.customers.create({
        email: user.email,
        name: `${user.firstName} ${user.lastName}`.trim(),
        metadata: {
          userId: user.id,
        },
      });
    } catch (error) {
      console.error('[Stripe] Failed to create customer:', error);
      throw error;
    }
  }

  /**
   * Retrieve a Stripe customer
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} Stripe customer
   */
  async get(customerId) {
    try {
      return await stripe.customers.retrieve(customerId);
    } catch (error) {
      console.error('[Stripe] Failed to retrieve customer:', error);
      throw error;
    }
  }

  /**
   * Update a Stripe customer
   * @param {string} customerId - Customer ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated Stripe customer
   */
  async update(customerId, data) {
    try {
      return await stripe.customers.update(customerId, data);
    } catch (error) {
      console.error('[Stripe] Failed to update customer:', error);
      throw error;
    }
  }

  /**
   * Delete a Stripe customer
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} Deleted Stripe customer
   */
  async delete(customerId) {
    try {
      return await stripe.customers.del(customerId);
    } catch (error) {
      console.error('[Stripe] Failed to delete customer:', error);
      throw error;
    }
  }
}

module.exports = new StripeCustomer();
