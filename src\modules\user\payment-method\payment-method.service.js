/**
 * Payment Method Service
 * Handles payment method business logic
 */
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { PAYMENT_METHOD } = require('@utils/messages.utils');
const StripePaymentMethod = require('@integrations/stripe/stripe.payment-method');
const StripeCustomer = require('@integrations/stripe/stripe.customer');
const paymentMethodRepository = require('@models/repositories/payment-method.repository');
const userRepository = require('@models/repositories/user.repository');

/**
 * Payment Method service
 */
const paymentMethodService = {
  /**
   * Create a setup intent for securely collecting payment method details
   * @param {Object} user - Authenticated user object
   * @returns {Promise<Object>} Setup intent with client secret
   */
  createSetupIntent: async (user) => {
    try {
      let stripeCustomerId = user.stripeCustomerId;

      // Create Stripe customer if it doesn't exist
      if (!stripeCustomerId) {
        const stripeCustomer = await StripeCustomer.create(user);

        // Update user with Stripe customer ID
        await userRepository.update(user.id, {
          stripeCustomerId: stripeCustomer.id,
        });

        stripeCustomerId = stripeCustomer.id;
      }

      // Create setup intent
      const setupIntent =
        await StripePaymentMethod.createSetupIntent(stripeCustomerId);
      return setupIntent;
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.CREATE_SETUP_INTENT_ERROR
      );
    }
  },

  /**
   * Add a new payment method
   * @param {Object} user - Authenticated user object
   * @param {Object} data - Payment method data
   * @returns {Promise<Object>} Created payment method
   */
  addPaymentMethod: async (user, data) => {
    try {
      const stripeCustomerId = user.stripeCustomerId;
      if (!stripeCustomerId) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          PAYMENT_METHOD.NO_STRIPE_CUSTOMER
        );
      }

      // Attach payment method to customer in Stripe
      await StripePaymentMethod.attachToCustomer(
        data.paymentMethodId,
        stripeCustomerId
      );

      // Get the full payment method details
      const paymentMethodDetails = await StripePaymentMethod.getDetails(
        data.paymentMethodId
      );

      // Check if card with same details already exists
      const cardDetails = {
        last4: paymentMethodDetails.card.last4,
        brand: paymentMethodDetails.card.brand,
        expMonth: paymentMethodDetails.card.exp_month,
        expYear: paymentMethodDetails.card.exp_year,
      };

      const isDuplicate = await paymentMethodRepository.isDuplicateCard(
        user.id,
        cardDetails
      );

      if (isDuplicate) {
        // Detach the payment method from Stripe since we won't be using it
        await StripePaymentMethod.detachAndRemove(data.paymentMethodId);
        throw new ApiException(
          HttpStatus.CONFLICT,
          PAYMENT_METHOD.CARD_ALREADY_EXISTS
        );
      }

      // Create in database
      const paymentMethod = await paymentMethodRepository.create(
        user,
        {
          ...data,
          card: cardDetails,
        },
        paymentMethodDetails
      );

      // If this is the default payment method, update in both Stripe and database
      if (paymentMethod.isDefault) {
        try {
          // First update in Stripe
          await StripePaymentMethod.setAsDefault(
            stripeCustomerId,
            paymentMethodDetails.id
          );
          console.log('Successfully set as default in Stripe');

          // Then update in database
          await paymentMethodRepository.setAsDefault(paymentMethod.id, user.id);
          console.log('Successfully set as default in database');
        } catch (error) {
          console.error('Error setting payment method as default:', error);
          // If setting as default fails, we should still return the created payment method
          // but log the error for debugging
          console.error(
            'Payment method was created but setting as default failed:',
            error
          );
        }
      }

      return paymentMethod;
    } catch (error) {
      console.error('Error creating payment method:', error);
      if (error instanceof ApiException) {
        throw error;
      }
      if (error.type === 'StripeCardError') {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          PAYMENT_METHOD.INVALID_CARD
        );
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.CREATE_ERROR
      );
    }
  },

  /**
   * Get all payment methods for a user
   * @param {Object} user - Authenticated user object
   * @param {Object} pagination - Pagination parameters
   * @param {number} pagination.page - Page number
   * @param {number} pagination.limit - Items per page
   * @returns {Promise<Object>} Object containing payment methods and pagination info
   */
  getPaymentMethods: async (user, pagination) => {
    try {
      return await paymentMethodRepository.getPaymentMethods(
        user.id,
        pagination
      );
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.FIND_ERROR
      );
    }
  },

  /**
   * Get default payment method for a user
   * @param {Object} user - Authenticated user object
   * @returns {Promise<Object>} Default payment method
   */
  getDefaultPaymentMethod: async (user) => {
    try {
      return await paymentMethodRepository.findDefaultByPaymentMethod(user.id);
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.FIND_ERROR
      );
    }
  },

  /**
   * Remove a payment method
   * @param {string} id - Payment method ID
   * @param {Object} user - Authenticated user object
   * @returns {Promise<boolean>} Success status
   */
  removePaymentMethod: async (id, user) => {
    try {
      const paymentMethod = await paymentMethodRepository.findById(id);
      if (paymentMethod.userId !== user.id) {
        throw new ApiException(HttpStatus.NOT_FOUND, PAYMENT_METHOD.NOT_FOUND);
      }

      // Detach from Stripe
      await StripePaymentMethod.detachAndRemove(
        paymentMethod.stripePaymentMethodId
      );

      // Delete from database
      await paymentMethodRepository.delete(id);

      return true;
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.DELETE_ERROR
      );
    }
  },

  /**
   * Set payment method as default
   * @param {string} id - Payment method ID
   * @param {Object} user - Authenticated user object
   * @returns {Promise<Object>} Updated payment method
   */
  setDefaultPaymentMethod: async (id, user) => {
    try {
      const paymentMethod = await paymentMethodRepository.findById(id);
      if (paymentMethod.userId !== user.id) {
        throw new ApiException(HttpStatus.NOT_FOUND, PAYMENT_METHOD.NOT_FOUND);
      }

      if (!user.stripeCustomerId) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          PAYMENT_METHOD.NO_STRIPE_CUSTOMER
        );
      }

      // Update in Stripe
      await StripePaymentMethod.setAsDefault(
        user.stripeCustomerId,
        paymentMethod.stripePaymentMethodId
      );

      // Update in database (this will also set all other cards to non-default)
      return await paymentMethodRepository.setAsDefault(id, user.id);
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        PAYMENT_METHOD.SET_DEFAULT_ERROR
      );
    }
  },
};

module.exports = paymentMethodService;
