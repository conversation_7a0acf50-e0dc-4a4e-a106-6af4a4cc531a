'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Remove the isCompleted index first
    await queryInterface.removeIndex(
      'UserMilestone',
      'user_milestone_is_completed_idx'
    );

    // Remove the columns
    await queryInterface.removeColumn('UserMilestone', 'isCompleted');
    await queryInterface.removeColumn('UserMilestone', 'completedAt');
  },

  down: async (queryInterface, Sequelize) => {
    // Add the columns back
    await queryInterface.addColumn('UserMilestone', 'isCompleted', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    });

    await queryInterface.addColumn('UserMilestone', 'completedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    // Add the index back
    await queryInterface.addIndex('UserMilestone', ['isCompleted'], {
      name: 'user_milestone_is_completed_idx',
    });
  },
};
