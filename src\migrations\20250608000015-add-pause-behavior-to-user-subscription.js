'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('UserSubscription', 'pauseBehavior', {
      type: Sequelize.STRING,
      allowNull: true, // null means not paused
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('UserSubscription', 'pauseBehavior');
  },
};
