/**
 * Follow Repository
 *
 * Handles data access operations for the Follow model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { FOLLOW } = require('@utils/messages.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { Sequelize } = require('sequelize');
const { Op } = Sequelize;

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      Follow: databaseService.getFollowModel(),
      User: databaseService.getUserModel(),
    };
  }
}

/**
 * Helper class for Follow operations
 */
class FollowHelper extends BaseRepository {
  /**
   * Validate users exist and are not the same
   */
  async validateUsers(followerId, followingId, transaction) {
    if (followerId === followingId) {
      throw new ApiException(HttpStatus.BAD_REQUEST, FOLLOW.CANNOT_FOLLOW_SELF);
    }

    const users = await this.models.User.findAll({
      where: {
        id: { [Op.in]: [followerId, followingId] },
      },
      attributes: { exclude: ['password'] },
      transaction,
    });

    if (users.length !== 2) {
      throw new ApiException(HttpStatus.NOT_FOUND, FOLLOW.NOT_FOUND);
    }

    return users;
  }

  /**
   * Handle follow/unfollow operation
   */
  async handleFollowOperation(followerId, followingId, transaction) {
    const existingFollow = await this.models.Follow.findOne({
      where: {
        followerId,
        followingId,
      },
      transaction,
    });

    if (existingFollow) {
      await existingFollow.destroy({ transaction });
      return false;
    }

    await this.models.Follow.create(
      {
        followerId,
        followingId,
      },
      { transaction }
    );
    return true;
  }
}

/**
 * Main Follow Repository class
 */
class FollowRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new FollowHelper();
  }

  /**
   * Toggle follow status for a user
   * @param {string} followerId - User ID of the follower
   * @param {string} followingId - User ID of the user being followed
   * @returns {Promise<Object>} Object containing follow status and user data
   * @throws {ApiException} If user not found or trying to follow self
   */
  async toggleFollow(followerId, followingId) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Validate users
      await this.helper.validateUsers(followerId, followingId, transaction);

      // Handle follow operation
      const isFollowing = await this.helper.handleFollowOperation(
        followerId,
        followingId,
        transaction
      );

      // Commit transaction
      await transaction.commit();

      return {
        isFollowing,
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Check if a user is following another user
   * @param {string} followerId - User ID of the follower
   * @param {string} followingId - User ID of the user being followed
   * @returns {Promise<boolean>} True if following
   */
  async isFollowing(followerId, followingId) {
    try {
      const follow = await this.models.Follow.findOne({
        where: {
          followerId,
          followingId,
        },
      });

      return !!follow;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Count new followers for a user in a date range
   * @param {string} userId - The user being followed
   * @param {Date} startDate - Start of the date range
   * @param {Date} endDate - End of the date range
   * @returns {Promise<number>} Number of new followers
   */
  async countNewFollowers(userId, startDate, endDate) {
    try {
      return await this.models.Follow.count({
        where: {
          followingId: userId,
          createdAt: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      });
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new FollowRepository();
