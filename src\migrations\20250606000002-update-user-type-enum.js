'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First, we need to create a new type with the additional value
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_User_userType" ADD VALUE IF NOT EXISTS 'PROVIDER';
    `);
  },

  async down(queryInterface, Sequelize) {
    // Note: PostgreSQL does not support removing values from ENUM types
    // We can only add new values, not remove them
    // This is a limitation of PostgreSQL
    console.log('Cannot remove ENUM values in PostgreSQL');
  },
};
