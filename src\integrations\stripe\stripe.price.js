const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripePrice {
  /**
   * Create a Stripe price
   * @param {Object} data - Price data
   * @param {string} data.product - Product ID
   * @param {number} data.unitAmount - Price in cents
   * @param {string} data.currency - Currency code
   * @param {Object} [data.recurring] - Recurring price configuration
   * @returns {Promise<Object>} Created Stripe price
   */
  async create(data) {
    try {
      return await stripe.prices.create({
        product: data.product,
        unit_amount: data.unitAmount,
        currency: data.currency,
        recurring: data.recurring,
      });
    } catch (error) {
      console.error('[Stripe] Failed to create price:', error);
      throw error;
    }
  }

  /**
   * Update a Stripe price
   * @param {string} priceId - Price ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated Stripe price
   */
  async update(priceId, data) {
    try {
      return await stripe.prices.update(priceId, data);
    } catch (error) {
      console.error('[Stripe] Failed to update price:', error);
      throw error;
    }
  }

  /**
   * List Stripe prices
   * @param {Object} options - List options
   * @returns {Promise<Object>} List of prices
   */
  async list(options = {}) {
    try {
      return await stripe.prices.list(options);
    } catch (error) {
      console.error('[Stripe] Failed to list prices:', error);
      throw error;
    }
  }
}

module.exports = new StripePrice();
