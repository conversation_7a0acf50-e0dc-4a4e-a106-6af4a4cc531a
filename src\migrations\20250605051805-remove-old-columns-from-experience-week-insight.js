'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Remove text and sourceUrl columns from ExperienceWeekInsight
    await queryInterface.removeColumn('ExperienceWeekInsight', 'text');
    await queryInterface.removeColumn('ExperienceWeekInsight', 'sourceUrl');

    // Make insightId NOT NULL and add unique constraint
    await queryInterface.changeColumn('ExperienceWeekInsight', 'insightId', {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'Insight',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });

    // Add unique constraint for experienceWeekId + insightId
    await queryInterface.addIndex(
      'ExperienceWeekInsight',
      ['experienceWeekId', 'insightId'],
      {
        unique: true,
        name: 'experience_week_insight_week_insight_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    // Remove unique constraint
    await queryInterface.removeIndex(
      'ExperienceWeekInsight',
      'experience_week_insight_week_insight_unique_idx'
    );

    // Make insightId nullable
    await queryInterface.changeColumn('ExperienceWeekInsight', 'insightId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'Insight',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });

    // Add back text and sourceUrl columns
    await queryInterface.addColumn('ExperienceWeekInsight', 'text', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('ExperienceWeekInsight', 'sourceUrl', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },
};
