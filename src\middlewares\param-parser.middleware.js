/**
 * Parameter Parser Middleware
 *
 * Middleware to parse and convert route parameters to appropriate types
 */

/**
 * Parse numeric parameters from route params
 * @param {Array<string>} numericParams - List of parameter names that should be converted to numbers
 * @returns {Function} Express middleware function
 */
const parseNumericParams = (numericParams = []) => {
  return (req, _, next) => {
    // Process each parameter that should be a number
    numericParams.forEach((param) => {
      if (req.params[param]) {
        const parsed = parseInt(req.params[param], 10);

        // Check if parsing was successful
        if (!isNaN(parsed)) {
          req.params[param] = parsed;
        }
        // If parsing failed, leave the original value (validation will catch invalid values)
      }
    });

    next();
  };
};

/**
 * Parse specific parameter types
 * @param {Object} paramConfig - Configuration object with parameter names and their types
 * @returns {Function} Express middleware function
 *
 * Example usage:
 * parseParams({
 *   weekNumber: 'number',
 *   experienceId: 'string',
 *   isActive: 'boolean'
 * })
 */
const parseParams = (paramConfig = {}) => {
  return (req, _, next) => {
    Object.keys(paramConfig).forEach((param) => {
      if (req.params[param]) {
        const type = paramConfig[param];
        const value = req.params[param];

        switch (type) {
          case 'number':
            const parsed = parseInt(value, 10);
            if (!isNaN(parsed)) {
              req.params[param] = parsed;
            }
            break;
          case 'float':
            const parsedFloat = parseFloat(value);
            if (!isNaN(parsedFloat)) {
              req.params[param] = parsedFloat;
            }
            break;
          case 'boolean':
            req.params[param] = value === 'true' || value === '1';
            break;
          case 'string':
          default:
            // Keep as string (no conversion needed)
            break;
        }
      }
    });

    next();
  };
};

module.exports = {
  parseNumericParams,
  parseParams,
};
