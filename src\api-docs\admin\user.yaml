openapi: 3.0.0
info:
  title: Admin User API
  version: 1.0.0
  description: Admin endpoints for managing users

paths:
  /admin/user:
    post:
      summary: Create a new user
      tags:
        - Admin User
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
    get:
      summary: List users
      tags:
        - Admin User
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
        - $ref: '#/components/parameters/UserTypeParam'
        - $ref: '#/components/parameters/CreatedViaParam'
        - $ref: '#/components/parameters/AccountStatusParam'
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'

  /admin/user/{userId}:
    get:
      summary: Get user by ID
      tags:
        - Admin User
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '401':
          description: Unauthorized
        '404':
          description: User not found

    put:
      summary: Update a user
      tags:
        - Admin User
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
    delete:
      summary: Delete a user
      tags:
        - Admin User
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
      responses:
        '200':
          description: User deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'


components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    UserIdParam:
      name: userId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: User ID
    PageParam:
      name: page
      in: query
      required: false
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number for pagination
    LimitParam:
      name: limit
      in: query
      required: false
      schema:
        type: integer
        minimum: 1
        default: 10
      description: Number of items per page
    SearchParam:
      name: search
      in: query
      required: false
      schema:
        type: string
      description: Search string for user name or email
    UserTypeParam:
      name: userType
      in: query
      required: false
      schema:
        type: string
      description: Filter by user type
    CreatedViaParam:
      name: createdVia
      in: query
      required: false
      schema:
        type: string
        enum: [USER, ADMIN]
      description: Filter users by registration source ('USER' for user panel, 'ADMIN' for admin panel)
    AccountStatusParam:
      name: accountStatus
      in: query
      required: false
      schema:
        type: string
        enum: [ACTIVE, ON_HOLD, DELETED]
      description: Filter users by account status

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        userType:
          type: string
          enum: [EDUCATOR, EDUCATOR_PLUS, PROVIDER, PROVIDER_PLUS]
          example: EDUCATOR
        position:
          type: string
        state:
          type: string
        country:
          type: string
        companyName:
          type: string
        focuses:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              name:
                type: string
        profilePic:
          type: string
          nullable: true
        accountStatus:
          type: string
          enum: [ACTIVE, ON_HOLD, DELETED]
          description: The current status of the user's account
      required:
        - id
        - firstName
        - lastName
        - email
        - userType
        - state
        - country
        - focuses
      example:
        id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        firstName: Alice
        lastName: Smith
        email: <EMAIL>
        userType: EDUCATOR
        position: Head of Operations
        state: New York
        country: United States
        companyName: AdminOrg
        focuses:
          - id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
            name: "Leadership"
        profilePic: null
        createdVia: USER
        accountStatus: ACTIVE
    UserResponse:
      type: object
      properties:
        message:
          type: string
        data:
          $ref: '#/components/schemas/User'
      example:
        message: User created successfully
        data:
          id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
          firstName: Alice
          lastName: Smith
          email: <EMAIL>
          userType: EDUCATOR
          position: Head of Operations
          state: New York
          country: United States
          companyName: AdminOrg
          focuses:
            - id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
              name: "Leadership"
          profilePic: null
          createdVia: USER
          accountStatus: ACTIVE
    UserListResponse:
      type: object
      properties:
        message:
          type: string
        data:
          type: object
          properties:
            users:
              type: array
              items:
                $ref: '#/components/schemas/User'
            pagination:
              $ref: '#/components/schemas/Pagination'
      example:
        message: Users retrieved successfully
        data:
          users:
            - id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
              firstName: Alice
              lastName: Smith
              email: <EMAIL>
              userType: EDUCATOR
              position: Head of Operations
              state: New York
              country: United States
              companyName: AdminOrg
              focuses:
                - id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                  name: "Leadership"
              profilePic: null
              createdVia: USER
              accountStatus: ACTIVE
          pagination:
            page: 1
            limit: 10
            total: 1
            totalPages: 1
            hasNext: false
            hasPrevious: false
    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        totalPages:
          type: integer
        hasNext:
          type: boolean
        hasPrevious:
          type: boolean
      example:
        page: 1
        limit: 10
        total: 1
        totalPages: 1
        hasNext: false
        hasPrevious: false
    CreateUserRequest:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        userType:
          type: string
          enum: [EDUCATOR, EDUCATOR_PLUS, PROVIDER, PROVIDER_PLUS]
          example: EDUCATOR
        focus:
          type: array
          items:
            type: string
            format: uuid
        state:
          type: string
        country:
          type: string
        position:
          type: string
        companyName:
          type: string
        subscriptionPlanId:
          type: string
          format: uuid
          description: Subscription plan ID
        currentPeriodStart:
          type: string
          format: date-time
          description: Subscription period start (ISO8601)
        currentPeriodEnd:
          type: string
          format: date-time
          description: Subscription period end (ISO8601)
      required:
        - firstName
        - lastName
        - email
        - userType
        - focus
        - state
        - country
      example:
        firstName: Alice
        lastName: Smith
        email: <EMAIL>
        userType: EDUCATOR
        focus: ["3fa85f64-5717-4562-b3fc-2c963f66afa6"]
        state: New York
        country: United States
        position: Head of Operations
        companyName: AdminOrg
        subscriptionPlanId: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        currentPeriodStart: "2025-06-18T00:00:00Z"
        currentPeriodEnd: "2025-09-18T00:00:00Z"
    UpdateUserRequest:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        userType:
          type: string
          enum: [EDUCATOR, EDUCATOR_PLUS, PROVIDER, PROVIDER_PLUS]
          example: EDUCATOR
        focus:
          type: array
          items:
            type: string
            format: uuid
        state:
          type: string
        country:
          type: string
        position:
          type: string
        companyName:
          type: string
        subscriptionPlanId:
          type: string
          format: uuid
          description: Subscription plan ID
        currentPeriodStart:
          type: string
          format: date-time
          description: Subscription period start (ISO8601)
        currentPeriodEnd:
          type: string
          format: date-time
          description: Subscription period end (ISO8601)
      example:
        firstName: Alicia
        lastName: Smithson
        email: <EMAIL>
        userType: EDUCATOR
        focus: ["3fa85f64-5717-4562-b3fc-2c963f66afa6"]
        state: California
        country: United States
        position: Director
        companyName: AdminOrg
        subscriptionPlanId: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        currentPeriodStart: "2025-06-18T00:00:00Z"
        currentPeriodEnd: "2025-09-18T00:00:00Z"
    SuccessResponse:
      type: object
      properties:
        message:
          type: string
      example:
        message: User deleted successfully 