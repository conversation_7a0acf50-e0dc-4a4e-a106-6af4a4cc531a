'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Milestone', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      quote: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      quoteAuthor: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      icon: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      userType: {
        type: Sequelize.ENUM(
          'EDUCATOR',
          'EDUCATOR_PLUS',
          'PROVIDER',
          'PROVIDER_PLUS'
        ),
        allowNull: false,
        defaultValue: 'EDUCATOR',
      },
      congratulationsMessage: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('Milestone', ['userType'], {
      name: 'milestone_user_type_idx',
    });

    await queryInterface.addIndex('Milestone', ['isActive'], {
      name: 'milestone_is_active_idx',
    });

    await queryInterface.addIndex('Milestone', ['order'], {
      name: 'milestone_order_idx',
    });

    // Add unique constraint
    await queryInterface.addConstraint('Milestone', {
      fields: ['name', 'userType'],
      type: 'unique',
      name: 'milestone_name_user_type_unique_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex(
      'Milestone',
      'milestone_name_user_type_unique_idx'
    );
    await queryInterface.removeIndex('Milestone', 'milestone_order_idx');
    await queryInterface.removeIndex('Milestone', 'milestone_is_active_idx');
    await queryInterface.removeIndex('Milestone', 'milestone_user_type_idx');

    // Drop the table
    await queryInterface.dropTable('Milestone');

    // Drop the enum type for userType
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_Milestone_userType";'
    );
  },
};
