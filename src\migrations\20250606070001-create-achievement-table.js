'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Achievement', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      targetValue: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },
      achievementType: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      milestoneId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Milestone',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      userType: {
        type: Sequelize.ENUM(
          'EDUCATOR',
          'EDUCATOR_PLUS',
          'PROVIDER',
          'PROVIDER_PLUS'
        ),
        allowNull: false,
        defaultValue: 'EDUCATOR',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('Achievement', ['milestoneId'], {
      name: 'achievement_milestone_id_idx',
    });

    await queryInterface.addIndex('Achievement', ['achievementType'], {
      name: 'achievement_type_idx',
    });

    await queryInterface.addIndex('Achievement', ['userType'], {
      name: 'achievement_user_type_idx',
    });

    await queryInterface.addIndex('Achievement', ['isActive'], {
      name: 'achievement_is_active_idx',
    });

    await queryInterface.addIndex('Achievement', ['order'], {
      name: 'achievement_order_idx',
    });

    // Add unique constraint
    await queryInterface.addConstraint('Achievement', {
      fields: ['name', 'milestoneId'],
      type: 'unique',
      name: 'achievement_name_milestone_unique_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex(
      'Achievement',
      'achievement_name_milestone_unique_idx'
    );
    await queryInterface.removeIndex('Achievement', 'achievement_order_idx');
    await queryInterface.removeIndex(
      'Achievement',
      'achievement_is_active_idx'
    );
    await queryInterface.removeIndex(
      'Achievement',
      'achievement_user_type_idx'
    );
    await queryInterface.removeIndex('Achievement', 'achievement_type_idx');
    await queryInterface.removeIndex(
      'Achievement',
      'achievement_milestone_id_idx'
    );

    // Drop the table
    await queryInterface.dropTable('Achievement');

    // Drop the enum type for userType
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_Achievement_userType";'
    );
  },
};
