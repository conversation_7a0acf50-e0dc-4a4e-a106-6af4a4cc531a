/**
 * LikedEducatorQuestion Model
 * Represents the many-to-many relationship between User and EducatorQuestion for likes
 */
const { Model, DataTypes } = require('sequelize');

class LikedEducatorQuestion extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        educatorQuestionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'EducatorQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'LikedEducatorQuestion',
        tableName: 'LikedEducatorQuestion',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'educatorQuestionId'],
            name: 'liked_educator_question_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // The associations are defined in the User and EducatorQuestion models
  }
}

module.exports = LikedEducatorQuestion;
