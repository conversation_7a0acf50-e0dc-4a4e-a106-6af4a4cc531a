/**
 * Payment Method Module
 *
 * This module handles payment method-related functionality for users
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const paymentMethodController = require('./payment-method.controller');
const paymentMethodValidation = require('./payment-method.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Create setup intent for securely collecting payment method details
  router.post(
    '/setup-intent',
    authenticate,
    paymentMethodController.createSetupIntent
  );

  // Add a new payment method
  router.post(
    '/',
    authenticate,
    validate(paymentMethodValidation.add),
    paymentMethodController.addPaymentMethod
  );

  // Get all payment methods
  router.get('/', authenticate, paymentMethodController.getPaymentMethods);

  // Get default payment method
  router.get(
    '/default',
    authenticate,
    paymentMethodController.getDefaultPaymentMethod
  );

  // Remove a payment method
  router.delete(
    '/:id',
    authenticate,
    validate(paymentMethodValidation.remove),
    paymentMethodController.removePaymentMethod
  );

  // Set default payment method
  router.patch(
    '/:id/default',
    authenticate,
    validate(paymentMethodValidation.setDefault),
    paymentMethodController.setDefaultPaymentMethod
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
