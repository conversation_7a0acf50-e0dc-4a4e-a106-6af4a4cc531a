openapi: 3.0.0
info:
  title: Educator Question API
  version: 1.0.0
  description: API endpoints for managing educator questions for users.

paths:
  /user/educator-question:
    post:
      tags:
        - EducatorQuestion
      summary: Create a new educator question
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EducatorQuestionCreateRequest'
      responses:
        '201':
          description: Educator question created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EducatorQuestionResponse'
        '400':
          description: Bad request
        '401':
          description: Unauthorized

    get:
      tags:
        - EducatorQuestion
      summary: Get all educator questions
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/SearchParam'
        - $ref: '#/components/parameters/FocusIdsParam'
        - $ref: '#/components/parameters/WtdCategoryIdsParam'
      responses:
        '200':
          description: List of educator questions
          content:
            application/json:
              schema:
                type: object
                properties:
                  questions:
                    type: array
                    items:
                      $ref: '#/components/schemas/EducatorQuestionResponse'
        '401':
          description: Unauthorized

  /user/educator-question/{questionId}:
    get:
      tags:
        - EducatorQuestion
      summary: Get educator question by ID
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: questionId
          required: true
          schema:
            type: string
          description: The ID of the educator question
      responses:
        '200':
          description: Educator question details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EducatorQuestionResponse'
        '404':
          description: Not found
        '401':
          description: Unauthorized

  /user/educator-question/{questionId}/like:
    post:
      tags:
        - EducatorQuestion
      summary: Toggle Like Status
      description: Like or unlike an educator question
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/QuestionIdParam'
      responses:
        '200':
          description: Successfully toggled like status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LikeToggleResponse'

  /user/educator-question/{questionId}/bookmark:
    post:
      tags:
        - EducatorQuestion
      summary: Toggle Bookmark Status
      description: Bookmark or unbookmark an educator question
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/QuestionIdParam'
      responses:
        '200':
          description: Successfully toggled bookmark status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookmarkToggleResponse'

  /user/educator-question/{questionId}/implement:
    post:
      tags:
        - EducatorQuestion
      summary: Toggle Implement Status
      description: Mark or unmark an educator question as implemented
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/QuestionIdParam'
      responses:
        '200':
          description: Successfully toggled implement status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImplementToggleResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    SearchParam:
      in: query
      name: search
      schema:
        type: string
      description: Search text for questions
    FocusIdsParam:
      in: query
      name: focusIds
      schema:
        type: array
        items:
          type: string
      style: form
      explode: false
      description: Filter by focus IDs
    WtdCategoryIdsParam:
      in: query
      name: wtdCategoryIds
      schema:
        type: array
        items:
          type: string
      style: form
      explode: false
      description: Filter by WTD category IDs
    QuestionIdParam:
      in: path
      name: questionId
      required: true
      schema:
        type: string
      description: The ID of the educator question
  schemas:
    EducatorQuestionCreateRequest:
      type: object
      required:
        - text
      properties:
        text:
          type: string
          example: "What is the best way to engage students in online learning?"
        sourceUrl:
          type: string
          example: "https://example.com/teaching-strategies"
        pdCategoryId:
          type: string
          example: "abc123-pdcat"
        focusIds:
          type: array
          items:
            type: string
          example: ["focus1", "focus2"]
        wtdCategoryIds:
          type: array
          items:
            type: string
          example: ["wtdcat1", "wtdcat2"]
    EducatorQuestionResponse:
      type: object
      properties:
        id:
          type: string
          example: "edq-12345"
        text:
          type: string
          example: "What is the best way to engage students in online learning?"
        sourceUrl:
          type: string
          example: "https://example.com/teaching-strategies"
        pdCategoryId:
          type: string
          example: "abc123-pdcat"
        createdBy:
          type: string
          example: "user-123"
        isBookmarked:
          type: boolean
          example: false
        isLiked:
          type: boolean
          example: true
        likesCount:
          type: integer
          example: 5
        bookmarksCount:
          type: integer
          example: 2
        createdAt:
          type: string
          format: date-time
          example: "2025-07-09T12:00:00.000Z"
        updatedAt:
          type: string
          format: date-time
          example: "2025-07-09T12:00:00.000Z"
        focus:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: "focus1"
              name:
                type: string
                example: "Student Engagement"
        wtdCategories:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: "wtdcat1"
              name:
                type: string
                example: "Online Learning"
        creator:
          type: object
          properties:
            id:
              type: string
              example: "user-123"
            firstName:
              type: string
              example: "John"
            lastName:
              type: string
              example: "Doe"
            profilePic:
              type: string
              example: "https://example.com/profile.jpg"
            userType:
              type: string
              example: "educator"
    LikeToggleResponse:
      type: object
      properties:
        isLiked:
          type: boolean
          description: Whether the educator question is liked by the current user
    BookmarkToggleResponse:
      type: object
      properties:
        isBookmarked:
          type: boolean
          description: Whether the educator question is bookmarked by the current user
    ImplementToggleResponse:
      type: object
      properties:
        isImplemented:
          type: boolean
          description: Whether the educator question is implemented by the current user
