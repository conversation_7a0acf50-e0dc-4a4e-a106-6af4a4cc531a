const router = require('express').Router();
const basicAuth = require('express-basic-auth');
const admin = require('./admin');
const users = require('./users');

// Default credentials if environment variables are not set
const swaggerUser = process.env.SWAGGER_DOCS_USER || 'api_docs';
const swaggerPassword = process.env.SWAGGER_DOCS_PASSWORD || 'ApiDocs@2025';

// Check if running on localhost
const isLocalhost = process.env.NODE_ENV === 'local';

// Basic auth middleware - only apply if not on localhost
const basicAuthMiddleware = isLocalhost ? (req, res, next) => next() : basicAuth({
  users: {
    [swaggerUser]: swaggerPassword,
  },
  challenge: true,
});

router.use(
  '/admin',
  basicAuthMiddleware,
  admin
);

router.use(
  '/user',
  basicAuthMiddleware,
  users
);

module.exports = router;
