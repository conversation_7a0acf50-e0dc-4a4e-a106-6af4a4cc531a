'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('AchievementQuestion', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      achievementId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Achievement',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      userType: {
        type: Sequelize.ENUM('EDUCATOR', 'PROVIDER'),
        allowNull: false,
      },
      question: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      questionType: {
        type: Sequelize.ENUM('MULTIPLE_CHOICE', 'TEXT'),
        allowNull: false,
        defaultValue: 'TEXT',
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      isRequired: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
    await queryInterface.addIndex('AchievementQuestion', ['achievementId'], {
      name: 'achievement_question_achievement_id_idx',
    });
    await queryInterface.addIndex('AchievementQuestion', ['userType'], {
      name: 'achievement_question_user_type_idx',
    });
    await queryInterface.addIndex('AchievementQuestion', ['isActive'], {
      name: 'achievement_question_is_active_idx',
    });
    await queryInterface.addIndex('AchievementQuestion', ['order'], {
      name: 'achievement_question_order_idx',
    });
    await queryInterface.addIndex('AchievementQuestion', ['questionType'], {
      name: 'achievement_question_type_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'AchievementQuestion',
      'achievement_question_type_idx'
    );
    await queryInterface.removeIndex(
      'AchievementQuestion',
      'achievement_question_order_idx'
    );
    await queryInterface.removeIndex(
      'AchievementQuestion',
      'achievement_question_is_active_idx'
    );
    await queryInterface.removeIndex(
      'AchievementQuestion',
      'achievement_question_user_type_idx'
    );
    await queryInterface.removeIndex(
      'AchievementQuestion',
      'achievement_question_achievement_id_idx'
    );
    await queryInterface.dropTable('AchievementQuestion');
  },
};
