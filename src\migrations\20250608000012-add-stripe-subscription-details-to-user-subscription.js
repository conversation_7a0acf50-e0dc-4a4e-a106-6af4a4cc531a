const { DataTypes } = require('sequelize');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'UserSubscription',
      'stripeSubscriptionDetails',
      {
        type: DataTypes.JSONB,
        allowNull: true,
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      'UserSubscription',
      'stripeSubscriptionDetails'
    );
  },
};
