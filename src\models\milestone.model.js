/**
 * Milestone Model
 * Represents milestones that can be achieved by users
 */
const { Model, DataTypes } = require('sequelize');
const { UserType } = require('@utils/enums.utils');

class Milestone extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        milestoneIdentifier: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          validate: {
            notEmpty: true,
            isUppercase: true,
          },
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        quote: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        quoteAuthor: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        icon: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        userType: {
          type: DataTypes.ENUM(...UserType.values),
          allowNull: false,
          defaultValue: UserType.EDUCATOR,
          field: 'userType',
          references: {
            model: 'enum_User_userType',
            key: 'enum_User_userType',
          },
        },
        congratulationsMessage: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'Milestone',
        tableName: 'Milestone',
        timestamps: true,
        indexes: [
          {
            fields: ['userType'],
            name: 'milestone_user_type_idx',
          },
          {
            fields: ['isActive'],
            name: 'milestone_is_active_idx',
          },
          {
            fields: ['order'],
            name: 'milestone_order_idx',
          },
          {
            unique: true,
            fields: ['name', 'userType'],
            name: 'milestone_name_user_type_unique_idx',
          },
          {
            unique: true,
            fields: ['milestoneIdentifier'],
            name: 'milestone_identifier_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Has many achievements
    Milestone.hasMany(models.Achievement, {
      foreignKey: 'milestoneId',
      as: 'achievements',
      onDelete: 'CASCADE',
    });

    // Has many user milestones
    Milestone.hasMany(models.UserMilestone, {
      foreignKey: 'milestoneId',
      as: 'userMilestones',
      onDelete: 'CASCADE',
    });

    // Has many milestone questions
    Milestone.hasMany(models.MilestoneQuestion, {
      foreignKey: 'milestoneId',
      as: 'questions',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = Milestone;
