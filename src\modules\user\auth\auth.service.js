/**
 * User Auth Service
 *
 * Contains business logic for user authentication
 */
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { ApiException } = require('@utils/exception.utils');
const userRepository = require('@models/repositories/user.repository');
const { AUTH: AUTH_MESSAGES } = require('@utils/messages.utils');
const {
  UserRole,
  UserType,
  HttpStatus,
  SubscriptionStatus,
  AccountStatus,
} = require('@utils/enums.utils');
const { AUTH: AUTH_CONSTANTS } = require('@utils/constants');
const followRepository = require('@models/repositories/follow.repository');
const achievementService = require('@modules/user/experience/services/achievement.service');
const { AchievementType } = require('@utils/enums.utils');
const StripeCustomer = require('@integrations/stripe/stripe.customer');
const sendGridService = require('@integrations/sendgrid/sendgrid.service');
const subscriptionService = require('../subscription/subscription.service');
const { USER: USER_MESSAGES } = require('@utils/messages.utils');
const ImpactRepository = require('@repositories/impact.repository');
const subscriptionPlanRepository = require('@models/repositories/subscription-plan.repository');
const { SubscriptionPlanSlug } = require('@utils/enums.utils');

/**
 * User auth service
 */
const authService = {
  /**
   * Generate JWT token
   * @param {Object} payload - Data to be included in token
   * @returns {string} JWT token
   */
  generateToken: (payload) => {
    try {
      return jwt.sign(payload, process.env.JWT_SECRET);
    } catch (error) {
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        AUTH_MESSAGES.TOKEN_GENERATION_FAILED
      );
    }
  },

  /**
   * Verify JWT token
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  verifyToken: (token) => {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      throw new ApiException(
        HttpStatus.UNAUTHORIZED,
        AUTH_MESSAGES.INVALID_TOKEN
      );
    }
  },

  /**
   * Register a new user
   * @param {Object} requestBody - User registration data
   * @returns {Object} User data and token
   */
  registerUser: async (requestBody) => {
    const { email, userType } = requestBody;
    try {
      // Check if user already exists
      const existingUser = await userRepository.findByEmail(email);
      if (existingUser) {
        throw new ApiException(
          HttpStatus.CONFLICT,
          AUTH_MESSAGES.EMAIL_ALREADY_EXISTS
        );
      }

      // Validate user type
      if (!UserType.isValid(userType)) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          AUTH_MESSAGES.INVALID_USER_TYPE
        );
      }

      // Create user with focus associations
      const user = await userRepository.createUserWithFocus({
        ...requestBody,
      });

      // Create Stripe customer
      const customer = await StripeCustomer.create(user);

      // Update user with Stripe customer ID
      await userRepository.update(user.id, { stripeCustomerId: customer.id });
      user.stripeCustomerId = customer.id;

      // Generate token
      const freshUser = await userRepository.findByEmail(user.email);
      const token = jwt.sign(
        {
          id: freshUser.id,
          email: freshUser.email,
          userType: freshUser.userType,
          role: UserRole.USER,
          tokenVersion: freshUser.tokenVersion,
        },
        process.env.JWT_SECRET
      );

      // Return user data (without password)
      return {
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          userType: user.userType,
          position: user.position,
          state: user.state,
          country: user.country,
          companyName: user.companyName,
          focuses: user.focuses,
          role: UserRole.USER,
          deviceToken: user.deviceToken,
          deviceType: user.deviceType,
          currentStep: user.currentStep,
          signupToken: user.signupToken,
        },
        token,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Ensure educator user has a free subscription if none is active
   * @param {Object} user - User object
   * @param {boolean} hasActiveSubscription - Whether the user has an active subscription
   * @returns {Promise<{hasActiveSubscription: boolean, activeSubscription: object}>}
   */
  async ensureEducatorHasFreeSubscription(user, hasActiveSubscription) {
    if (
      (user.userType === UserType.EDUCATOR ||
        user.userType === UserType.EDUCATOR_PLUS) &&
      !hasActiveSubscription
    ) {
      // Get the free plan by slug
      const freePlan = await subscriptionPlanRepository.findBySlug(
        SubscriptionPlanSlug.EDUCATOR_FREE.toLowerCase()
      );
      if (freePlan) {
        // Create the free subscription
        await subscriptionService.createSubscription(user, {
          planId: freePlan.id,
        });

        // Update userType to EDUCATOR
        await userRepository.update(user.id, { userType: UserType.EDUCATOR });
        await userRepository.findById(user.id);

        // Refresh active subscription info
        const activeSubscription =
          await subscriptionService.getActiveSubscription(user.id);
        const hasActive =
          !!activeSubscription &&
          (activeSubscription.status === SubscriptionStatus.ACTIVE ||
            activeSubscription.status === SubscriptionStatus.TRIALING);
        return { hasActiveSubscription: hasActive, activeSubscription };
      }
    }
    // If no changes, return original values
    const activeSubscription = await subscriptionService.getActiveSubscription(
      user.id
    );
    return { hasActiveSubscription, activeSubscription };
  },

  /**
   * Login user
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {string} deviceToken - Device token
   * @param {string} deviceType - Device type
   * @returns {Object} User data and token
   */
  loginUser: async (email, password, deviceToken, deviceType) => {
    try {
      // Find user
      const user = await userRepository.findByEmail(email);

      if (!user) {
        throw new ApiException(
          HttpStatus.UNAUTHORIZED,
          AUTH_MESSAGES.EMAIL_NOT_FOUND
        );
      }

      // Verify password
      const isPasswordValid = await user.verifyPassword(password);

      if (!isPasswordValid) {
        throw new ApiException(
          HttpStatus.UNAUTHORIZED,
          AUTH_MESSAGES.INVALID_CREDENTIALS
        );
      }

      // Check account status
      if (user.accountStatus === AccountStatus.ON_HOLD) {
        throw new ApiException(
          HttpStatus.FORBIDDEN,
          AUTH_MESSAGES.ACCOUNT_ON_HOLD
        );
      }

      if (user.accountStatus === AccountStatus.DELETED) {
        throw new ApiException(
          HttpStatus.FORBIDDEN,
          AUTH_MESSAGES.ACCOUNT_DELETED
        );
      }

      // Update deviceToken and deviceType if provided
      if (deviceToken || deviceType) {
        await userRepository.update(user.id, {
          ...(deviceToken && { deviceToken }),
          ...(deviceType && { deviceType }),
        });
      }

      // Check for active subscription
      let hasActiveSubscription = false;
      let activeSubscription = await subscriptionService.getActiveSubscription(
        user.id
      );

      hasActiveSubscription =
        !!activeSubscription &&
        (activeSubscription.status === SubscriptionStatus.ACTIVE ||
          activeSubscription.status === SubscriptionStatus.TRIALING);

      // --- AUTO FREE SUBSCRIPTION LOGIC ---
      ({ hasActiveSubscription, activeSubscription } =
        await authService.ensureEducatorHasFreeSubscription(
          user,
          hasActiveSubscription
        ));

      // Generate token (without expiration)
      const freshUser = await userRepository.findByEmail(user.email);
      const token = jwt.sign(
        {
          id: freshUser.id,
          email: freshUser.email,
          userType: freshUser.userType,
          role: UserRole.USER,
          tokenVersion: freshUser.tokenVersion,
        },
        process.env.JWT_SECRET
      );

      const { currentMilestone, nextMilestone } =
        await ImpactRepository.getMilestonesWithProgress(freshUser);
      // Return user data with token and subscription status
      return {
        id: freshUser.id,
        firstName: freshUser.firstName,
        lastName: freshUser.lastName,
        email: freshUser.email,
        userType: freshUser.userType,
        position: freshUser.position,
        focus: freshUser.focus,
        state: freshUser.state,
        country: freshUser.country,
        companyName: freshUser.companyName,
        profilePic: freshUser.profilePic,
        role: UserRole.USER,
        token: token,
        hasActiveSubscription,
        activeSubscription: activeSubscription
          ? {
              id: activeSubscription.id,
              status: activeSubscription.status,
              currentPeriodStart: activeSubscription.currentPeriodStart,
              currentPeriodEnd: activeSubscription.currentPeriodEnd,
              plan: activeSubscription.plan,
            }
          : null,
        deviceToken: freshUser.deviceToken,
        deviceType: freshUser.deviceType,
        currentStep: freshUser.currentStep,
        signupToken: freshUser.signupToken,
        currentMilestone,
        nextMilestone,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get user profile
   * @param {string} userId - User ID
   * @returns {Object} User profile data
   */
  getUserProfile: async (userId) => {
    try {
      const profile = await userRepository.getUserProfileData(userId);

      // Check for active subscription
      let hasActiveSubscription = false;
      const activeSubscription =
        await subscriptionService.getActiveSubscription(userId);
      hasActiveSubscription =
        !!activeSubscription &&
        (activeSubscription.status === SubscriptionStatus.ACTIVE ||
          activeSubscription.status === SubscriptionStatus.TRIALING);

      return {
        ...profile,
        hasActiveSubscription,
        activeSubscription: activeSubscription
          ? {
              id: activeSubscription.id,
              status: activeSubscription.status,
              currentPeriodStart: activeSubscription.currentPeriodStart,
              currentPeriodEnd: activeSubscription.currentPeriodEnd,
              plan: activeSubscription.plan,
            }
          : null,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get another user's profile with following status
   * @param {string} userId - ID of the user whose profile to retrieve
   * @param {string} currentUserId - ID of the current user
   * @returns {Object} User profile data with following status
   */
  getOtherUserProfile: async (userId, currentUserId) => {
    try {
      // Get user profile data
      const user = await userRepository.getUserProfileData(userId);

      // Check if current user is following this user
      const isFollowing = await followRepository.isFollowing(
        currentUserId,
        userId
      );

      // Add following status to profile data
      return {
        ...user,
        isFollowing,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} profileData - Updated profile data
   * @returns {Object} Updated user profile data
   */
  updateUserProfile: async (userId, profileData) => {
    try {
      // Get current profile
      const currentProfile = await userRepository.getUserProfileData(userId);

      // Update user profile using repository
      const updatedProfile = await userRepository.updateProfile(
        userId,
        profileData
      );

      // If profile picture is being updated, trigger achievement
      if (
        profileData.profilePic &&
        (!currentProfile?.profilePic ||
          currentProfile.profilePic !== profileData.profilePic)
      ) {
        await achievementService.updateAchievementProgress(
          userId,
          AchievementType.PROFILE_PICTURE_UPDATE
        );
      }

      // If all ice breaker questions are being answered, trigger achievement
      if (profileData.iceBreakers) {
        const allIceBreakersAnswered = Object.values(
          profileData.iceBreakers
        ).every((answer) => {
          if (answer === null || answer === undefined) return false;
          if (typeof answer === 'string') return answer.trim() !== '';
          if (typeof answer === 'object' && answer !== null) {
            return answer.value !== undefined && answer.value !== null;
          }
          return true;
        });

        // Trigger achievement if all questions are answered
        if (allIceBreakersAnswered) {
          console.log('Triggering ICE_BREAKER_ANSWERED achievement');
          await achievementService.updateAchievementProgress(
            userId,
            AchievementType.ICE_BREAKER_ANSWERED
          );
        }
      }

      return updatedProfile;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Change user password
   * @param {string} userId - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<void>}
   */
  changePassword: async (userId, currentPassword, newPassword) => {
    try {
      // Find user
      const user = await userRepository.findById(userId);
      if (!user) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          AUTH_MESSAGES.USER_NOT_FOUND
        );
      }

      // Verify current password
      const isPasswordValid = await user.verifyPassword(currentPassword);
      if (!isPasswordValid) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          AUTH_MESSAGES.INVALID_CURRENT_PASSWORD
        );
      }

      // Update password
      await userRepository.changePassword(userId, newPassword);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Handle forgot password request
   * @param {string} email - User email
   * @returns {Promise<void>}
   */
  handleForgotPassword: async (email) => {
    try {
      // Find user by email
      const user = await userRepository.findByEmail(email);

      if (!user) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          AUTH_MESSAGES.EMAIL_NOT_FOUND
        );
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetTokenExpiry = new Date(
        Date.now() + AUTH_CONSTANTS.RESET_TOKEN_EXPIRY_MS
      );

      // Save reset token to user
      await userRepository.update(user.id, {
        resetToken,
        resetTokenExpiry,
      });

      // Generate reset URL
      const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

      // Send reset password email
      await sendGridService.sendForgotPasswordEmail(email, {
        USER_NAME: `${user.firstName} ${user.lastName}`,
        RESET_URL: resetUrl,
      });
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        AUTH_MESSAGES.FORGOT_PASSWORD_FAILED
      );
    }
  },

  /**
   * Reset user password
   * @param {string} token - Reset token
   * @param {string} newPassword - New password
   * @returns {Promise<void>}
   */
  resetPassword: async (token, newPassword) => {
    try {
      // Find user by reset token
      const user = await userRepository.findByResetToken(token);
      if (!user) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          AUTH_MESSAGES.INVALID_RESET_TOKEN
        );
      }

      // Check if token is expired
      const tokenAge = Date.now() - user.resetTokenExpiry.getTime();

      if (tokenAge > AUTH_CONSTANTS.RESET_TOKEN_EXPIRY_MS) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          AUTH_MESSAGES.RESET_TOKEN_EXPIRED
        );
      }

      // Update password using changePassword method
      await userRepository.changePassword(user.id, newPassword);

      // Clear reset token
      await userRepository.update(user.id, {
        resetToken: null,
        resetTokenExpiry: null,
      });
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        AUTH_MESSAGES.PASSWORD_RESET_FAILED
      );
    }
  },

  /**
   * Request account deletion (puts account on hold)
   * @param {string} userId - User ID
   */
  requestAccountDeletion: async (userId) => {
    try {
      const user = await userRepository.findById(userId);
      if (!user) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          USER_MESSAGES.USER_NOT_FOUND
        );
      }
      if (user.accountStatus === AccountStatus.ON_HOLD) {
        throw new ApiException(
          HttpStatus.CONFLICT,
          USER_MESSAGES.ACCOUNT_ALREADY_ON_HOLD
        );
      }
      await userRepository.updateAccountStatus(userId, AccountStatus.ON_HOLD);
      // Also increment tokenVersion and clear deviceToken/deviceType
      await userRepository.update(userId, {
        tokenVersion: user.tokenVersion + 1,
        deviceToken: null,
        deviceType: null,
      });
      return true;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Logout user by incrementing tokenVersion
   * @param {string} userId - User ID
   */
  logoutUser: async (userId) => {
    const user = await userRepository.findById(userId);
    if (!user)
      throw new ApiException(
        HttpStatus.NOT_FOUND,
        AUTH_MESSAGES.EMAIL_NOT_FOUND
      );
    await userRepository.update(userId, {
      tokenVersion: user.tokenVersion + 1,
      deviceToken: null,
      deviceType: null,
    });
    return true;
  },
};

module.exports = authService;
