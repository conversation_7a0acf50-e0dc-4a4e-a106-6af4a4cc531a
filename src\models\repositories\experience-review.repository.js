/**
 * Experience Review Repository
 *
 * This repository handles experience review-related database operations
 */
const databaseService = require('@config/database.config');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE_REVIEW } = require('@utils/messages.utils');
const commonRepository = require('./common.repository');

/**
 * Experience review repository methods
 */
class ExperienceReviewRepository {
  constructor() {
    this.models = {
      ExperienceReview: databaseService.getExperienceReviewModel(),
      User: databaseService.getUserModel(),
    };
  }

  /**
   * Find review by experience and user
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Review object or null if not found
   */
  async findByExperienceAndUser(experienceId, userId) {
    return this.models.ExperienceReview.findOne({
      where: { experienceId, userId },
    });
  }

  /**
   * Create a new review
   * @param {Object} data - Review data
   * @returns {Promise<Object>} Created review
   */
  async create(data) {
    try {
      return await this.models.ExperienceReview.create(data);
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw new ApiException(
          HttpStatus.CONFLICT,
          EXPERIENCE_REVIEW.ALREADY_REVIEWED
        );
      }
      throw error;
    }
  }

  /**
   * Find reviews by experience ID with pagination
   * @param {string} experienceId - Experience ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Reviews with pagination
   */
  async findReviewsByExperienceId(experienceId, page, limit) {
    const offset = commonRepository.calculateOffset(page, limit);

    const { count, rows } = await this.models.ExperienceReview.findAndCountAll({
      where: { experienceId },
      include: [
        {
          model: this.models.User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePic'],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });

    return {
      data: rows,
      pagination: commonRepository.buildPaginationInfo(count, page, limit),
    };
  }
}

module.exports = new ExperienceReviewRepository();
