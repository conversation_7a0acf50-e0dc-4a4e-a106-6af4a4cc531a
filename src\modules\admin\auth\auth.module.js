/**
 * Admin Auth Module
 *
 * This module handles admin authentication-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const authController = require('./auth.controller');
const authValidation = require('./auth.validation');

/**
 * Register routes
 */
function registerRoutes() {
  router.post('/login', validate(authValidation.login), authController.login);

  router.get('/profile', authenticate, authController.getProfile);

  // Admin logout endpoint
  router.post('/logout', authenticate, authController.logout);

  return router;
}

// Export the router
module.exports = registerRoutes();
