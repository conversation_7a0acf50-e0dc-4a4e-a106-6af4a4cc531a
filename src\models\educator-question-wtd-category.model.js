/**
 * EducatorQuestionWtdCategory Model
 * Represents the many-to-many relationship between EducatorQuestion and WtdCategory
 */
const { Model, DataTypes } = require('sequelize');

class EducatorQuestionWtdCategory extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        educatorQuestionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'EducatorQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        wtdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'WtdCategory',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'EducatorQuestionWtdCategory',
        tableName: 'EducatorQuestionWtdCategory',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['educatorQuestionId', 'wtdCategoryId'],
            name: 'educator_question_wtd_category_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
  }
}

module.exports = EducatorQuestionWtdCategory;
