'use strict';
const { UserType } = require('../utils/enums.utils');
const { v4: uuidv4 } = require('uuid');

/**
 * Milestone Seeder — Idempotent, with unique global order
 * EDUCATOR, EDUCATOR_PLUS, PROVIDER, and PROVIDER_PLUS milestone entries (order unique per table).
 */
module.exports = {
  async up(queryInterface) {
    // Move milestone base arrays inside the function to generate fresh UUIDs each run
    const educatorMilestonesBase = [
      {
        name: 'WTD Beginner',
        milestoneIdentifier: 'EDUCATOR_WTD_BEGINNER',
        description:
          "Being a WTD Beginner means you've taken the first bold step your Whole Teacher journey. It's about showing up with curiosity, courage and commitment—knowing that consistency will turn small starts into powerful growth. We're excited to have you as part of our community—this is just the beginning!",
        quote:
          "You don't have to see the whole staircase, just take the first step.",
        quoteAuthor: '<PERSON>, Jr.',
        icon: 'uploads/beginner_1749206444798.svg',
        isActive: true,
        congratulationsMessage: null,
      },
      {
        name: 'WTD Contributor',
        milestoneIdentifier: 'EDUCATOR_WTD_CONTRIBUTOR',
        description:
          'The Contributor Badge celebrates those who bring meaningful insights to the department. Their contributions play a vital role in uplifting other educators, supporting providers, and overall, building a community of honesty, vulnerability, and reflection.',
        quote: 'Courage starts with showing up and letting ourselves be seen.',
        quoteAuthor: 'Brené Brown',
        icon: 'uploads/contributor_1749206584080.svg',
        isActive: true,
        congratulationsMessage:
          'Congratulations on earning the Contributor Badge! Your thoughtful contributions and willingness to share have made our department a more open and honest place.',
      },
      {
        name: 'WTD Reflector',
        milestoneIdentifier: 'EDUCATOR_WTD_REFLECTOR',
        description:
          "The Reflector Badge celebrates taking the time to think things through, observe what works (and what doesn't), and share thoughtful feedback. Your insights truly make a difference to our WTD community.",
        quote:
          'We do not learn from experience... we learn from reflecting on experience.',
        quoteAuthor: 'John Dewey',
        icon: 'uploads/reflector_1749206628038.svg',
        isActive: true,
        congratulationsMessage:
          'Congratulations on earning the Reflector Badge! This is one of the most important aspects of WTD—educators are encouraged to reflect on whether insights shared are right for their life, their classroom, or their school.',
      },
      {
        name: 'WTD Encourager',
        milestoneIdentifier: 'EDUCATOR_WTD_ENCOURAGER',
        description:
          "The Encourager badge is for those who make other members of the WTD community feel supported and motivated—while also spreading these good vibes to their coworkers and students. You're a person who's always cheering others on and spreading positivity, even when times are tough.",
        quote:
          'What you do makes a difference, and you have to decide what kind of difference you want to make.',
        quoteAuthor: 'Jane Goodall',
        icon: 'uploads/encourager_1749206673803.svg',
        isActive: true,
        congratulationsMessage:
          "Congratulations on earning the Encourager Badge! There's no better legacy to leave than one of support, encouragement, and optimism. Your warmth and energy are felt in your classroom, your home, your school, and here on this platform.",
      },
      {
        name: 'WTD Catalyst',
        milestoneIdentifier: 'EDUCATOR_WTD_CATALYST',
        description:
          "Catalysts don't just talk about change—they take action and inspire others to do the same. Earning this badge requires a deep commitment to growth. By engaging in an immersive WTD Experience, expanding their community, and using their personal and professional experience to impact others, Catalysts ignite change and transformation inside and outside the classroom.",
        quote: 'Be the change that you wish to see in the world.',
        quoteAuthor: 'Mahatma Gandhi',
        icon: 'uploads/catalyst_1749206710286.svg',
        isActive: true,
        congratulationsMessage:
          "What an accomplishment it is to earn the Catalyst Badge! Your school, WTD, and the education system needs people like you—someone who is willing to put the work in to make change happen. It's an honor to have you in this community.",
      },
      {
        name: 'WTD Visionary',
        milestoneIdentifier: 'EDUCATOR_WTD_VISIONARY',
        description:
          "A visionary often sees possibilities, opportunities, or solutions that others may not, and they're driven to bring those ideas to life. Their willingness to visualize a change in your classroom can ignite long-term satisfaction, but the biggest indicator of impact is whether they take the first step. Earning this badge means they have brought their vision to life!",
        quote: "It always seems impossible until it's done.",
        quoteAuthor: 'Nelson Mandela',
        icon: 'uploads/visionary_1749206744566.svg',
        isActive: true,
        congratulationsMessage:
          "You did it! You have officially earned the Visionary Badge! Congratulations! It's an honor to present you with this badge—you are taking the steps to make real change in your school. Through the lens of a Visionary, you will continue to make decisions, brainstorm solutions, and take the steps required to transform the future of education.",
      },
      {
        name: 'WTD Innovator',
        milestoneIdentifier: 'EDUCATOR_WTD_INNOVATOR',
        description:
          "Innovators don't just generate new ideas—they actively reflect on how those ideas impact their teaching and their students' learning. They refine, adapt, and build on what they've tried, creating a continuous cycle of improvement. Innovators embrace challenges, make intentional changes, and use those experiences to shape their future steps, always striving to create a more engaging, effective, and inclusive classroom.",
        quote: 'The best way to predict the future is to create it.',
        quoteAuthor: 'Abraham Lincoln',
        icon: 'uploads/innovator_1749206786846.svg',
        isActive: true,
        congratulationsMessage:
          "Presenting you with…the Innovator Badge! Wear this proudly. Your ability to think outside the box is admirable. Every single school and every single student deserve an educator like you. Continue dreaming big, Innovator. We're honored to be a part of your journey.",
      },
      {
        name: 'WTD Trailblazer',
        milestoneIdentifier: 'EDUCATOR_WTD_TRAILBLAZER',
        description:
          "Trailblazers don't just make an impact in their own classroom—they lead the charge in making the entire school a better place. By implementing a thoughtful initiative to enhance the school environment, they are inspiring others, spreading their energy, and creating a positive ripple effect that touches everyone. As a Trailblazer, their actions are sparking change that uplifts the entire school community and sets the foundation for future progress.",
        quote:
          'If you want to fly, you have to give up the things that weigh you down.',
        quoteAuthor: 'Toni Morrison',
        icon: 'uploads/trailblazer_1749206819095.svg',
        isActive: true,
        congratulationsMessage:
          "YOU DID IT! Wow! Congrats on earning your EIGHTH badge! On behalf of the WTD community, it's an honor to learn and grow alongside you. You are an inspiration—taking action to make a change is not easy, nor is it for the faint of heart. Your determination, bravery, and perspective have changed your school for the better. What an accomplishment. Savor this, Trailblazer, and never stop fighting for the change that you, your coworkers, and your students deserve.",
      },
    ];

    // Provider milestones (9-16 offset), Provider Plus (17-24 offset), Ed Plus (25-32 offset)
    function milestoneGroup(ms, userType, orderOffset) {
      return ms.map((m, i) => {
        // Get the base identifier without any prefix
        const baseIdentifier = m.milestoneIdentifier
          .split('_')
          .slice(1)
          .join('_');

        // Create the appropriate prefix based on user type
        let prefix;
        switch (userType) {
          case UserType.EDUCATOR:
            prefix = 'EDUCATOR';
            break;
          case UserType.EDUCATOR_PLUS:
            prefix = 'EDUCATOR_PLUS';
            break;
          case UserType.PROVIDER:
            prefix = 'PROVIDER';
            break;
          case UserType.PROVIDER_PLUS:
            prefix = 'PROVIDER_PLUS';
            break;
          default:
            throw new Error(`Unknown user type: ${userType}`);
        }

        return {
          ...m,
          userType: userType,
          order: i + 1 + orderOffset,
          milestoneIdentifier: `${prefix}_${baseIdentifier}`,
        };
      });
    }

    const educatorMilestones = milestoneGroup(
      educatorMilestonesBase,
      UserType.EDUCATOR,
      0
    );
    const educatorPlusMilestones = milestoneGroup(
      educatorMilestonesBase,
      UserType.EDUCATOR_PLUS,
      24
    );

    const providerMilestonesBase = [
      {
        name: 'WTD Beginner',
        milestoneIdentifier: 'PROVIDER_WTD_BEGINNER',
        description:
          "Being a WTD Beginner means you've taken the first bold step your Whole Teacher journey. It's about showing up for educators with excitement, generosity, and commitment. We're excited to have you as part of our community—this is just the beginning!",
        quote:
          "You don't have to see the whole staircase, just take the first step.",
        quoteAuthor: 'Martin Luther King, Jr.',
        icon: 'uploads/beginner_1749206444798.svg',
        isActive: true,
        congratulationsMessage: null,
      },
      {
        name: 'WTD Contributor',
        milestoneIdentifier: 'PROVIDER_WTD_CONTRIBUTOR',
        description:
          'The Contributor Badge celebrates those who bring meaningful insights to the department. Their contributions play a vital role in uplifting educators, supporting other providers, and overall, building a community of honesty, vulnerability, and reflection.',
        quote: 'Courage starts with showing up and letting ourselves be seen.',
        quoteAuthor: 'Brené Brown',
        icon: 'uploads/contributor_1749206584080.svg',
        isActive: true,
        congratulationsMessage:
          'Congratulations on earning the Contributor Badge! Your thoughtful contributions and willingness to share have made our department a more open and honest place.',
      },
      {
        name: 'WTD Reflector',
        milestoneIdentifier: 'PROVIDER_WTD_REFLECTOR',
        description:
          "The Reflector Badge celebrates taking the time to think things through, observe what works (and what doesn't), and share thoughtful feedback. Your insights truly make a difference to our WTD community.",
        quote:
          'We do not learn from experience... we learn from reflecting on experience.',
        quoteAuthor: 'John Dewey',
        icon: 'uploads/reflector_1749206628038.svg',
        isActive: true,
        congratulationsMessage:
          "Congratulations on earning the Reflector Badge! This badge represents one of the most important aspects of WTD: as educators reflect and grow, so do providers. Providers not only reflect on their own expertise by posting insights, but also engage with other providers' contributions by liking, contributing, and bookmarking. Doing so inspires our entire community to grow.",
      },
      {
        name: 'WTD Encourager',
        milestoneIdentifier: 'PROVIDER_WTD_ENCOURAGER',
        description:
          "The Encourager badge is for those who make other members of the WTD community feel supported and motivated—while also spreading these good vibes to those in their own company or personal life. You're a provider who's always cheering others on and spreading positivity, even when times are tough.",
        quote:
          'What you do makes a difference, and you have to decide what kind of difference you want to make.',
        quoteAuthor: 'Jane Goodall',
        icon: 'uploads/encourager_1749206673803.svg',
        isActive: true,
        congratulationsMessage:
          "Congratulations on earning the Encourager Badge! There's no better legacy to leave than one of support, encouragement, and optimism. Your warmth and energy are felt in your presence on this platform.",
      },
      {
        name: 'WTD Catalyst',
        milestoneIdentifier: 'PROVIDER_WTD_CATALYST',
        description:
          "Catalysts don't just talk about change—they take action and inspire others to do the same. Earning this badge requires a deep commitment to growth. By creating an immersive WTD Experience and continuing to use their professional expertise to impact others, Catalysts ignite change and transformation for educators across the country.",
        quote: 'Be the change that you wish to see in the world.',
        quoteAuthor: 'Mahatma Gandhi',
        icon: 'uploads/catalyst_1749206710286.svg',
        isActive: true,
        congratulationsMessage:
          "Your school, WTD, and the education system as a whole need providers like you—those willing to put in the work to make change happen. It's an honor to have you in this community.",
      },
      {
        name: 'WTD Visionary',
        milestoneIdentifier: 'PROVIDER_WTD_VISIONARY',
        description:
          "A visionary often sees possibilities, opportunities, or solutions that others may not, and they're driven to bring those ideas to life. Through the lens of a Visionary, you offer the support required to transform the future of education.",
        quote: "It always seems impossible until it's done.",
        quoteAuthor: 'Nelson Mandela',
        icon: 'uploads/visionary_1749206744566.svg',
        isActive: true,
        congratulationsMessage:
          "You did it! You have officially earned the Visionary Badge! Congratulations! It's an honor to present you with this badge—you are taking the steps to make real change in the educators' lives.",
      },
      {
        name: 'WTD Innovator',
        milestoneIdentifier: 'PROVIDER_WTD_INNOVATOR',
        description:
          "Innovators don't just generate new ideas—they actively reflect on how those ideas impact educators and students. They refine, adapt, and build on what they've tried, creating a continuous cycle of improvement. Innovators embrace challenges, make intentional changes, and use those experiences to shape their future steps, always striving to create a more engaging, effective, and inclusive company.",
        quote: 'The best way to predict the future is to create it.',
        quoteAuthor: 'Abraham Lincoln',
        icon: 'uploads/innovator_1749206786846.svg',
        isActive: true,
        congratulationsMessage:
          "Presenting you with…the Innovator Badge! Wear this proudly. Your ability to think outside the box is admirable. Every educator deserves a supporter like you. Continue dreaming big, Innovator. We're honored to be a part of your journey.",
      },
      {
        name: 'WTD Trailblazer',
        milestoneIdentifier: 'PROVIDER_WTD_TRAILBLAZER',
        description:
          "Trailblazers don't just make an impact on educators—they lead the charge in making the entire school system a better place. By implementing a thoughtful initiative to enhance school environments, they are inspiring others, sharing their energy, and creating a positive ripple effect that touches everyone. As a Trailblazer, their actions are sparking change that uplifts the entire school community and sets the foundation for future progress.",
        quote:
          'If you want to fly, you have to give up the things that weigh you down.',
        quoteAuthor: 'Toni Morrison',
        icon: 'uploads/trailblazer_1749206819095.svg',
        isActive: true,
        congratulationsMessage:
          "YOU DID IT! Wow! Congrats on earning your EIGHTH badge! On behalf of the WTD community, it's an honor to learn and grow alongside you. You are an inspiration—taking action to make a change is not easy, nor is it for the faint of heart. Your determination, bravery, and perspective have changed education for the better. What an accomplishment. Savor this, Trailblazer, and never stop fighting for the change that educators and students deserve.",
      },
    ];

    const providerMilestones = milestoneGroup(
      providerMilestonesBase,
      UserType.PROVIDER,
      8
    );
    const providerPlusMilestones = milestoneGroup(
      providerMilestonesBase,
      UserType.PROVIDER_PLUS,
      16
    );

    // All milestones
    const allMilestones = [
      ...educatorMilestones,
      ...educatorPlusMilestones,
      ...providerMilestones,
      ...providerPlusMilestones,
    ];

    // Get existing milestones using parameterized query
    const existing = await queryInterface.sequelize.query(
      `SELECT "id", "name", "userType", "milestoneIdentifier" FROM "Milestone" WHERE "name" IN (:names) AND "userType" IN (:types)`,
      {
        replacements: {
          names: allMilestones.map((m) => m.name),
          types: [
            UserType.EDUCATOR,
            UserType.EDUCATOR_PLUS,
            UserType.PROVIDER,
            UserType.PROVIDER_PLUS,
          ],
        },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      }
    );

    // Create a map of existing milestones by name and userType
    const existingMap = new Map(
      existing.map((m) => [`${m.name}-${m.userType}`, m])
    );

    // Separate milestones into new and existing
    const newMilestones = [];
    const existingMilestones = [];

    allMilestones.forEach((milestone) => {
      const key = `${milestone.name}-${milestone.userType}`;
      if (existingMap.has(key)) {
        existingMilestones.push({
          ...milestone,
          id: existingMap.get(key).id,
        });
      } else {
        newMilestones.push({
          ...milestone,
          id: uuidv4(),
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
    });

    // Insert new milestones
    if (newMilestones.length > 0) {
      await queryInterface.bulkInsert('Milestone', newMilestones);
      console.log(`Inserted ${newMilestones.length} new milestones`);
    }

    // Update existing milestones
    for (const milestone of existingMilestones) {
      await queryInterface.sequelize.query(
        `UPDATE "Milestone" 
         SET "milestoneIdentifier" = :milestoneIdentifier,
             "description" = :description,
             "quote" = :quote,
             "quoteAuthor" = :quoteAuthor,
             "icon" = :icon,
             "isActive" = :isActive,
             "congratulationsMessage" = :congratulationsMessage,
             "updatedAt" = NOW()
         WHERE "id" = :id`,
        {
          replacements: {
            id: milestone.id,
            milestoneIdentifier: milestone.milestoneIdentifier,
            description: milestone.description,
            quote: milestone.quote,
            quoteAuthor: milestone.quoteAuthor,
            icon: milestone.icon,
            isActive: milestone.isActive,
            congratulationsMessage: milestone.congratulationsMessage,
          },
        }
      );
    }
    console.log(`Updated ${existingMilestones.length} existing milestones`);
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete('Milestone', null, {});
  },
};
