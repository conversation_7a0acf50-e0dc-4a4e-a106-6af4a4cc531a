/**
 * Admin User Module
 * Registers admin user CRUD routes
 */
const express = require('express');
const userController = require('./user.controller');
const userValidation = require('./user.validation');
const { validate } = require('@middlewares/validation.middleware');
const paginationMiddleware = require('@middlewares/pagination.middleware');

function registerRoutes() {
  const router = express.Router();

  // Create user
  router.post(
    '/',
    validate(userValidation.createUser),
    userController.createUser
  );

  // Update user
  router.put(
    '/:userId',
    validate(userValidation.updateUser),
    userController.updateUser
  );

  // List users with pagination
  router.get(
    '/',
    paginationMiddleware,
    validate(userValidation.listUsers),
    userController.listUsers
  );

  // Delete user
  router.delete(
    '/:userId',
    validate(userValidation.deleteUser),
    userController.deleteUser
  );

  // Get a particular user
  router.get(
    '/:userId',
    validate(userValidation.deleteUser), // param validation for userId
    userController.getUserByUserId
  );

  return router;
}

module.exports = registerRoutes();
