openapi: 3.0.0
info:
  title: WTD Platform Onboarding Config API
  version: 1.0.0
  description: API endpoints for retrieving onboarding screen configurations for users.

paths:
  /user/onboarding-config:
    get:
      tags:
        - Onboarding Config
      summary: Get Onboarding Config for User
      description: >
        Get the onboarding configuration specific to the authenticated user's type.
        The userType is automatically determined from the authenticated user's profile.
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successfully retrieved onboarding config.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /user/onboarding-config/current-step:
    patch:
      tags:
        - Onboarding Config
      summary: Update Current Step
      description: Update the current onboarding step for the authenticated user.
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCurrentStepRequest'
      responses:
        '200':
          description: Current step updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCurrentStepResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    UserType:
      type: string
      enum:
        - EDUCATOR
        - EDUCATOR_PLUS
        - PROVIDER
        - PROVIDER_PLUS
      description: The type of user.

    OnboardingConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
        imageUrl:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        userType:
          $ref: '#/components/schemas/UserType'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    OnboardingConfigResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Onboarding config retrieved successfully.
        data:
          $ref: '#/components/schemas/OnboardingConfig'

    UpdateCurrentStepRequest:
      type: object
      properties:
        currentStep:
          type: integer
          description: The new current onboarding step.
      required:
        - currentStep

    UpdateCurrentStepResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            id:
              type: string
              format: uuid
            currentStep:
              type: integer

  responses:
    Unauthorized:
      description: Unauthorized - JWT is missing or invalid.
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 401
              error:
                type: string
                example: Unauthorized
              message:
                type: string
                example: Authentication required

    NotFound:
      description: Not Found - The requested resource was not found.
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: integer
                example: 404
              error:
                type: string
                example: Not Found
              message:
                type: string
                example: Onboarding config not found for your user type