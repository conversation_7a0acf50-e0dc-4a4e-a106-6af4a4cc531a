/**
 * UserAchievementResponse Model
 * Tracks user responses to achievement questions
 */
const { Model, DataTypes } = require('sequelize');

class UserAchievementResponse extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        questionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'AchievementQuestion',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        response: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        selectedOptionId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'AchievementQuestionOption',
            key: 'id',
          },
          onDelete: 'SET NULL',
        },
      },
      {
        sequelize,
        modelName: 'UserAchievementResponse',
        tableName: 'UserAchievementResponse',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'questionId'],
            name: 'user_achievement_response_unique_idx',
          },
          {
            fields: ['userId'],
            name: 'user_achievement_response_user_id_idx',
          },
          {
            fields: ['questionId'],
            name: 'user_achievement_response_question_id_idx',
          },
          {
            fields: ['selectedOptionId'],
            name: 'user_achievement_response_option_id_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to user
    UserAchievementResponse.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    // Belongs to achievement question
    UserAchievementResponse.belongsTo(models.AchievementQuestion, {
      foreignKey: 'questionId',
      as: 'question',
      onDelete: 'CASCADE',
    });

    // Belongs to selected option (if any)
    UserAchievementResponse.belongsTo(models.AchievementQuestionOption, {
      foreignKey: 'selectedOptionId',
      as: 'selectedOption',
      onDelete: 'SET NULL',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = UserAchievementResponse;
