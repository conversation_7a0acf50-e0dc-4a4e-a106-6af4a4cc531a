'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('User', 'currentStep', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('User', 'signupToken', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('User', 'currentStep');
    await queryInterface.removeColumn('User', 'signupToken');
  },
};
