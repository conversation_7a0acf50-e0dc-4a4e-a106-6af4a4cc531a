'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Drop the junction tables as they're no longer needed
    await queryInterface.dropTable('ExperienceWeekInsightFocus');
    await queryInterface.dropTable('ExperienceWeekInsightPdCategory');
    await queryInterface.dropTable('ExperienceWeekInsightWtdCategory');
  },

  async down(queryInterface, Sequelize) {
    // Recreate the junction tables if migration is rolled back
    await queryInterface.createTable('ExperienceWeekInsightFocus', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      experienceWeekInsightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeekInsight',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      focusId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Focus',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.createTable('ExperienceWeekInsightPdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      experienceWeekInsightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeekInsight',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      pdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'PdCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.createTable('ExperienceWeekInsightWtdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      experienceWeekInsightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeekInsight',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      wtdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'WtdCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
    });
  },
};
