/**
 * Migration: Update Experience table with missing fields
 */
'use strict';

const { ExperienceStatus } = require('../utils/enums.utils');

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add status enum type if it doesn't exist
    try {
      await queryInterface.sequelize.query(
        `DO $$
        BEGIN
          IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_experience_status') THEN
            CREATE TYPE "enum_experience_status" AS ENUM('PENDING', 'APPROVED', 'REJECTED', 'DRAFT');
          END IF;
        END
        $$;`
      );
    } catch (error) {
      console.log('Enum type might already exist, continuing migration');
    }

    // Add missing columns to Experience table
    await queryInterface.addColumn('Experience', 'status', {
      type: Sequelize.ENUM(...ExperienceStatus.values),
      allowNull: false,
      defaultValue: 'PENDING',
    });

    await queryInterface.addColumn('Experience', 'reviewedBy', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'Admin',
        key: 'id',
      },
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('Experience', 'reviewedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    await queryInterface.addColumn('Experience', 'rejectionReason', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    // Add index for the reviewedBy column
    await queryInterface.addIndex('Experience', ['reviewedBy'], {
      name: 'experience_reviewed_by_idx',
    });

    // Add index for status column for faster filtering
    await queryInterface.addIndex('Experience', ['status'], {
      name: 'experience_status_idx',
    });

  },

  async down(queryInterface, Sequelize) {
    // Drop indexes first
    await queryInterface.removeIndex('Experience', 'experience_status_idx');
    await queryInterface.removeIndex('Experience', 'experience_reviewed_by_idx');

    // Remove columns
    await queryInterface.removeColumn('Experience', 'rejectionReason');
    await queryInterface.removeColumn('Experience', 'reviewedAt');
    await queryInterface.removeColumn('Experience', 'reviewedBy');
    await queryInterface.removeColumn('Experience', 'status');

    // We don't drop the enum type as it might be used elsewhere
  },
};
