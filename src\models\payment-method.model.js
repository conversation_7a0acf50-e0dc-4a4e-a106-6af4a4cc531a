/**
 * PaymentMethod Model
 * Represents user's payment methods stored in Stripe
 */
const { Model, DataTypes } = require('sequelize');

class PaymentMethod extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
        },
        stripePaymentMethodId: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
        },
        type: {
          type: DataTypes.STRING, // card, bank_account, etc.
          allowNull: false,
        },
        last4: {
          type: DataTypes.STRING(4),
          allowNull: true,
        },
        brand: {
          type: DataTypes.STRING, // visa, mastercard, etc.
          allowNull: true,
        },
        expMonth: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        expYear: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        isDefault: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: true,
        },
        billingAddressStreet: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        billingAddressCity: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        billingAddressZipCode: {
          type: DataTypes.STRING,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'PaymentMethod',
        tableName: 'PaymentMethod',
        timestamps: true,
        indexes: [
          {
            fields: ['userId'],
            name: 'payment_method_user_id_idx',
          },
          {
            fields: ['isDefault'],
            name: 'payment_method_is_default_idx',
          },
          {
            fields: ['stripePaymentMethodId'],
            name: 'payment_method_stripe_id_idx',
            unique: true,
          },
        ],
      }
    );
  }

  static associate(models) {
    PaymentMethod.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    PaymentMethod.hasMany(models.UserSubscription, {
      foreignKey: 'paymentMethodId',
      as: 'subscriptions',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = PaymentMethod;
