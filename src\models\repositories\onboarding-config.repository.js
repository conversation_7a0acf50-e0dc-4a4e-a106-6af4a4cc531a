/**
 * OnboardingConfig Repository
 * Handles database operations for onboarding config
 */
const { ApiException } = require('@utils/exception.utils');
const { PAGINATION } = require('@utils/constants');
const databaseService = require('@config/database.config');
const commonRepository = require('./common.repository');
const { ONBOARDING_CONFIG } = require('@utils/messages.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { Op } = require('sequelize');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      OnboardingConfig: databaseService.getOnboardingConfigModel(),
    };
  }
}

/**
 * Main OnboardingConfig Repository class
 */
class OnboardingConfigRepository extends BaseRepository {
  constructor() {
    super();
  }

  /**
   * Find all onboarding configs with pagination
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Search query
   * @param {Object} options.where - Where clause for filtering
   * @returns {Promise<Object>} Object containing configs and pagination info
   */
  async findAll({
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    where = {},
  } = {}) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.model.findAndCountAll({
        where,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        configs: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find onboarding config by ID
   * @param {string} id - Onboarding config UUID
   * @returns {Promise<Object>} Onboarding config instance
   * @throws {ApiException} If config not found
   */
  async findById(id) {
    try {
      const config = await this.models.OnboardingConfig.findByPk(id);

      if (!config) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          ONBOARDING_CONFIG.NOT_FOUND
        );
      }

      return config;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Create a new onboarding config
   * @param {Object} data - Raw request body data
   * @returns {Promise<Object>} Created onboarding config
   * @throws {ApiException} If config already exists for the userType
   */
  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const { title, description, imageUrl, userType } = data;

      // Check if config already exists for this userType
      const existingConfig = await this.models.OnboardingConfig.findOne({
        where: { userType },
        transaction,
      });

      if (existingConfig) {
        throw new ApiException(
          HttpStatus.CONFLICT,
          ONBOARDING_CONFIG.ALREADY_EXISTS(userType)
        );
      }

      const config = await this.models.OnboardingConfig.create(
        {
          title,
          description,
          imageUrl,
          userType,
        },
        { transaction }
      );

      await transaction.commit();
      return await this.findById(config.id);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update onboarding config
   * @param {string} id - Onboarding config UUID
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated onboarding config
   * @throws {ApiException} If config not found or userType already exists
   */
  async update(id, data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const config = await this.findById(id);

      // If userType is being updated, check for duplicates
      if (data.userType && data.userType !== config.userType) {
        const existingConfig = await this.models.OnboardingConfig.findOne({
          where: {
            userType: data.userType,
            id: { [Op.ne]: id },
          },
          transaction,
        });

        if (existingConfig) {
          throw new ApiException(
            HttpStatus.CONFLICT,
            ONBOARDING_CONFIG.ALREADY_EXISTS(data.userType)
          );
        }
      }

      Object.assign(config, data);
      await config.save({ transaction });

      await transaction.commit();
      return await this.findById(config.id);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete onboarding config
   * @param {string} id - Onboarding config UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If config not found
   */
  async delete(id) {
    try {
      const config = await this.findById(id);
      await config.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }

  /**
   * Find all onboarding configs with pagination and filtering
   * @param {Object} query - Request query parameters
   * @param {Object} pagination - Pagination parameters
   * @returns {Promise<Object>} Onboarding configs and pagination info
   */
  async fetchOnboardingConfigs(query, pagination) {
    try {
      const { search = '', userType } = query;
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
      } = pagination;

      const where = {};

      if (userType) {
        where.userType = userType;
      }

      if (search) {
        where[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } =
        await this.models.OnboardingConfig.findAndCountAll({
          where,
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        configs: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in fetchOnboardingConfigs repository:', error);
      throw error;
    }
  }

  /**
   * Find onboarding config by user type
   * @param {string} userType - User type
   * @returns {Promise<Object>} Onboarding config instance
   * @throws {ApiException} If config not found
   */
  async findByUserType(userType) {
    try {
      const config = await this.models.OnboardingConfig.findOne({
        where: { userType },
      });

      if (!config) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          ONBOARDING_CONFIG.NOT_FOUND
        );
      }

      return config;
    } catch (error) {
      console.error('Error in findByUserType repository:', error);
      throw error;
    }
  }
}

module.exports = new OnboardingConfigRepository();
