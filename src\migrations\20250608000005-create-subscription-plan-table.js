/**
 * Migration to create SubscriptionPlan table
 */
const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('SubscriptionPlan', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      targetUserType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      stripeProductId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      stripePriceId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      priceCents: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      billingInterval: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      trialDays: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      canRegisterExperiences: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      insightsLimit: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      insightsLimitPeriod: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex('SubscriptionPlan', ['targetUserType'], {
      name: 'subscription_plan_target_user_type_idx',
    });
    await queryInterface.addIndex('SubscriptionPlan', ['isActive'], {
      name: 'subscription_plan_is_active_idx',
    });
    await queryInterface.addIndex('SubscriptionPlan', ['billingInterval'], {
      name: 'subscription_plan_billing_interval_idx',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Drop indexes first
    await queryInterface.removeIndex(
      'SubscriptionPlan',
      'subscription_plan_target_user_type_idx'
    );
    await queryInterface.removeIndex(
      'SubscriptionPlan',
      'subscription_plan_is_active_idx'
    );
    await queryInterface.removeIndex(
      'SubscriptionPlan',
      'subscription_plan_billing_interval_idx'
    );

    // Then drop the table
    await queryInterface.dropTable('SubscriptionPlan');
  },
};
