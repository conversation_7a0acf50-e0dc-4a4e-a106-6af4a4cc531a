/**
 * User Auth Module
 *
 * This module handles user authentication-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const authController = require('./auth.controller');
const authValidation = require('./auth.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // User registration endpoint
  router.post(
    '/register',
    validate(authValidation.register),
    authController.register
  );

  // User login endpoint
  router.post('/login', validate(authValidation.login), authController.login);

  // User profile endpoints
  router.get('/profile', authenticate, authController.getProfile);

  router.get(
    '/profile/:userId',
    authenticate,
    validate(authValidation.getOtherUserProfile),
    authController.getOtherUserProfile
  );

  router.put(
    '/profile',
    authenticate,
    validate(authValidation.updateProfile),
    authController.updateProfile
  );

  // Change password endpoint
  router.patch(
    '/change-password',
    authenticate,
    validate(authValidation.changePassword),
    authController.changePassword
  );

  // Forgot password endpoint
  router.post(
    '/forgot-password',
    validate(authValidation.forgotPassword),
    authController.forgotPassword
  );

  // Reset password endpoint
  router.patch(
    '/reset-password',
    validate(authValidation.resetPassword),
    authController.resetPassword
  );

  // Request account deletion endpoint
  router.patch('/delete', authenticate, authController.requestAccountDeletion);

  // User logout endpoint
  router.post('/logout', authenticate, authController.logout);

  return router;
}

// Export the router
module.exports = registerRoutes();
