/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('PaymentMethod', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      stripePaymentMethodId: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      last4: {
        type: Sequelize.STRING(4),
        allowNull: true,
      },
      brand: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      expMonth: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      expYear: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      isDefault: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      billingAddressStreet: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      billingAddressCity: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      billingAddressZipCode: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex('PaymentMethod', ['userId'], {
      name: 'payment_method_user_id_idx',
    });
    await queryInterface.addIndex('PaymentMethod', ['isDefault'], {
      name: 'payment_method_is_default_idx',
    });
    await queryInterface.addIndex('PaymentMethod', ['stripePaymentMethodId'], {
      name: 'payment_method_stripe_id_idx',
      unique: true,
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex(
      'PaymentMethod',
      'payment_method_user_id_idx'
    );
    await queryInterface.removeIndex(
      'PaymentMethod',
      'payment_method_is_default_idx'
    );
    await queryInterface.removeIndex(
      'PaymentMethod',
      'payment_method_stripe_id_idx'
    );

    // Then drop the table
    await queryInterface.dropTable('PaymentMethod');
  },
};
