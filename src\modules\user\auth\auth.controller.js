/**
 * User Auth Controller
 *
 * Handles user authentication-related HTTP requests
 */
const authService = require('./auth.service');
const { ApiResponse } = require('@utils/response.utils');
const { AUTH, USER } = require('@utils/messages.utils');

/**
 * User auth controller
 */
const authController = {
  /**
   * Register a new user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  register: async (req, res, next) => {
    try {
      // Register user using the auth service
      const { user, token } = await authService.registerUser(req.body);

      // Return user data
      return ApiResponse.created(res, AUTH.REGISTER_SUCCESS, {
        user,
        token,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Login user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  login: async (req, res, next) => {
    try {
      const { email, password, deviceToken, deviceType } = req.body;

      // Login user using the auth service
      const user = await authService.loginUser(
        email,
        password,
        deviceToken,
        deviceType
      );

      return ApiResponse.success(res, AUTH.LOGIN_SUCCESS, user);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get current user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getProfile: async (req, res, next) => {
    try {
      const userId = req.user.id;

      // Get user profile using the auth service
      const user = await authService.getUserProfile(userId);

      return ApiResponse.success(res, AUTH.PROFILE_RETRIEVED, user);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get another user's profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getOtherUserProfile: async (req, res, next) => {
    try {
      const { userId } = req.params;
      const currentUserId = req.user.id;

      // Get user profile using the auth service
      const user = await authService.getOtherUserProfile(userId, currentUserId);

      return ApiResponse.success(res, AUTH.PROFILE_RETRIEVED, user);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateProfile: async (req, res, next) => {
    try {
      const userId = req.user.id;
      const profileData = req.body;

      // Update user profile using the auth service
      const updatedUser = await authService.updateUserProfile(
        userId,
        profileData
      );

      return ApiResponse.success(res, USER.PROFILE_UPDATED, updatedUser);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Change user password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  changePassword: async (req, res, next) => {
    try {
      const userId = req.user.id;
      const { currentPassword, newPassword } = req.body;

      // Change password using the auth service
      await authService.changePassword(userId, currentPassword, newPassword);

      return ApiResponse.success(res, AUTH.PASSWORD_CHANGE_SUCCESS);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Handle forgot password request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  forgotPassword: async (req, res, next) => {
    try {
      const { email } = req.body;
      await authService.handleForgotPassword(email);
      return ApiResponse.success(res, AUTH.FORGOT_PASSWORD_EMAIL_SENT);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Handle reset password request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  resetPassword: async (req, res, next) => {
    try {
      const { token, newPassword } = req.body;
      await authService.resetPassword(token, newPassword);
      return ApiResponse.success(res, AUTH.PASSWORD_RESET_SUCCESS);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Request account deletion (puts account on hold)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  requestAccountDeletion: async (req, res, next) => {
    try {
      const userId = req.user.id;
      await authService.requestAccountDeletion(userId);
      return ApiResponse.success(res, USER.ACCOUNT_DELETION_REQUESTED);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Logout user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  logout: async (req, res, next) => {
    try {
      await authService.logoutUser(req.user.id);
      return ApiResponse.success(res, AUTH.LOGOUT_SUCCESS);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = authController;
