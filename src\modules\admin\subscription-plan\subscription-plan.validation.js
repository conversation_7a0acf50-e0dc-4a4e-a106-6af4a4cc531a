/**
 * Admin Subscription Plan Validation
 *
 * Validation schemas for subscription plan-related endpoints
 */
const { body, param, query } = require('express-validator');
const {
  SubscriptionPlanBillingInterval,
  SubscriptionPlanInsightsLimitPeriod,
  UserType,
} = require('@utils/enums.utils');

/**
 * Validation schemas for subscription plan endpoints
 */
const subscriptionPlanValidation = {
  /**
   * Validate seed subscription plans request
   */
  seed: [
    // No validation needed for seeding as it's a protected admin endpoint
  ],

  /**
   * Validate get all subscription plans request
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
    query('targetUserType')
      .optional()
      .isIn([UserType.EDUCATOR, UserType.PROVIDER])
      .withMessage('Target user type must be either EDUCATOR or PROVIDER'),
  ],

  /**
   * Validate get subscription plan by ID request
   */
  getById: [param('id').isUUID(4).withMessage('Invalid subscription plan ID')],

  /**
   * Validate create subscription plan request
   */
  create: [
    body('name')
      .isString()
      .notEmpty()
      .withMessage('Name is required')
      .isLength({ min: 3 })
      .withMessage('Name must be at least 3 characters'),
    body('description')
      .isString()
      .notEmpty()
      .withMessage('Description is required')
      .isLength({ min: 10 })
      .withMessage('Description must be at least 10 characters'),
    body('priceCents')
      .isInt({ min: 0 })
      .withMessage('Price cents must be a non-negative integer'),
    body('billingInterval')
      .isIn(Object.values(SubscriptionPlanBillingInterval))
      .withMessage('Invalid billing interval'),
    body('targetUserType')
      .isIn([UserType.EDUCATOR, UserType.PROVIDER])
      .withMessage('Target user type must be either EDUCATOR or PROVIDER'),
    body('trialDays')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Trial days must be a non-negative integer'),
    body('canRegisterExperiences')
      .isBoolean()
      .withMessage('canRegisterExperiences must be a boolean'),
    body('insightsLimit')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Insights limit must be a non-negative integer'),
    body('insightsLimitPeriod')
      .optional()
      .isIn(Object.values(SubscriptionPlanInsightsLimitPeriod))
      .withMessage('Invalid insights limit period'),
    body('isActive').isBoolean().withMessage('isActive must be a boolean'),
  ],

  /**
   * Validate update subscription plan request
   */
  update: [
    param('id').isUUID(4).withMessage('Invalid subscription plan ID'),
    body('name')
      .optional()
      .isString()
      .notEmpty()
      .withMessage('Name cannot be empty')
      .isLength({ min: 3 })
      .withMessage('Name must be at least 3 characters'),
    body('description')
      .optional()
      .isString()
      .notEmpty()
      .withMessage('Description cannot be empty')
      .isLength({ min: 10 })
      .withMessage('Description must be at least 10 characters'),
    body('priceCents')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Price cents must be a non-negative integer'),
    body('billingInterval')
      .optional()
      .isIn(Object.values(SubscriptionPlanBillingInterval))
      .withMessage('Invalid billing interval'),
    body('targetUserType')
      .optional()
      .isIn([UserType.EDUCATOR, UserType.PROVIDER])
      .withMessage('Target user type must be either EDUCATOR or PROVIDER'),
    body('trialDays')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Trial days must be a non-negative integer'),
    body('canRegisterExperiences')
      .optional()
      .isBoolean()
      .withMessage('canRegisterExperiences must be a boolean'),
    body('insightsLimit')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Insights limit must be a non-negative integer'),
    body('insightsLimitPeriod')
      .optional()
      .isIn(Object.values(SubscriptionPlanInsightsLimitPeriod))
      .withMessage('Invalid insights limit period'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('isActive must be a boolean'),
  ],

  /**
   * Validate delete subscription plan request
   */
  delete: [param('id').isUUID(4).withMessage('Invalid subscription plan ID')],
};

module.exports = subscriptionPlanValidation;
