const { DataTypes } = require('sequelize');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn(
      'UserSubscription',
      'currentPeriodStart',
      {
        type: DataTypes.DATE,
        allowNull: true,
      }
    );
    await queryInterface.changeColumn('UserSubscription', 'currentPeriodEnd', {
      type: DataTypes.DATE,
      allowNull: true,
    });
    await queryInterface.changeColumn('UserSubscription', 'cancelAtPeriodEnd', {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    });
  },

  async down(queryInterface, Sequelize) {
    // Revert changes if needed
    await queryInterface.changeColumn(
      'UserSubscription',
      'currentPeriodStart',
      {
        type: DataTypes.DATE,
        allowNull: false,
      }
    );
    await queryInterface.changeColumn('UserSubscription', 'currentPeriodEnd', {
      type: DataTypes.DATE,
      allowNull: false,
    });
    await queryInterface.changeColumn('UserSubscription', 'cancelAtPeriodEnd', {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
  },
};
